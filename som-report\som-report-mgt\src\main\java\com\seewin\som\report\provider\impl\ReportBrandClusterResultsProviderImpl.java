package com.seewin.som.report.provider.impl;

import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.entity.ReportBrandClusterResults;
import com.seewin.som.report.req.ReportBrandClusterResultsAddDto;
import com.seewin.som.report.req.ReportBrandClusterResultsEditDto;
import com.seewin.som.report.req.ReportBrandClusterResultsListDto;
import com.seewin.som.report.resp.ReportBrandClusterResultsAddVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsGetVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsListVo;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import com.seewin.som.report.service.ReportBrandClusterResultsService;
import com.seewin.som.report.provider.ReportBrandClusterResultsProvider;
/**
 * <p>
 * 品牌聚类结果信息表 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@DubboService
public class ReportBrandClusterResultsProviderImpl implements ReportBrandClusterResultsProvider{

	@Autowired
	private ReportBrandClusterResultsService reportBrandClusterResultsService;
	
	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<ReportBrandClusterResultsListVo> page(PageQuery<ReportBrandClusterResultsListDto> pageQuery) throws ServiceException
    {
    	ReportBrandClusterResultsListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<ReportBrandClusterResults> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<ReportBrandClusterResults> queryWrapper = queryBuild(dto);

        //查询数据
        page = reportBrandClusterResultsService.page(page, queryWrapper);
        List<ReportBrandClusterResults> records = page.getRecords();

        //响应结果封装
        PageResult<ReportBrandClusterResultsListVo> result = new PageResult<>();
        List<ReportBrandClusterResultsListVo> items = BeanUtils.copyProperties(records, ReportBrandClusterResultsListVo.class);
        
        result.setItems(items);
        result.setPages((int)page.getPages());
        result.setTotal((int)page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<ReportBrandClusterResultsListVo> list(ReportBrandClusterResultsListDto dto) throws ServiceException
    {
    	  //构造查询条件
        QueryWrapper<ReportBrandClusterResults> queryWrapper = queryBuild(dto);

        //查询数据
        List<ReportBrandClusterResults> records = reportBrandClusterResultsService.list(queryWrapper);

        //响应结果封装
        List<ReportBrandClusterResultsListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, ReportBrandClusterResultsListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(ReportBrandClusterResultsListDto dto) throws ServiceException
    {
     	//构造查询条件
        QueryWrapper<ReportBrandClusterResults> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) reportBrandClusterResultsService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public ReportBrandClusterResultsGetVo get(Long id) throws ServiceException
    {
    	//查询数据
        ReportBrandClusterResults item = reportBrandClusterResultsService.getById(id);

        //响应结果封装
        ReportBrandClusterResultsGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportBrandClusterResultsGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportBrandClusterResultsGetVo get(ReportBrandClusterResultsListDto dto) throws ServiceException
    {
        //构造查询条件
        QueryWrapper<ReportBrandClusterResults> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        ReportBrandClusterResults item = reportBrandClusterResultsService.getOne(queryWrapper);

        //响应结果封装
        ReportBrandClusterResultsGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, ReportBrandClusterResultsGetVo.class);
        }

        //返回查询结果
        return result; 	
    }


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
     @Override
    public ReportBrandClusterResultsAddVo add(ReportBrandClusterResultsAddDto dto) throws ServiceException
    {
    	 ReportBrandClusterResults entity = BeanUtils.copyProperties(dto, ReportBrandClusterResults.class);

        LocalDateTime nowTime = LocalDateTime.now();                
     	entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setUpdateTime(nowTime);
	    entity.setDelStatus(0);
        reportBrandClusterResultsService.save(entity);

        //响应结果封装
        ReportBrandClusterResultsAddVo result = new ReportBrandClusterResultsAddVo();
        result.setId(entity.getId());

        return result;
    }


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void edit(ReportBrandClusterResultsEditDto dto) throws ServiceException
    {
    	ReportBrandClusterResults entity = BeanUtils.copyProperties(dto, ReportBrandClusterResults.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateTime(nowTime);

        reportBrandClusterResultsService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(Long id) throws ServiceException
    {
    	 reportBrandClusterResultsService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(ReportBrandClusterResultsListDto dto) throws ServiceException
    {
    	//构造查询条件
        QueryWrapper<ReportBrandClusterResults> queryWrapper = queryBuild(dto, false);

        //删除操作
        reportBrandClusterResultsService.remove(queryWrapper);
    }
    
     /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportBrandClusterResults> queryBuild(ReportBrandClusterResultsListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<ReportBrandClusterResults> queryBuild(ReportBrandClusterResultsListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<ReportBrandClusterResults> queryWrapper = new QueryWrapper<>();

        ReportBrandClusterResults entity = BeanUtils.copyProperties(dto, ReportBrandClusterResults.class);
	    entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setName(null);
            queryWrapper.like("name", dto.getName());
        }

        queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

        if (orderBy) {
            if (dto.getTypeOrder() != null) {
                queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
            }

            queryWrapper.orderByAsc("order_by");
        }
        */

        //按创建时间倒序排序，根据需要添加
        //queryWrapper.orderByDesc("create_time");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}
