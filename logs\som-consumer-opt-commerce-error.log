{"appName":"som-consumer-opt-commerce","time":"2025-05-23 09:15:15","level":"ERROR","class":"com.seewin.consumer.handler.GlobalExceptionHandler","method":"handleException","line":"125","message":"请求异常","statck_trace":"java.io.IOException: 你的主机中的软件中止了一个已建立的连接。\r\n\tat java.base/sun.nio.ch.SocketDispatcher.writev0(Native Method)\r\n\tat java.base/sun.nio.ch.SocketDispatcher.writev(SocketDispatcher.java:58)\r\n\tat java.base/sun.nio.ch.IOUtil.write(IOUtil.java:217)\r\n\tat java.base/sun.nio.ch.IOUtil.write(IOUtil.java:153)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.write(SocketChannelImpl.java:563)\r\n\tat org.xnio.nio.NioSocketConduit.write(NioSocketConduit.java:162)\r\n\tat io.undertow.server.protocol.http.HttpResponseConduit.write(HttpResponseConduit.java:667)\r\n\tat io.undertow.conduits.AbstractFixedLengthStreamSinkConduit.write(AbstractFixedLengthStreamSinkConduit.java:148)\r\n\tat org.xnio.conduits.ConduitStreamSinkChannel.write(ConduitStreamSinkChannel.java:158)\r\n\tat io.undertow.channels.DetachableStreamSinkChannel.write(DetachableStreamSinkChannel.java:179)\r\n\tat io.undertow.server.HttpServerExchange$WriteDispatchChannel.write(HttpServerExchange.java:2172)\r\n\tat org.xnio.channels.Channels.writeBlocking(Channels.java:202)\r\n\tat io.undertow.servlet.spec.ServletOutputStreamImpl.writeTooLargeForBuffer(ServletOutputStreamImpl.java:222)\r\n\tat io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:149)\r\n\tat io.undertow.servlet.spec.ServletOutputStreamImpl.write(ServletOutputStreamImpl.java:131)\r\n\tat org.springframework.util.StreamUtils.copy(StreamUtils.java:117)\r\n\tat org.springframework.http.converter.ByteArrayHttpMessageConverter.writeInternal(ByteArrayHttpMessageConverter.java:67)\r\n\tat org.springframework.http.converter.ByteArrayHttpMessageConverter.writeInternal(ByteArrayHttpMessageConverter.java:38)\r\n\tat org.springframework.http.converter.AbstractHttpMessageConverter.write(AbstractHttpMessageConverter.java:236)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.AbstractMessageConverterMethodProcessor.writeWithMessageConverters(AbstractMessageConverterMethodProcessor.java:300)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestResponseBodyMethodProcessor.handleReturnValue(RequestResponseBodyMethodProcessor.java:190)\r\n\tat org.springframework.web.method.support.HandlerMethodReturnValueHandlerComposite.handleReturnValue(HandlerMethodReturnValueHandlerComposite.java:78)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:136)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 09:32:18","level":"ERROR","class":"com.seewin.som.commerce.service.impl.CommerceContractServiceImpl","method":"aiReviewSubmit","line":"3353","message":"存储userInput失败，但不影响主流程: org.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\norg.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\n\tat org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)\r\n\tat org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)\r\n\tat org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)\r\n\tat org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy105.update(Unknown Source)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.updateUserInput(CommerceContractServiceImpl.java:114)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl$$SpringCGLIB$$0.updateUserInput(<generated>)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImpl.updateUserInput(CommerceContractProviderImpl.java:1158)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImplDubboWrap33.invokeMethod(CommerceContractProviderImplDubboWrap33.java)\r\n\tat org.apache.dubbo.rpc.proxy.javassist.JavassistProxyFactory$1.doInvoke(JavassistProxyFactory.java:89)\r\n\tat org.apache.dubbo.rpc.proxy.AbstractProxyInvoker.invoke(AbstractProxyInvoker.java:100)\r\n\tat org.apache.dubbo.config.invoker.DelegateProviderMetaDataInvoker.invoke(DelegateProviderMetaDataInvoker.java:55)\r\n\tat org.apache.dubbo.rpc.protocol.InvokerWrapper.invoke(InvokerWrapper.java:56)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderCallbackFilter.invoke(ClassLoaderCallbackFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.TraceFilter.invoke(TraceFilter.java:80)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.TimeoutFilter.invoke(TimeoutFilter.java:45)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ExceptionFilter.invoke(ExceptionFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.AccessLogFilter.invoke(AccessLogFilter.java:120)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.observation.ObservationReceiverFilter.invoke(ObservationReceiverFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.GenericFilter.invoke(GenericFilter.java:222)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderFilter.invoke(ClassLoaderFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.EchoFilter.invoke(EchoFilter.java:41)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.metrics.filter.MetricsProviderFilter.invoke(MetricsProviderFilter.java:37)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ProfilerServerFilter.invoke(ProfilerServerFilter.java:66)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ContextFilter.invoke(ContextFilter.java:145)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCallListener.invoke(AbstractServerCallListener.java:64)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.UnaryServerCallListener.onComplete(UnaryServerCallListener.java:82)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCall.onComplete(AbstractServerCall.java:208)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerDecoderListener.close(TripleServerStream.java:502)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.deliver(TriDecoder.java:101)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.close(TriDecoder.java:67)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.doOnData(TripleServerStream.java:468)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.lambda$onData$2(TripleServerStream.java:452)\r\n\tat org.apache.dubbo.common.threadpool.serial.SerializingExecutor.run(SerializingExecutor.java:105)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)\r\n\tat org.apache.dubbo.common.threadlocal.InternalRunnable.run(InternalRunnable.java:39)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n\tat com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)\r\n\tat com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)\r\n\tat com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)\r\n\tat com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)\r\n\tat jdk.proxy3/jdk.proxy3.$Proxy145.execute(Unknown Source)\r\n\tat org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)\r\n\tat org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy143.update(Unknown Source)\r\n\tat org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)\r\n\tat org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)\r\n\tat org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)\r\n\tat com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy142.update(Unknown Source)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)\r\n\t... 61 more\r\n","statck_trace":""}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 09:43:05","level":"ERROR","class":"com.seewin.consumer.interceptor.AuthValidInterceptor","method":"preHandle","line":"139","message":"请求异常","statck_trace":"org.springframework.data.redis.RedisSystemException: Redis exception\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)\r\n\tat org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)\r\n\tat org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:306)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1010)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$3(LettuceConnection.java:443)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:72)\r\n\tat org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:86)\r\n\tat org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:337)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$3(RedisTemplate.java:571)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)\r\n\tat org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:571)\r\n\tat com.seewin.redis.util.RedisUtil.get(RedisUtil.java:24)\r\n\tat com.seewin.redis.handler.ParamHandler.get(ParamHandler.java:13)\r\n\tat com.seewin.consumer.interceptor.AuthValidInterceptor.preHandle(AuthValidInterceptor.java:126)\r\n\tat org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset\r\n\tat io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)\r\n\tat io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)\r\n\tat io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1008)\r\n\t... 67 common frames omitted\r\nCaused by: java.net.SocketException: Connection reset\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)\r\n\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)\r\n\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\r\n\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\t... 1 common frames omitted\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 09:44:16","level":"ERROR","class":"com.seewin.som.commerce.service.impl.CommerceContractServiceImpl","method":"aiReviewSubmit","line":"3353","message":"存储userInput失败，但不影响主流程: org.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\norg.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\n\tat org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)\r\n\tat org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)\r\n\tat org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)\r\n\tat org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy105.update(Unknown Source)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.updateUserInput(CommerceContractServiceImpl.java:114)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl$$SpringCGLIB$$0.updateUserInput(<generated>)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImpl.updateUserInput(CommerceContractProviderImpl.java:1158)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImplDubboWrap33.invokeMethod(CommerceContractProviderImplDubboWrap33.java)\r\n\tat org.apache.dubbo.rpc.proxy.javassist.JavassistProxyFactory$1.doInvoke(JavassistProxyFactory.java:89)\r\n\tat org.apache.dubbo.rpc.proxy.AbstractProxyInvoker.invoke(AbstractProxyInvoker.java:100)\r\n\tat org.apache.dubbo.config.invoker.DelegateProviderMetaDataInvoker.invoke(DelegateProviderMetaDataInvoker.java:55)\r\n\tat org.apache.dubbo.rpc.protocol.InvokerWrapper.invoke(InvokerWrapper.java:56)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderCallbackFilter.invoke(ClassLoaderCallbackFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.TraceFilter.invoke(TraceFilter.java:80)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.TimeoutFilter.invoke(TimeoutFilter.java:45)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ExceptionFilter.invoke(ExceptionFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.AccessLogFilter.invoke(AccessLogFilter.java:120)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.observation.ObservationReceiverFilter.invoke(ObservationReceiverFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.GenericFilter.invoke(GenericFilter.java:222)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderFilter.invoke(ClassLoaderFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.EchoFilter.invoke(EchoFilter.java:41)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.metrics.filter.MetricsProviderFilter.invoke(MetricsProviderFilter.java:37)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ProfilerServerFilter.invoke(ProfilerServerFilter.java:66)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ContextFilter.invoke(ContextFilter.java:145)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCallListener.invoke(AbstractServerCallListener.java:64)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.UnaryServerCallListener.onComplete(UnaryServerCallListener.java:82)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCall.onComplete(AbstractServerCall.java:208)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerDecoderListener.close(TripleServerStream.java:502)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.deliver(TriDecoder.java:101)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.close(TriDecoder.java:67)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.doOnData(TripleServerStream.java:468)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.lambda$onData$2(TripleServerStream.java:452)\r\n\tat org.apache.dubbo.common.threadpool.serial.SerializingExecutor.run(SerializingExecutor.java:105)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)\r\n\tat org.apache.dubbo.common.threadlocal.InternalRunnable.run(InternalRunnable.java:39)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n\tat com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)\r\n\tat com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)\r\n\tat com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)\r\n\tat com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)\r\n\tat jdk.proxy3/jdk.proxy3.$Proxy145.execute(Unknown Source)\r\n\tat org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)\r\n\tat org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy143.update(Unknown Source)\r\n\tat org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)\r\n\tat org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)\r\n\tat org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)\r\n\tat com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy142.update(Unknown Source)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)\r\n\t... 61 more\r\n","statck_trace":""}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 09:46:11","level":"ERROR","class":"com.seewin.som.commerce.service.impl.CommerceContractServiceImpl","method":"aiReviewSubmit","line":"3353","message":"存储userInput失败，但不影响主流程: org.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\norg.springframework.jdbc.BadSqlGrammarException: \r\n### Error updating database.  Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.update-Inline\r\n### The error occurred while setting parameters\r\n### SQL: UPDATE som_commerce_contract     WHERE del_status=0     AND (contract_code = ? AND user_input = ?)\r\n### Cause: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\n; bad SQL grammar []\r\n\tat org.springframework.jdbc.support.SQLErrorCodeSQLExceptionTranslator.doTranslate(SQLErrorCodeSQLExceptionTranslator.java:246)\r\n\tat org.springframework.jdbc.support.AbstractFallbackSQLExceptionTranslator.translate(AbstractFallbackSQLExceptionTranslator.java:107)\r\n\tat org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:92)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy94.update(Unknown Source)\r\n\tat org.mybatis.spring.SqlSessionTemplate.update(SqlSessionTemplate.java:288)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:64)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy105.update(Unknown Source)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.updateUserInput(CommerceContractServiceImpl.java:114)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl$$SpringCGLIB$$0.updateUserInput(<generated>)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImpl.updateUserInput(CommerceContractProviderImpl.java:1158)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImplDubboWrap33.invokeMethod(CommerceContractProviderImplDubboWrap33.java)\r\n\tat org.apache.dubbo.rpc.proxy.javassist.JavassistProxyFactory$1.doInvoke(JavassistProxyFactory.java:89)\r\n\tat org.apache.dubbo.rpc.proxy.AbstractProxyInvoker.invoke(AbstractProxyInvoker.java:100)\r\n\tat org.apache.dubbo.config.invoker.DelegateProviderMetaDataInvoker.invoke(DelegateProviderMetaDataInvoker.java:55)\r\n\tat org.apache.dubbo.rpc.protocol.InvokerWrapper.invoke(InvokerWrapper.java:56)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderCallbackFilter.invoke(ClassLoaderCallbackFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.TraceFilter.invoke(TraceFilter.java:80)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.TimeoutFilter.invoke(TimeoutFilter.java:45)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ExceptionFilter.invoke(ExceptionFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.AccessLogFilter.invoke(AccessLogFilter.java:120)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.observation.ObservationReceiverFilter.invoke(ObservationReceiverFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.GenericFilter.invoke(GenericFilter.java:222)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderFilter.invoke(ClassLoaderFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.EchoFilter.invoke(EchoFilter.java:41)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.metrics.filter.MetricsProviderFilter.invoke(MetricsProviderFilter.java:37)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ProfilerServerFilter.invoke(ProfilerServerFilter.java:66)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ContextFilter.invoke(ContextFilter.java:145)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCallListener.invoke(AbstractServerCallListener.java:64)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.UnaryServerCallListener.onComplete(UnaryServerCallListener.java:82)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCall.onComplete(AbstractServerCall.java:208)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerDecoderListener.close(TripleServerStream.java:502)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.deliver(TriDecoder.java:101)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.close(TriDecoder.java:67)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.doOnData(TripleServerStream.java:468)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.lambda$onData$2(TripleServerStream.java:452)\r\n\tat org.apache.dubbo.common.threadpool.serial.SerializingExecutor.run(SerializingExecutor.java:105)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)\r\n\tat org.apache.dubbo.common.threadlocal.InternalRunnable.run(InternalRunnable.java:39)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: java.sql.SQLSyntaxErrorException: You have an error in your SQL syntax; check the manual that corresponds to your MySQL server version for the right syntax to use near 'WHERE del_status=0     AND (contract_code = '99992025016' AND user_input = '预�' at line 1\r\n\tat com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:120)\r\n\tat com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.executeInternal(ClientPreparedStatement.java:916)\r\n\tat com.mysql.cj.jdbc.ClientPreparedStatement.execute(ClientPreparedStatement.java:354)\r\n\tat com.zaxxer.hikari.pool.ProxyPreparedStatement.execute(ProxyPreparedStatement.java:44)\r\n\tat com.zaxxer.hikari.pool.HikariProxyPreparedStatement.execute(HikariProxyPreparedStatement.java)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.logging.jdbc.PreparedStatementLogger.invoke(PreparedStatementLogger.java:58)\r\n\tat jdk.proxy3/jdk.proxy3.$Proxy145.execute(Unknown Source)\r\n\tat org.apache.ibatis.executor.statement.PreparedStatementHandler.update(PreparedStatementHandler.java:48)\r\n\tat org.apache.ibatis.executor.statement.RoutingStatementHandler.update(RoutingStatementHandler.java:75)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:61)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy143.update(Unknown Source)\r\n\tat org.apache.ibatis.executor.SimpleExecutor.doUpdate(SimpleExecutor.java:50)\r\n\tat org.apache.ibatis.executor.BaseExecutor.update(BaseExecutor.java:117)\r\n\tat org.apache.ibatis.executor.CachingExecutor.update(CachingExecutor.java:76)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.ibatis.plugin.Invocation.proceed(Invocation.java:49)\r\n\tat com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:106)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy142.update(Unknown Source)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.update(DefaultSqlSession.java:197)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)\r\n\t... 61 more\r\n","statck_trace":""}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 14:34:32","level":"ERROR","class":"com.seewin.consumer.interceptor.AuthValidInterceptor","method":"preHandle","line":"139","message":"请求异常","statck_trace":"org.springframework.data.redis.RedisSystemException: Redis exception\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)\r\n\tat org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)\r\n\tat org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:306)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1010)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$3(LettuceConnection.java:443)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:72)\r\n\tat org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:86)\r\n\tat org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:337)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$3(RedisTemplate.java:571)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)\r\n\tat org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:571)\r\n\tat com.seewin.redis.util.RedisUtil.get(RedisUtil.java:24)\r\n\tat com.seewin.redis.handler.ParamHandler.get(ParamHandler.java:13)\r\n\tat com.seewin.consumer.interceptor.AuthValidInterceptor.preHandle(AuthValidInterceptor.java:126)\r\n\tat org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset\r\n\tat io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)\r\n\tat io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)\r\n\tat io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1008)\r\n\t... 67 common frames omitted\r\nCaused by: java.net.SocketException: Connection reset\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)\r\n\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)\r\n\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\r\n\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\t... 1 common frames omitted\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 14:35:43","level":"ERROR","class":"com.seewin.consumer.handler.GlobalExceptionHandler","method":"handleRuntimeExceptionn","line":"116","message":"请求异常","statck_trace":"java.lang.RuntimeException: org.mybatis.spring.MyBatisSystemException\r\norg.mybatis.spring.MyBatisSystemException\r\n\tat org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy94.selectList(Unknown Source)\r\n\tat org.mybatis.spring.SqlSessionTemplate.selectList(SqlSessionTemplate.java:224)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperMethod.executeForMany(MybatisMapperMethod.java:164)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:77)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy105.selectList(Unknown Source)\r\n\tat com.baomidou.mybatisplus.core.mapper.BaseMapper.selectOne(BaseMapper.java:222)\r\n\tat java.base/java.lang.invoke.MethodHandle.invokeWithArguments(MethodHandle.java:732)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy$DefaultMethodInvoker.invoke(MybatisMapperProxy.java:166)\r\n\tat com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy105.selectOne(Unknown Source)\r\n\tat com.baomidou.mybatisplus.extension.service.impl.ServiceImpl.getOne(ServiceImpl.java:242)\r\n\tat com.baomidou.mybatisplus.extension.service.IService.getOne(IService.java:327)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:352)\r\n\tat org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:713)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl$$SpringCGLIB$$0.getOne(<generated>)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImpl.get(CommerceContractProviderImpl.java:222)\r\n\tat com.seewin.som.commerce.provider.impl.CommerceContractProviderImplDubboWrap33.invokeMethod(CommerceContractProviderImplDubboWrap33.java)\r\n\tat org.apache.dubbo.rpc.proxy.javassist.JavassistProxyFactory$1.doInvoke(JavassistProxyFactory.java:89)\r\n\tat org.apache.dubbo.rpc.proxy.AbstractProxyInvoker.invoke(AbstractProxyInvoker.java:100)\r\n\tat org.apache.dubbo.config.invoker.DelegateProviderMetaDataInvoker.invoke(DelegateProviderMetaDataInvoker.java:55)\r\n\tat org.apache.dubbo.rpc.protocol.InvokerWrapper.invoke(InvokerWrapper.java:56)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderCallbackFilter.invoke(ClassLoaderCallbackFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.TraceFilter.invoke(TraceFilter.java:80)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.TimeoutFilter.invoke(TimeoutFilter.java:45)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ExceptionFilter.invoke(ExceptionFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.AccessLogFilter.invoke(AccessLogFilter.java:120)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.observation.ObservationReceiverFilter.invoke(ObservationReceiverFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.GenericFilter.invoke(GenericFilter.java:222)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ClassLoaderFilter.invoke(ClassLoaderFilter.java:54)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.EchoFilter.invoke(EchoFilter.java:41)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.metrics.filter.MetricsProviderFilter.invoke(MetricsProviderFilter.java:37)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ProfilerServerFilter.invoke(ProfilerServerFilter.java:66)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.filter.ContextFilter.invoke(ContextFilter.java:145)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCallListener.invoke(AbstractServerCallListener.java:64)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.UnaryServerCallListener.onComplete(UnaryServerCallListener.java:82)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCall.onComplete(AbstractServerCall.java:208)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerDecoderListener.close(TripleServerStream.java:502)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.deliver(TriDecoder.java:101)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.close(TriDecoder.java:67)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.doOnData(TripleServerStream.java:468)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.lambda$onData$2(TripleServerStream.java:452)\r\n\tat org.apache.dubbo.common.threadpool.serial.SerializingExecutor.run(SerializingExecutor.java:105)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)\r\n\tat java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)\r\n\tat org.apache.dubbo.common.threadlocal.InternalRunnable.run(InternalRunnable.java:39)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: org.apache.ibatis.exceptions.PersistenceException: \r\n### Error querying database.  Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection\r\n### The error may exist in com/seewin/som/commerce/mapper/CommerceContractMapper.java (best guess)\r\n### The error may involve com.seewin.som.commerce.mapper.CommerceContractMapper.selectList\r\n### The error occurred while executing a query\r\n### Cause: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection\r\n\tat org.apache.ibatis.exceptions.ExceptionFactory.wrapException(ExceptionFactory.java:30)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:156)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:147)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:142)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:425)\r\n\t... 68 more\r\nCaused by: org.springframework.jdbc.CannotGetJdbcConnectionException: Failed to obtain JDBC Connection\r\n\tat org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:84)\r\n\tat org.mybatis.spring.transaction.SpringManagedTransaction.openConnection(SpringManagedTransaction.java:80)\r\n\tat org.mybatis.spring.transaction.SpringManagedTransaction.getConnection(SpringManagedTransaction.java:67)\r\n\tat org.apache.ibatis.executor.BaseExecutor.getConnection(BaseExecutor.java:348)\r\n\tat org.apache.ibatis.executor.SimpleExecutor.prepareStatement(SimpleExecutor.java:89)\r\n\tat org.apache.ibatis.executor.SimpleExecutor.doQuery(SimpleExecutor.java:64)\r\n\tat org.apache.ibatis.executor.BaseExecutor.queryFromDatabase(BaseExecutor.java:336)\r\n\tat org.apache.ibatis.executor.BaseExecutor.query(BaseExecutor.java:158)\r\n\tat org.apache.ibatis.executor.CachingExecutor.query(CachingExecutor.java:110)\r\n\tat com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor.intercept(MybatisPlusInterceptor.java:81)\r\n\tat org.apache.ibatis.plugin.Plugin.invoke(Plugin.java:59)\r\n\tat jdk.proxy2/jdk.proxy2.$Proxy142.query(Unknown Source)\r\n\tat org.apache.ibatis.session.defaults.DefaultSqlSession.selectList(DefaultSqlSession.java:154)\r\n\t... 75 more\r\nCaused by: java.sql.SQLException: Access denied for user 'som'@'192.168.1.140' (using password: YES)\r\n\tat com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:129)\r\n\tat com.mysql.cj.jdbc.exceptions.SQLExceptionsMapping.translateException(SQLExceptionsMapping.java:122)\r\n\tat com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:828)\r\n\tat com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:448)\r\n\tat com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:241)\r\n\tat com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:198)\r\n\tat com.zaxxer.hikari.util.DriverDataSource.getConnection(DriverDataSource.java:138)\r\n\tat com.zaxxer.hikari.pool.PoolBase.newConnection(PoolBase.java:359)\r\n\tat com.zaxxer.hikari.pool.PoolBase.newPoolEntry(PoolBase.java:201)\r\n\tat com.zaxxer.hikari.pool.HikariPool.createPoolEntry(HikariPool.java:470)\r\n\tat com.zaxxer.hikari.pool.HikariPool.checkFailFast(HikariPool.java:561)\r\n\tat com.zaxxer.hikari.pool.HikariPool.<init>(HikariPool.java:100)\r\n\tat com.zaxxer.hikari.HikariDataSource.getConnection(HikariDataSource.java:112)\r\n\tat org.springframework.jdbc.datasource.DataSourceUtils.fetchConnection(DataSourceUtils.java:160)\r\n\tat org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:118)\r\n\tat org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:81)\r\n\t... 87 more\r\n\r\n\tat org.apache.dubbo.rpc.filter.ExceptionFilter.onResponse(ExceptionFilter.java:112)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.lambda$invoke$1(FilterChainBuilder.java:221)\r\n\tat org.apache.dubbo.rpc.AsyncRpcResult.lambda$whenCompleteWithContext$0(AsyncRpcResult.java:253)\r\n\tat java.util.concurrent.CompletableFuture.uniWhenComplete(CompletableFuture.java:863)\r\n\tat java.util.concurrent.CompletableFuture.uniWhenCompleteStage(CompletableFuture.java:887)\r\n\tat java.util.concurrent.CompletableFuture.whenComplete(CompletableFuture.java:2325)\r\n\tat org.apache.dubbo.rpc.AsyncRpcResult.whenCompleteWithContext(AsyncRpcResult.java:249)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:198)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCallListener.invoke(AbstractServerCallListener.java:64)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.UnaryServerCallListener.onComplete(UnaryServerCallListener.java:82)\r\n\tat org.apache.dubbo.rpc.protocol.tri.call.AbstractServerCall.onComplete(AbstractServerCall.java:208)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerDecoderListener.close(TripleServerStream.java:502)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.deliver(TriDecoder.java:101)\r\n\tat org.apache.dubbo.rpc.protocol.tri.frame.TriDecoder.close(TriDecoder.java:67)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.doOnData(TripleServerStream.java:468)\r\n\tat org.apache.dubbo.rpc.protocol.tri.stream.TripleServerStream$ServerTransportObserver.lambda$onData$2(TripleServerStream.java:452)\r\n\tat org.apache.dubbo.common.threadpool.serial.SerializingExecutor.run(SerializingExecutor.java:105)\r\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)\r\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)\r\n\tat org.apache.dubbo.common.threadlocal.InternalRunnable.run(InternalRunnable.java:39)\r\n\tat java.lang.Thread.run(Thread.java:842)\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 14:40:25","level":"ERROR","class":"com.seewin.consumer.handler.GlobalExceptionHandler","method":"handleRuntimeExceptionn","line":"116","message":"请求异常","statck_trace":"org.apache.dubbo.rpc.RpcException: No provider available from registry RegistryDirectory(registry: localhost:8848)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) for service com.seewin.som.storage.provider.SysAttachmentProvider on consumer *********** use dubbo version 3.2.10, please check status of providers(disabled, not registered or in blacklist).\r\n\tat org.apache.dubbo.registry.integration.DynamicDirectory.doList(DynamicDirectory.java:204)\r\n\tat org.apache.dubbo.rpc.cluster.directory.AbstractDirectory.list(AbstractDirectory.java:227)\r\n\tat org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.list(AbstractClusterInvoker.java:452)\r\n\tat org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:355)\r\n\tat org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:123)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)\r\n\tat org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:294)\r\n\tat org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)\r\n\tat org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)\r\n\tat com.seewin.som.storage.provider.SysAttachmentProviderDubboProxy5.list(SysAttachmentProviderDubboProxy5.java)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)\r\n\tat com.seewin.som.storage.provider.SysAttachmentProviderDubboProxy5.list(SysAttachmentProviderDubboProxy5.java)\r\n\tat com.seewin.som.commerce.utils.FileUtils.findFile(FileUtils.java:53)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.getByContractCode(CommerceContractServiceImpl.java:352)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.aiReviewSubmit(CommerceContractServiceImpl.java:3270)\r\n\tat com.seewin.som.commerce.controller.CommerceContractController.aiReviewSubmit(CommerceContractController.java:138)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 14:50:50","level":"ERROR","class":"com.seewin.consumer.handler.GlobalExceptionHandler","method":"handleRuntimeExceptionn","line":"116","message":"请求异常","statck_trace":"org.apache.dubbo.rpc.RpcException: No provider available from registry RegistryDirectory(registry: localhost:8848)-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) for service com.seewin.som.storage.provider.SysAttachmentProvider on consumer *********** use dubbo version 3.2.10, please check status of providers(disabled, not registered or in blacklist).\r\n\tat org.apache.dubbo.registry.integration.DynamicDirectory.doList(DynamicDirectory.java:204)\r\n\tat org.apache.dubbo.rpc.cluster.directory.AbstractDirectory.list(AbstractDirectory.java:227)\r\n\tat org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.list(AbstractClusterInvoker.java:452)\r\n\tat org.apache.dubbo.rpc.cluster.support.AbstractClusterInvoker.invoke(AbstractClusterInvoker.java:355)\r\n\tat org.apache.dubbo.rpc.cluster.router.RouterSnapshotFilter.invoke(RouterSnapshotFilter.java:46)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.monitor.support.MonitorFilter.invoke(MonitorFilter.java:108)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.MetricsClusterFilter.invoke(MetricsClusterFilter.java:57)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.protocol.dubbo.filter.FutureFilter.invoke(FutureFilter.java:52)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ObservationSenderFilter.invoke(ObservationSenderFilter.java:62)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.spring.security.filter.ContextHolderParametersSelectedTransferFilter.invoke(ContextHolderParametersSelectedTransferFilter.java:40)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.metrics.filter.MetricsFilter.invoke(MetricsFilter.java:86)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.MetricsConsumerFilter.invoke(MetricsConsumerFilter.java:38)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ConsumerClassLoaderFilter.invoke(ConsumerClassLoaderFilter.java:40)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.support.ConsumerContextFilter.invoke(ConsumerContextFilter.java:123)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CopyOfFilterChainNode.invoke(FilterChainBuilder.java:349)\r\n\tat org.apache.dubbo.rpc.cluster.filter.FilterChainBuilder$CallbackRegistrationInvoker.invoke(FilterChainBuilder.java:197)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.AbstractCluster$ClusterFilterInvoker.invoke(AbstractCluster.java:101)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.MockClusterInvoker.invoke(MockClusterInvoker.java:106)\r\n\tat org.apache.dubbo.rpc.cluster.support.wrapper.ScopeClusterInvoker.invoke(ScopeClusterInvoker.java:171)\r\n\tat org.apache.dubbo.registry.client.migration.MigrationInvoker.invoke(MigrationInvoker.java:294)\r\n\tat org.apache.dubbo.rpc.proxy.InvocationUtil.invoke(InvocationUtil.java:64)\r\n\tat org.apache.dubbo.rpc.proxy.InvokerInvocationHandler.invoke(InvokerInvocationHandler.java:81)\r\n\tat com.seewin.som.storage.provider.SysAttachmentProviderDubboProxy5.list(SysAttachmentProviderDubboProxy5.java)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.apache.dubbo.config.spring.util.LazyTargetInvocationHandler.invoke(LazyTargetInvocationHandler.java:54)\r\n\tat com.seewin.som.storage.provider.SysAttachmentProviderDubboProxy5.list(SysAttachmentProviderDubboProxy5.java)\r\n\tat com.seewin.som.commerce.utils.FileUtils.findFile(FileUtils.java:53)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.getByContractCode(CommerceContractServiceImpl.java:351)\r\n\tat com.seewin.som.commerce.service.impl.CommerceContractServiceImpl.aiReviewSubmit(CommerceContractServiceImpl.java:3269)\r\n\tat com.seewin.som.commerce.controller.CommerceContractController.aiReviewSubmit(CommerceContractController.java:138)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\r\n\tat java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\r\n\tat java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\r\n\tat java.base/java.lang.reflect.Method.invoke(Method.java:568)\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:262)\r\n\tat org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:190)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:917)\r\n\tat org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:829)\r\n\tat org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:547)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1512)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\n"}
{"appName":"som-consumer-opt-commerce","time":"2025-05-23 15:54:04","level":"ERROR","class":"com.seewin.consumer.interceptor.AuthValidInterceptor","method":"preHandle","line":"139","message":"请求异常","statck_trace":"org.springframework.data.redis.RedisSystemException: Redis exception\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)\r\n\tat org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)\r\n\tat org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:306)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1010)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$3(LettuceConnection.java:443)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:72)\r\n\tat org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:86)\r\n\tat org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:337)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$3(RedisTemplate.java:571)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)\r\n\tat org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:571)\r\n\tat com.seewin.redis.util.RedisUtil.get(RedisUtil.java:24)\r\n\tat com.seewin.redis.handler.ParamHandler.get(ParamHandler.java:13)\r\n\tat com.seewin.consumer.interceptor.AuthValidInterceptor.preHandle(AuthValidInterceptor.java:126)\r\n\tat org.springframework.web.servlet.HandlerExecutionChain.applyPreHandle(HandlerExecutionChain.java:146)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1084)\r\n\tat org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\r\n\tat org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\r\n\tat org.springframework.web.servlet.FrameworkServlet.doGet(FrameworkServlet.java:903)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:527)\r\n\tat org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\r\n\tat jakarta.servlet.http.HttpServlet.service(HttpServlet.java:614)\r\n\tat io.undertow.servlet.handlers.ServletHandler.handleRequest(ServletHandler.java:74)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:129)\r\n\tat org.springframework.web.filter.RequestContextFilter.doFilterInternal(RequestContextFilter.java:100)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.FormContentFilter.doFilterInternal(FormContentFilter.java:93)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\r\n\tat org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\r\n\tat io.undertow.servlet.core.ManagedFilter.doFilter(ManagedFilter.java:67)\r\n\tat io.undertow.servlet.handlers.FilterHandler$FilterChainImpl.doFilter(FilterHandler.java:131)\r\n\tat io.undertow.servlet.handlers.FilterHandler.handleRequest(FilterHandler.java:84)\r\n\tat io.undertow.servlet.handlers.security.ServletSecurityRoleHandler.handleRequest(ServletSecurityRoleHandler.java:62)\r\n\tat io.undertow.servlet.handlers.ServletChain$1.handleRequest(ServletChain.java:68)\r\n\tat io.undertow.servlet.handlers.ServletDispatchingHandler.handleRequest(ServletDispatchingHandler.java:36)\r\n\tat io.undertow.servlet.handlers.RedirectDirHandler.handleRequest(RedirectDirHandler.java:68)\r\n\tat io.undertow.servlet.handlers.security.SSLInformationAssociationHandler.handleRequest(SSLInformationAssociationHandler.java:117)\r\n\tat io.undertow.servlet.handlers.security.ServletAuthenticationCallHandler.handleRequest(ServletAuthenticationCallHandler.java:57)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.security.handlers.AbstractConfidentialityHandler.handleRequest(AbstractConfidentialityHandler.java:46)\r\n\tat io.undertow.servlet.handlers.security.ServletConfidentialityConstraintHandler.handleRequest(ServletConfidentialityConstraintHandler.java:64)\r\n\tat io.undertow.security.handlers.AuthenticationMechanismsHandler.handleRequest(AuthenticationMechanismsHandler.java:60)\r\n\tat io.undertow.servlet.handlers.security.CachedAuthenticatedSessionHandler.handleRequest(CachedAuthenticatedSessionHandler.java:77)\r\n\tat io.undertow.security.handlers.AbstractSecurityContextAssociationHandler.handleRequest(AbstractSecurityContextAssociationHandler.java:43)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.SendErrorPageHandler.handleRequest(SendErrorPageHandler.java:52)\r\n\tat io.undertow.server.handlers.PredicateHandler.handleRequest(PredicateHandler.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.handleFirstRequest(ServletInitialHandler.java:276)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:135)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$2.call(ServletInitialHandler.java:132)\r\n\tat io.undertow.servlet.core.ServletRequestContextThreadSetupAction$1.call(ServletRequestContextThreadSetupAction.java:48)\r\n\tat io.undertow.servlet.core.ContextClassLoaderSetupAction$1.call(ContextClassLoaderSetupAction.java:43)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler.dispatchRequest(ServletInitialHandler.java:256)\r\n\tat io.undertow.servlet.handlers.ServletInitialHandler$1.handleRequest(ServletInitialHandler.java:101)\r\n\tat io.undertow.server.Connectors.executeRootHandler(Connectors.java:393)\r\n\tat io.undertow.server.HttpServerExchange$1.run(HttpServerExchange.java:859)\r\n\tat org.jboss.threads.ContextHandler$1.runWith(ContextHandler.java:18)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$Task.run(EnhancedQueueExecutor.java:2513)\r\n\tat org.jboss.threads.EnhancedQueueExecutor$ThreadBody.run(EnhancedQueueExecutor.java:1538)\r\n\tat org.xnio.XnioWorker$WorkerThreadFactory$1$1.run(XnioWorker.java:1282)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset\r\n\tat io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)\r\n\tat io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)\r\n\tat io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1008)\r\n\t... 67 common frames omitted\r\nCaused by: java.net.SocketException: Connection reset\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)\r\n\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)\r\n\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\r\n\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\t... 1 common frames omitted\r\n"}
