package com.seewin.som.report.resp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 品牌聚类结果信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ReportBrandClusterResultsListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 簇序号
     */
    private Integer cluster;

    /**
     * 业态
     */
    private String commercialTypeName;

    /**
     * 平均销售坪效
     */
    private BigDecimal salesPerSquareFootMean;

    /**
     * 平均实用面积
     */
    private BigDecimal actualAreaMean;

    /**
     * 平均销售笔数
     */
    private BigDecimal totalOrderMean;

    /**
     * 平均客单价
     */
    private BigDecimal averageOrderValueMean;

    /**
     * 平均租金坪效
     */
    private BigDecimal rentPerSquareFootMean;

    /**
     * 平均租售比
     */
    private BigDecimal rentToSalesRatioMean;

    /**
     * 平均进店转化率
     */
    private BigDecimal enterConversionRateMean;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    private LocalDateTime updateTime;


}
