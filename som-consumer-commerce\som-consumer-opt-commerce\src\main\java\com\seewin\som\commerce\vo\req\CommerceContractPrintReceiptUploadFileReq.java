package com.seewin.som.commerce.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintReceiptUploadFileReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用章公司编码
     */
    @Schema(description = "核算组织，即用章公司")
    @NotNull(message = "核算组织不能为空")
    private String accountingOrgName;


    /**
     * 文件总页数
     */
    @Schema(description = "文件总页数")
    @NotNull(message = "文件总页数不能为空")
    private Integer pageNum;


}
