package com.seewin.som.commerce.mapper;

import com.seewin.som.commerce.entity.CommerceContractPrint;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.seewin.som.commerce.resp.CommerceContractPrintGetVo;
import com.seewin.som.commerce.resp.CommerceContractPrintListVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 招商合同打印表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface CommerceContractPrintMapper extends BaseMapper<CommerceContractPrint> {

    CommerceContractPrintGetVo getBasicInfoByContractCode(String contractCode);

    CommerceContractPrintGetVo selectSignInfoBySignFlowId(String signFlowId);

    void updateSignStatusBySignFlowId(String signFlowId, Integer signStatus);

    String selectSealId(Integer signCompanyCode,Integer sealTypeCode);

    Map<String, Object> getSignInfoBySignCompanyCode(Integer signCompanyCode);

    Map<String, Object> selectSealIdByOaUploadReq(String signCompanyName,String sealTypeName);

    Integer selectCompanyCodeByLessor(String lessor);

    List<CommerceContractPrintListVo> getOtherFileById(Long id);

    List<CommerceContractPrintGetVo> getAllFileBySameContractId(String sameContractId);

    void batchUpdateTemplateId(Long newTemplateId, Long templateId);
}
