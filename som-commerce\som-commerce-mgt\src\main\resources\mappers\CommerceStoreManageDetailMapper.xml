<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceStoreManageDetailMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceStoreManageDetail">
        <id column="id" property="id" />
        <result column="store_manage_id" property="storeManageId" />
        <result column="store_id" property="storeId" />
        <result column="room_status" property="roomStatus" />
        <result column="brand_id" property="brandId" />
        <result column="brand_name" property="brandName" />
        <result column="refer_open_time" property="referOpenTime" />
        <result column="refer_close_time" property="referCloseTime" />
        <result column="refer_open_length" property="referOpenLength" />
        <result column="refer_flow_open_time" property="referFlowOpenTime" />
        <result column="refer_flow_close_time" property="referFlowCloseTime" />
        <result column="refer_flow_open_length" property="referFlowOpenLength" />
        <result column="electricity_open_time" property="electricityOpenTime" />
        <result column="electricity_close_time" property="electricityCloseTime" />
        <result column="electricity_open_length" property="electricityOpenLength" />
        <result column="flow_open_time" property="flowOpenTime" />
        <result column="flow_close_time" property="flowCloseTime" />
        <result column="flow_open_length" property="flowOpenLength" />
        <result column="store_status" property="storeStatus" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>

    <update id="updateCloseTime">
        update som_commerce_store_manage_detail set electricity_close_time = #{dto.electricityCloseTime}, electricity_open_length = #{dto.electricityOpenLength} where del_status = 0 and store_manage_id = #{dto.storeManageId}
        and create_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd}
    </update>

    <select id="getDetailData" resultType="com.seewin.som.commerce.resp.CommerceStoreManageDetailListVo">
        select * from som_commerce_store_manage_detail where del_status = 0 and store_manage_id = #{dto.storeManageId}
        and create_time BETWEEN #{dto.createTimeStart} AND #{dto.createTimeEnd} order by create_time desc limit 1
    </select>

</mapper>
