package com.seewin.som.commerce.vo.resp;

import com.seewin.som.commerce.resp.CommerceContractPrintOaVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import org.apache.poi.ss.formula.functions.T;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintOaSealResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "业务数据")
    private List<CommerceContractPrintOaVo> data;

    @Schema(description = "应答状态")
    private Integer code = 0;

    @Schema(description = "应答消息")
    private String msg = "";






}
