package com.seewin.som.commerce.controller;

import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.*;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.seewin.som.commerce.service.CommerceContractService;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Tag(name = "招商合同表-商铺")
@RestController
@RequestMapping("commerceContract")
public class CommerceContractController {

    @Autowired
    private CommerceContractService commerceContractService;

    @Operation(summary = "招商合同表-台账", description = "权限码：opt:commerce:commercecontract:page")
    @PostMapping(ApiMethod.PAGE)
    public ApiResponse<PageResp<CommerceContractListItem>> page(@RequestBody @Valid CommerceContractListReq listReq) {
        ApiResponse<PageResp<CommerceContractListItem>> result = new ApiResponse<>();

        PageResp<CommerceContractListItem> pageResp = commerceContractService.page(listReq,false);

        result.setData(pageResp);

        return result;
    }

    @Operation(summary = "招商合同表-台账", description = "权限码：opt:commerce:commercecontract:pageAll")
    @PostMapping("pageAll")
    public ApiResponse<PageResp<CommerceContractListItem>> pageAll(@RequestBody @Valid CommerceContractListReq listReq) {
        ApiResponse<PageResp<CommerceContractListItem>> result = new ApiResponse<>();

        PageResp<CommerceContractListItem> pageResp = commerceContractService.page(listReq, true);

        result.setData(pageResp);

        return result;
    }

    @Operation(summary = "招商合同表-台账(费用管理使用)", description = "权限码：opt:commerce:commercecontract:listEffective")
    @PostMapping("listEffective")
    public ApiResponse<List<CommerceContractListItemResp>> listEffective(@RequestBody @Valid CommerceContractListReq listReq) {
        ApiResponse<List<CommerceContractListItemResp>> result = new ApiResponse<>();

        List<CommerceContractListItemResp> listAllResp = commerceContractService.listEffective(listReq, true);

        result.setData(listAllResp);

        return result;
    }

    @Operation(summary = "招商合同表-详情", description = "权限码：opt:commerce:commercecontract:get")
    @GetMapping(ApiMethod.GET)
    public ApiResponse<CommerceContractGetResp> get(@Valid CommerceContractGetReq getReq) {
        ApiResponse<CommerceContractGetResp> result = new ApiResponse<>();

        CommerceContractGetResp getResp = commerceContractService.get(getReq);

        result.setData(getResp);

        return result;
    }

    /**
     * 根据工单号，只有在工单管理菜单，查看详情时调用
     *
     * @param getReq
     * @return
     */
    @Operation(summary = "招商合同表-详情", description = "权限码：opt:commerce:commercecontract:get")
    @GetMapping("getByOrderCode")
    public ApiResponse<CommerceContractGetResp> getByOrderCode(@Valid CommerceContractGetOrderReq getReq) {
        ApiResponse<CommerceContractGetResp> result = new ApiResponse<>();

        CommerceContractGetResp getResp = commerceContractService.getByOrderCode(getReq);

        result.setData(getResp);

        return result;
    }

    @Operation(summary = "招商合同表-详情", description = "权限码：opt:commerce:commercecontract:getByContractCode")
    @GetMapping("getByContractCode")
    public ApiResponse<CommerceContractGetResp> getByContractCode(@Valid CommerceContractCodeGetReq getReq) {
        ApiResponse<CommerceContractGetResp> result = new ApiResponse<>();

        CommerceContractGetResp getResp = commerceContractService.getByContractCode(getReq);

        result.setData(getResp);

        return result;
    }

    @Operation(summary = "招商合同表-合同传签", description = "权限码：opt:commerce:commercecontract:add")
    @PostMapping(ApiMethod.ADD)
    public ApiResponse<CommerceContractAddResp> add(@RequestBody @Valid CommerceContractAddReq addReq) {
        ApiResponse<CommerceContractAddResp> result = new ApiResponse<>();

        CommerceContractAddResp addResp = commerceContractService.add(addReq);

        result.setData(addResp);

        return result;
    }


    @Operation(summary = "合同传签-AI决策新增",description = "权限码：opt:commerce:commercecontract:aiReviewSubmit")
    @PostMapping("aiReviewSubmit")
    public ApiResponse aiReviewSubmit(@RequestBody @Valid CommerceContractAiReviewSubmitReq submitReq) {

        ApiResponse result = new ApiResponse<>();
        CommerceContractAiReviewSubmitResp submitResp = commerceContractService.aiReviewSubmit(submitReq);
        result.setData(submitResp);
        return result;
    }


    @Operation(summary = "合同传签-AI决策取消",description = "权限码：opt:commerce:commercecontract:aiReviewCancel")
    @PostMapping("aiReviewCancel")
    public ApiResponse aiReviewCancel(@RequestBody @Valid CommerceContractAiReviewCancelReq cancelReq) {
        ApiResponse result = new ApiResponse<>();
        CommerceContractAiReviewCancelResp cancelResp = commerceContractService.aiReviewCancel(cancelReq);
        result.setData(cancelResp);
        return result;
    }


    @Operation(summary = "招商合同表-合同编辑", description = "权限码：opt:commerce:commercecontract:edit")
    @PostMapping(ApiMethod.EDIT)
    public ApiResponse edit(@RequestBody @Valid CommerceContractEditReq editReq) {
        ApiResponse result = new ApiResponse<>();

        commerceContractService.edit(editReq);

        return result;
    }

    @Operation(summary = "招商合同表-合同修正", description = "权限码：opt:commerce:commercecontract:correct")
    @PostMapping("correct")
    public ApiResponse correct(@RequestBody @Valid CommerceContractEditReq editReq) {
        ApiResponse result = new ApiResponse<>();

        commerceContractService.correct(editReq);

        return result;
    }

    @Operation(summary = "招商合同表-合同归档", description = "权限码：opt:commerce:commercecontract:archive")
    @PostMapping("archive")
    public ApiResponse archive(@RequestBody @Valid CommerceContractArchiveReq archiveReq) {
        ApiResponse result = new ApiResponse<>();

        commerceContractService.archive(archiveReq);

        return result;
    }

    @Operation(summary = "招商合同表-合同协议追加", description = "权限码：opt:commerce:commercecontract:addition")
    @PostMapping("addition")
    public ApiResponse addition(@RequestBody @Valid CommerceContractAdditionReq additionReq) {
        ApiResponse result = new ApiResponse<>();

        commerceContractService.addition(additionReq);

        return result;
    }

    @Operation(summary = "招商合同表-历史详情", description = "权限码：opt:commerce:commercecontract:history")
    @PostMapping("history")
    public ApiResponse<List<CommerceContractHistoryItem>> history(@RequestBody @Valid CommerceContractHistoryReq historyReq) {
        ApiResponse result = new ApiResponse<>();

        List<CommerceContractHistoryItem> list = commerceContractService.listHistory(historyReq);

        result.setData(list);

        return result;
    }

    @Operation(summary = "招商合同表-合同传签导入",description = "权限码：opt:commerce:commercecontract:importContract")
    @PostMapping("importContract")
    public ApiResponse<CommerceContractExportResultResp> importContract(@RequestBody @Valid MultipartFile file) throws IOException{
        ApiResponse<CommerceContractExportResultResp> result = new ApiResponse<>();
        CommerceContractExportResultResp errorResult = commerceContractService.importContract(file);
        result.setData(errorResult);
        return result;
    }

    @Operation(summary = "招商合同表-开业交付",description = "权限码：opt:commerce:commercecontract:openDelivery")
    @PostMapping("openDelivery")
    public ApiResponse<CommerceContractExportResultResp> openDelivery(@RequestBody @Valid MultipartFile file) throws IOException{
        ApiResponse<CommerceContractExportResultResp> result = new ApiResponse<>();
        CommerceContractExportResultResp errorResult = commerceContractService.openDelivery(file);
        result.setData(errorResult);
        return result;
    }

    @Operation(summary = "合同传签台账导出", description = "权限码：opt:commerce:commercecontract:export")
	@PostMapping("export")
	public void export(HttpServletResponse response, @RequestBody @Valid CommerceContractListReq exportReq) {
		commerceContractService.export(response, exportReq);
	}

    @Operation(summary = "合同传签租赁开始日期校验", description = "权限码：opt:commerce:commercecontract:checkDate")
    @GetMapping("checkDate")
    public ApiResponse checkDate(@Valid CommerceContractCheckReq checkReq) {
        boolean status = commerceContractService.checkDate(checkReq);
        ApiResponse<Boolean> result = new ApiResponse<>();
        result.setData(status);
        return result;
    }

    @Operation(summary = "正铺合同-广告位合同-应收账单", description = "权限码：opt:commerce:commercecontract:listByReceivable")
    @PostMapping("listByReceivable")
    public ApiResponse<List<CommerceChargeContractListItem>> listByReceivable(@RequestBody @Valid CommerceChargeContractListReq listReq) {
        ApiResponse<List<CommerceChargeContractListItem>> result = new ApiResponse<>();

        List<CommerceChargeContractListItem> listAllResp = commerceContractService.listByReceivable(listReq);

        result.setData(listAllResp);

        return result;
    }

    /**
     * 根据品牌查询合同中供应商数据
     */
    @Operation(summary = "根据品牌ID查询合同中供应商数据")
    @GetMapping("listSupplierByBrand")
    public ApiResponse<CommerceContractSupplierResp> listSupplierByBrand(@Valid Long brandId) {
        ApiResponse<CommerceContractSupplierResp> result = new ApiResponse<>();
        result.setData(commerceContractService.listSupplierByBrand(brandId));

        return result;
    }
}
