package com.xxl.job.executor.service.jobhandler;

import cn.hutool.core.collection.CollectionUtil;
import com.seewin.ems.energy.provider.ElectricityDataProvider;
import com.seewin.ems.energy.req.ElectricityDataByDateDto;
import com.seewin.ems.energy.resp.ElectricityDataWithRoomVo;
import com.seewin.som.commerce.provider.CommerceStoreManageProvider;
import com.seewin.som.commerce.req.*;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.iot.provider.IotFlowDataProvider;
import com.seewin.som.iot.req.IotFlowDataEnterOutDto;
import com.seewin.som.iot.resp.IotFlowDataEnterOutVo;
import com.seewin.som.report.provider.ReportElectricityDataProvider;
import com.seewin.som.report.req.ReportElectricityDataAddDto;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsListVo;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 电表数据处理
 */
@Component
@Slf4j
public class StoreElectricityJob {

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceStoreManageProvider commerceStoreManageProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportElectricityDataProvider reportElectricityDataDataProvider;

    @DubboReference(providedBy = "ems-energy-mgt")
    private ElectricityDataProvider electricityDataProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFlowDataProvider flowDataProvider;

    /**
     * 计算近7天的电表和客流的参考开闭店数据
     */
    @XxlJob("computeReferData")
    public void computeReferData() {
        // 是否指定项目
        Long paramTenantId = null;
        String param = XxlJobHelper.getJobParam();
        log.info("计算近7天的电表和客流的参考开闭店数据:{}", param);
        if (StringUtils.isNotBlank(param)) {
            String[] split = param.split(",");
            if (split.length > 2) {
                paramTenantId = Long.valueOf(split[2]);
            }
        }

        // 同步全生命周期店铺到开闭店管理店铺
        syncRoomData(paramTenantId);

        // 获取开闭店管理数据
        CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
        if (paramTenantId!=null){
            storeDto.setTenantId(paramTenantId);
        }
        List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
        if (CollectionUtil.isNotEmpty(storeList)){
            Set<Long> tenantIdSet = storeList.stream().map(f -> f.getTenantId()).collect(Collectors.toSet());
            for (Long tenantId : tenantIdSet) {
                scopeTenantData(param, tenantId);
            }
        }

        log.info("计算近7天的电表和客流的参考开闭店数据成功...");
    }

    /**
     * 同步店铺数据
     */
    private void syncRoomData(Long tenantId) {
        RoomsListDto roomDto = new RoomsListDto();
        CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
        if (tenantId!=null && tenantId!=0){
            roomDto.setTenantId(tenantId);
            storeDto.setTenantId(tenantId);
        }
        List<RoomsListVo> roomList = roomProvider.list(roomDto);
        List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);

        CommerceStoreManageAddDto storeAddDto = new CommerceStoreManageAddDto();
        if (CollectionUtil.isNotEmpty(storeList)){
            List<Long> roomIdList = storeList.stream().map(f -> f.getRoomId()).collect(Collectors.toList());
            for (RoomsListVo roomsVo : roomList) {
                if (!roomIdList.contains(roomsVo.getId())){
                    saveStoreManage(roomsVo, storeAddDto);
                }
            }
        }else {
            for (RoomsListVo roomsVo : roomList) {
                saveStoreManage(roomsVo, storeAddDto);
            }
        }
    }

    /**
     * 保存店铺信息到开闭店管理表
     * @param roomsVo 店铺信息
     * @param storeAddDto
     */
    private void saveStoreManage(RoomsListVo roomsVo, CommerceStoreManageAddDto storeAddDto) {
        storeAddDto.setTenantId(roomsVo.getTenantId());
        storeAddDto.setTenantName(roomsVo.getTenantName());
        storeAddDto.setEntId(roomsVo.getEntId());
        storeAddDto.setOrgFid(roomsVo.getOrgFid());
        storeAddDto.setOrgFname(roomsVo.getOrgFname());
        storeAddDto.setRoomId(roomsVo.getId());
        storeAddDto.setStoreStatus(2);
        storeAddDto.setCreateBy(roomsVo.getCreateBy());
        storeAddDto.setCreateUser(roomsVo.getCreateUser());
        storeAddDto.setCreateUserName(roomsVo.getCreateUserName());
        commerceStoreManageProvider.add(storeAddDto);
    }

    public void scopeTenantData(String param, Long tenantId) {
        // 先同步客流数据
        scopeFlowData(param, tenantId);

        // 再同步电表数据
        // 获取拉取时间范围
        LocalDate endDay = LocalDate.now().minusDays(1);
        LocalDate startDay = LocalDate.now().minusDays(7);
        if (StringUtils.isNotBlank(param)) {
            String[] split = param.split(",");
            startDay = LocalDate.parse(split[0]);
            endDay = LocalDate.parse(split[1]);
        }

        // 获取日期列表
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(startDay);
        LocalDate tempDate = startDay.plusDays(1);
        while (tempDate.isBefore(endDay) || tempDate.isEqual(endDay)){
            dateList.add(tempDate);
            tempDate = tempDate.plusDays(1);
        }

        // 获取所有店铺数据
        RoomsListDto roomDto = new RoomsListDto();
        if (tenantId!=null){
            roomDto.setTenantId(tenantId);
        }
        List<RoomsListVo> roomList = roomProvider.list(roomDto);

        Map<Long, RoomsListVo> roomMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(roomList)) {
            roomMap = roomList.stream().collect(Collectors.toMap(RoomsListVo::getId, Function.identity()));
        }

        // 获取所有的开闭店管理的店铺列表
        CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
        if (tenantId!=null){
            storeDto.setTenantId(tenantId);
        }
        List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);

        Map<Long, CommerceStoreManageListVo> storeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(storeList)) {
            storeMap = storeList.stream().collect(Collectors.toMap(CommerceStoreManageListVo::getRoomId, Function.identity()));
        }

        // 存储每个店铺上一条记录功率
        Map<Long, BigDecimal> standPowerMap = new HashMap<>();
        Map<Long, BigDecimal> timePowerMap = new HashMap<>();
        Map<Long, BigDecimal> addPowerMap = new HashMap<>();

        // 存储每个店铺的所有标准差、开店时间、闭店时间
        Map<Long, List<BigDecimal>> standardListMap = new HashMap<>();
        Map<Long, List<LocalDateTime>> openTimeListMap = new HashMap<>();
        Map<Long, List<LocalDateTime>> closeTimeListMap = new HashMap<>();

        for (LocalDate localDate : dateList) {
            // 获取电表数据
            ElectricityDataByDateDto getDataDto = new ElectricityDataByDateDto();
            if (tenantId!=null){
                getDataDto.setTenantId(tenantId);
            }
            getDataDto.setStartTime(localDate.atStartOfDay());
            getDataDto.setEndTime(localDate.atTime(23, 59, 59));

            List<ElectricityDataWithRoomVo> elecDataList = electricityDataProvider.getElectricityData(getDataDto);

            if (CollectionUtil.isNotEmpty(elecDataList)){
                // 按门店进行分组
                Map<Long, List<ElectricityDataWithRoomVo>> electricityMap = elecDataList.stream().collect(Collectors.groupingBy(ElectricityDataWithRoomVo::getRoomId));
                for(Map.Entry<Long, List<ElectricityDataWithRoomVo>> entry : electricityMap.entrySet()){
                    Long roomId = entry.getKey();
                    List<ElectricityDataWithRoomVo> roomDataList = entry.getValue().stream()
                            .sorted(Comparator.comparing(ElectricityDataWithRoomVo::getReadTime))
                            .collect(Collectors.toList());

                    // 获取店铺信息
                    RoomsListVo roomsVo = roomMap.get(roomId);
                    CommerceStoreManageListVo storeManageVo = storeMap.get(roomId);
                    if (roomsVo!=null){

                        // 已有参考开店时间的门店则跳过
                        if (storeManageVo!=null && StringUtils.isNotEmpty(storeManageVo.getReferOpenTime())){
                            continue;
                        }

                        // 获取店铺历史的阀值,开店时间,闭店时间列表
                        List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
                        List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
                        List<BigDecimal> standardValueList = standardListMap.get(roomId);
                        if (CollectionUtil.isEmpty(openTimeList)){
                            openTimeList = new ArrayList<>();
                        }
                        if (CollectionUtil.isEmpty(closeTimeList)){
                            closeTimeList = new ArrayList<>();
                        }
                        if (CollectionUtil.isEmpty(standardValueList)){
                            standardValueList = new ArrayList<>();
                        }

                        // 先算出当天的标准差
                        List<BigDecimal> powerDiffValueList = new ArrayList<>();
                        for (ElectricityDataWithRoomVo dataVo : roomDataList) {
                            // 获取店铺上一个功率
                            BigDecimal standLastValue = standPowerMap.get(roomId);
                            BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
                            if (standLastValue==null){
                                powerDiffValueList.add(BigDecimal.ZERO);
                            }else {
                                BigDecimal powerDiffValue = powerValue.subtract(standLastValue);
                                if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
                                    powerDiffValue = BigDecimal.ZERO;
                                }
                                powerDiffValueList.add(powerDiffValue);
                            }
                            standPowerMap.put(roomId, powerValue);
                        }
                        BigDecimal standardValue = getStandardValue(powerDiffValueList);

                        // 存储每一天的标准差
                        standardValueList.add(standardValue);
                        standardListMap.put(roomId, standardValueList);

                        // 利用标准差再算出每天的开店时间和闭店时间是哪条数据
                        Integer openTime = null;
                        Integer closeTime = null;
                        for (int i = 0; i < roomDataList.size(); i++) {
                            // 获取店铺上一个功率
                            BigDecimal lastTimeValue = timePowerMap.get(roomId);
                            // 获取和上一条数据的差值
                            BigDecimal powerDiffValue;
                            ElectricityDataWithRoomVo dataVo = roomDataList.get(i);
                            BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
                            if (lastTimeValue==null){
                                powerDiffValue = BigDecimal.ZERO;
                            }else {
                                powerDiffValue = powerValue.subtract(lastTimeValue);
                                if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
                                    powerDiffValue = BigDecimal.ZERO;
                                }
                            }
                            timePowerMap.put(roomId, powerValue);
                            // 还没有开店时间
                            if (openTime==null){
                                if (powerDiffValue.compareTo(standardValue)>0){
                                    if (i==0){
                                        openTime = 0;
                                    }else {
                                        openTime = i-1;
                                    }
                                }
                            }else {
                                // 有了开业时间求闭店时间
                                if (closeTime==null && powerDiffValue.compareTo(standardValue)<0){
                                    if (i==0){
                                        closeTime = 0;
                                    }else {
                                        closeTime = i-1;
                                    }
                                }
                            }
                        }

                        // 入库当天的电表数据
                        for (int i = 0; i < roomDataList.size(); i++) {
                            ReportElectricityDataAddDto electricityAddDto = new ReportElectricityDataAddDto();
                            // 获取店铺上一个功率
                            BigDecimal lastAddValue = addPowerMap.get(roomId);

                            ElectricityDataWithRoomVo dataVo = roomDataList.get(i);

                            electricityAddDto.setDeviceId(dataVo.getDeviceSn());
                            electricityAddDto.setDeviceName(dataVo.getDeviceName());

                            electricityAddDto.setTenantId(roomsVo.getTenantId());
                            electricityAddDto.setRoomId(roomId);
                            electricityAddDto.setRoomNo(roomsVo.getName());
                            electricityAddDto.setStoreId(roomsVo.getStoreId());
                            electricityAddDto.setBrandId(roomsVo.getBrandId());
                            electricityAddDto.setBrandName(roomsVo.getBrandName());

                            electricityAddDto.setCollectTime(dataVo.getReadTime());
                            electricityAddDto.setCollectDiffTime(null);

                            BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
                            electricityAddDto.setPowerValue(powerValue);

                            // 获取和上一条记录的差值
                            if (lastAddValue==null){
                                electricityAddDto.setPowerDiffValue(BigDecimal.ZERO);
                            }else {
                                BigDecimal powerDiffValue = powerValue.subtract(lastAddValue);
                                if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
                                    powerDiffValue = BigDecimal.ZERO;
                                }
                                electricityAddDto.setPowerDiffValue(powerDiffValue);
                            }
                            addPowerMap.put(roomId, powerValue);

                            electricityAddDto.setCreateBy(roomsVo.getCreateBy());
                            electricityAddDto.setCreateUser(roomsVo.getCreateUser());
                            electricityAddDto.setCreateUserName(roomsVo.getCreateUserName());

                            if (closeTime!=null && closeTime == i){
                                electricityAddDto.setStoreFlag(1);
                                closeTimeList.add(dataVo.getReadTime());
                                closeTimeListMap.put(roomId, closeTimeList);
                            }

                            if (openTime!=null && openTime == i){
                                electricityAddDto.setStoreFlag(0);
                                openTimeList.add(dataVo.getReadTime());
                                openTimeListMap.put(roomId, openTimeList);
                            }
                            reportElectricityDataDataProvider.add(electricityAddDto);
                        }
                    }
                }
            }
        }

        // 给每个店铺赋值 参考值、开店时间、闭店时间、开闭店时长
        if (CollectionUtil.isNotEmpty(storeList)){
            for (CommerceStoreManageListVo storeVo : storeList) {

                // 已有参考开店时间的门店则跳过
                if (StringUtils.isNotEmpty(storeVo.getReferOpenTime())){
                    continue;
                }

                CommerceStoreManageEditDto editDto = new CommerceStoreManageEditDto();
                editDto.setId(storeVo.getId());

                Long roomId = storeVo.getRoomId();

                // 求所有标准差的平均值
                List<BigDecimal> standardList = standardListMap.get(roomId);
                BigDecimal referValue = getStandardAvgValue(standardList);
                editDto.setReferValue(referValue);

                // 近7天的开店时间小于5天忽略掉该门店
                List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
                if (CollectionUtil.isEmpty(openTimeList) || openTimeList.size() < 5){
                    continue;
                }

                // 求开店时间的平均值
                String referOpenTime = getTimeAvgValue(openTimeList);
                editDto.setReferOpenTime(referOpenTime);

                // 求开店时间的平均值
                List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
                String referCloseTime = getTimeAvgValue(closeTimeList);
                editDto.setReferCloseTime(referCloseTime);

                // 获取开店时长
                BigDecimal timeLength = getTimeLength(referOpenTime, referCloseTime);
                editDto.setReferOpenLength(timeLength);

                // 赋值参考类型为电表
                if (referValue!=null && referValue.compareTo(BigDecimal.ZERO) > 0){
                    editDto.setReferType(0);
                    commerceStoreManageProvider.edit(editDto);
                }
            }
        }
    }

    /**
     * 计算客流的参考时间
     */
    private void scopeFlowData(String param, Long tenantId) {
        // 获取时间范围
        LocalDate endDay = LocalDate.now().minusDays(1);
        LocalDate startDay = LocalDate.now().minusDays(7);
        if (StringUtils.isNotBlank(param)) {
            String[] split = param.split(",");
            startDay = LocalDate.parse(split[0]);
            endDay = LocalDate.parse(split[1]);
        }

        // 获取日期列表
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(startDay);
        LocalDate tempDate = startDay.plusDays(1);
        while (tempDate.isBefore(endDay) || tempDate.isEqual(endDay)){
            dateList.add(tempDate);
            tempDate = tempDate.plusDays(1);
        }

        // 获取所有店铺数据
        RoomsListDto roomDto = new RoomsListDto();
        if (tenantId!=null){
            roomDto.setTenantId(tenantId);
        }
        List<RoomsListVo> roomList = roomProvider.list(roomDto);

        // 店铺数据按tenantId-fcode组成key
        Map<String, RoomsListVo> roomCodeMap = new HashMap<>();
        if (CollectionUtil.isNotEmpty(roomList)) {
            roomCodeMap = roomList.stream().collect(Collectors.toMap(
                    room -> room.getTenantId() + "-" + room.getFcode(),
                    room -> room,
                    (existing, replacement) -> existing
            ));
        }

        // 存储每个店铺的所有开店时间、闭店时间
        Map<Long, List<LocalDateTime>> openTimeListMap = new HashMap<>();
        Map<Long, List<LocalDateTime>> closeTimeListMap = new HashMap<>();

        for (LocalDate localDate : dateList) {
            // 获取表名
            int monthValue = localDate.getMonthValue();
            String monthTwoDigits = String.format("%02d", monthValue);
            String tableName = "som_iot.som_iot_flow_data" + "_" + monthTwoDigits;

            // 进出客流请求参数
            IotFlowDataEnterOutDto enterOutDto = new IotFlowDataEnterOutDto();
            if (tenantId != null) {
                enterOutDto.setTenantId(tenantId);
            }
            enterOutDto.setStartCollectTime(localDate.atStartOfDay());
            enterOutDto.setEndCollectTime(localDate.atTime(23, 59, 59));
            enterOutDto.setTableName(tableName);

            // 获取进客流数据
            List<IotFlowDataEnterOutVo> enterVoList = flowDataProvider.getEnterCountFlowData(enterOutDto);
            if (CollectionUtil.isNotEmpty(enterVoList)) {
                for (IotFlowDataEnterOutVo enterVo : enterVoList) {
                    String roomCode = enterVo.getRoomCode();
                    LocalDateTime openTime = enterVo.getCollectTime();
                    RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
                    if (roomsListVo != null) {
                        Long roomId = roomsListVo.getId();

                        // 获取店铺历史的开店时间列表
                        List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
                        if (CollectionUtil.isEmpty(openTimeList)) {
                            openTimeList = new ArrayList<>();
                        }

                        openTimeList.add(openTime);
                        openTimeListMap.put(roomId, openTimeList);
                    }
                }
            }

            // 获取出客流数据
            List<IotFlowDataEnterOutVo> outVoList = flowDataProvider.getOutCountFlowData(enterOutDto);
            if (CollectionUtil.isNotEmpty(outVoList)) {
                for (IotFlowDataEnterOutVo outVo : outVoList) {
                    String roomCode = outVo.getRoomCode();
                    LocalDateTime closeTime = outVo.getCollectTime();
                    RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
                    if (roomsListVo != null) {
                        Long roomId = roomsListVo.getId();

                        // 获取店铺历史的闭店时间列表
                        List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
                        if (CollectionUtil.isEmpty(closeTimeList)) {
                            closeTimeList = new ArrayList<>();
                        }

                        closeTimeList.add(closeTime);
                        closeTimeListMap.put(roomId, closeTimeList);
                    }
                }
            }
        }

        // 获取所有的开闭店管理的店铺列表
        CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
        if (tenantId!=null){
            storeDto.setTenantId(tenantId);
        }
        List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);

        // 给每个店铺赋值 开店时间、闭店时间、开闭店时长
        if (CollectionUtil.isNotEmpty(storeList)){
            for (CommerceStoreManageListVo storeVo : storeList) {

                // 已有参考开店时间的门店则跳过
                if (StringUtils.isNotEmpty(storeVo.getReferFlowOpenTime())){
                    continue;
                }

                CommerceStoreManageEditDto editDto = new CommerceStoreManageEditDto();
                editDto.setId(storeVo.getId());

                Long roomId = storeVo.getRoomId();

                // 近7天的开店时间小于5天忽略掉该门店
                List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
                if (CollectionUtil.isEmpty(openTimeList) || openTimeList.size() < 5){
                    continue;
                }

                // 求开店时间的平均值
                String referOpenTime = getTimeAvgValue(openTimeList);
                editDto.setReferFlowOpenTime(referOpenTime);

                // 求开店时间的平均值
                List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
                String referCloseTime = getTimeAvgValue(closeTimeList);
                editDto.setReferFlowCloseTime(referCloseTime);

                // 获取开店时长
                BigDecimal timeLength = getTimeLength(referOpenTime, referCloseTime);
                editDto.setReferFlowOpenLength(timeLength);

                // 赋值参考类型为客流
                if (StringUtils.isNotEmpty(referOpenTime)){
                    if (storeVo.getReferType()==null){
                        editDto.setReferType(1);
                    }
                    commerceStoreManageProvider.edit(editDto);
                }
            }
        }
    }

    /**
     * 获取开店时长
     * @param openTime
     * @param closeTime
     * @return
     */
    private BigDecimal getTimeLength(String openTime, String closeTime) {
        if (StringUtils.isNotEmpty(openTime) && StringUtils.isNotEmpty(closeTime)){
            long openTotalMinutes = parseTimeToMinutes(openTime);
            long closeTotalMinutes = parseTimeToMinutes(closeTime);
            long minuteDifference = closeTotalMinutes - openTotalMinutes;
            if (minuteDifference<0){
                return BigDecimal.ZERO;
            }
            return new BigDecimal(minuteDifference)
                    .divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
        }
        return null;
    }

    private long parseTimeToMinutes(String timeStr) {
        String[] parts = timeStr.split(":");
        int hours = Integer.parseInt(parts[0]);
        int minutes = Integer.parseInt(parts[1]);
        return hours * 60L + minutes;
    }

    /**
     * 获取标准差平均值
     * @param standardList
     * @return
     */
    private BigDecimal getStandardAvgValue(List<BigDecimal> standardList) {
        BigDecimal referValue = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(standardList)){
            BigDecimal sum = BigDecimal.ZERO;
            for (BigDecimal value : standardList) {
                sum = sum.add(value);
            }
            BigDecimal count = new BigDecimal(standardList.size());
            referValue = sum.divide(count, 2, RoundingMode.HALF_UP);
        }
        return referValue;
    }

    /**
     * 获取时间平均值
     * @param timeList
     * @return
     */
    private String getTimeAvgValue(List<LocalDateTime> timeList) {
        if (CollectionUtil.isNotEmpty(timeList)){
            // 求总分钟
            long totalMinutes = 0;
            for (LocalDateTime dateTime : timeList) {
                int hour = dateTime.getHour();
                int minute = dateTime.getMinute();
                totalMinutes += hour * 60 + minute;
            }
            // 计算平均分钟数
            long averageMinutes = totalMinutes / timeList.size();
            // 转换回小时和分钟
            int hours = (int) (averageMinutes / 60);
            int minutes = (int) (averageMinutes % 60);
            // 格式化为hh:mm
            return String.format("%02d:%02d", hours, minutes);
        }
        return null;
    }

    /**
     * 获取标准差
     * @param numbers 数值集合
     * @return
     */
    private static BigDecimal getStandardValue(List<BigDecimal> numbers) {
        if (numbers == null || numbers.isEmpty()) {
            return BigDecimal.ZERO;
        }
        int size = numbers.size();
        BigDecimal sum = numbers.stream()
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal mean = sum.divide(new BigDecimal(size), MathContext.DECIMAL128);
        BigDecimal sumOfSquares = numbers.stream()
                .map(num -> num.subtract(mean).pow(2))
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal variance = sumOfSquares.divide(new BigDecimal(size), MathContext.DECIMAL128);
        return variance.sqrt(MathContext.DECIMAL128).setScale(2, RoundingMode.HALF_UP);
    }

}