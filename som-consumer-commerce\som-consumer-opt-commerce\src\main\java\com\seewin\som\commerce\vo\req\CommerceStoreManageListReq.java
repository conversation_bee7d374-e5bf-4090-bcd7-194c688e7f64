package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import com.seewin.consumer.data.ApiPageReq;

/**
 * <p>
 * 开闭店管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageListReq extends ApiPageReq {

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    private String orgFname;

    /**
     * 铺位id
     */
    @Schema(description = "铺位id")
    private Long roomId;
    /**
     * 铺位号
     */
    @Schema(description = "铺位号")
    private String roomNo;
    /**
     * 铺位状态： 1：空置； 2：在营； 3：已签约
     */
    @Schema(description = "铺位状态： 1：空置； 2：在营； 3：已签约")
    private Integer status;
    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private String storeId;
    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;
    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    @Schema(description = "门店状态（字典：未交付 no_delivered  未开业 no_open  已开业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）")
    private String roomStatus;
    /**
     * 参考开店时间
     */
    @Schema(description = "参考开店时间")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @Schema(description = "参考闭店时间")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @Schema(description = "参考开店时长(小时)")
    private BigDecimal referOpenLength;

    /**
     * 参考客流开店时间
     */
    @Schema(description = "参考客流开店时间")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @Schema(description = "参考客流闭店时间")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @Schema(description = "参考客流开店时长(小时)")
    private BigDecimal referFlowOpenLength;

    /**
     * 电表开店时间
     */
    @Schema(description = "电表开店时间")
    private String electricityOpenTime;

    /**
     * 客流仪开店时间
     */
    @Schema(description = "客流仪开店时间")
    private String flowOpenTime;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    @Schema(description = "开闭店状态： 0：正常； 1：异常 2：无状态-")
    private Integer storeStatus;

    /**
     * 消息通知开关： 0：关； 1：开
     */
    @Schema(description = "消息通知开关： 0：关； 1：开")
    private Integer messageNotify;

    /**
     * 巡查显示开关： 0：关； 1：开
     */
    @Schema(description = "巡查显示开关： 0：关； 1：开")
    private Integer patrolShow;

    /**
     * 异常时间间隔(分钟)
     */
    @Schema(description = "异常时间间隔(分钟)")
    private Integer timePeriod;

    /**
     * 参考值
     */
    @Schema(description = "参考值")
    private BigDecimal referValue;

    /**
     * 参考类型 0-电表 1-客流
     */
    @Schema(description = "参考类型 0-电表 1-客流")
    private Integer referType;

    /**
     * 最后时间
     */
    @Schema(description = "最后时间")
    private String lastPowerTime;

    /**
     * 最后数值
     */
    @Schema(description = "最后数值")
    private BigDecimal lastPowerValue;

}
