package com.seewin.som.commerce.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 开闭店管理明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@TableName("som_commerce_store_manage_detail")
public class CommerceStoreManageDetail implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 开闭店管理id
     */
    @TableField("store_manage_id")
    private Long storeManageId;

    /**
     * 门店id
     */
    @TableField("store_id")
    private String storeId;

    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    @TableField("room_status")
    private String roomStatus;

    /**
     * 品牌id
     */
    @TableField("brand_id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @TableField("brand_name")
    private String brandName;

    /**
     * 参考开店时间
     */
    @TableField("refer_open_time")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @TableField("refer_close_time")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @TableField("refer_open_length")
    private BigDecimal referOpenLength;

    /**
     * 参考客流开店时间
     */
    @TableField("refer_flow_open_time")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @TableField("refer_flow_close_time")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @TableField("refer_flow_open_length")
    private BigDecimal referFlowOpenLength;

    /**
     * 电表开店时间
     */
    @TableField("electricity_open_time")
    private String electricityOpenTime;

    /**
     * 电表闭店时间
     */
    @TableField("electricity_close_time")
    private String electricityCloseTime;

    /**
     * 电表开店时长(小时)
     */
    @TableField("electricity_open_length")
    private BigDecimal electricityOpenLength;

    /**
     * 客流仪开店时间
     */
    @TableField("flow_open_time")
    private String flowOpenTime;

    /**
     * 客流仪闭店时间
     */
    @TableField("flow_close_time")
    private String flowCloseTime;

    /**
     * 客流仪开店时长(小时)
     */
    @TableField("flow_open_length")
    private BigDecimal flowOpenLength;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    @TableField("store_status")
    private Integer storeStatus;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;


}
