package com.seewin.som.commerce.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

/**
 * <p>
 * 招商合同表-ai决策
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class CommerceContractAiReviewSubmitReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractCode;

    /**
     * 用户输入
     */
    @Schema(description = "用户输入")
    private String userInput;
    /**
     * System输入
     */
    @Schema(description = "System输入")
    private String rule;


    // 1. 字段映射表和rule写死
    public static final Map<String, String> FIELD_MAP = new HashMap<>();
    static {
        FIELD_MAP.put("签约品牌名称", "brandName");
        FIELD_MAP.put("商业不动产名称", "tenantName");
        FIELD_MAP.put("签约类型", "contractType");
        FIELD_MAP.put("签约品牌一级品类", "brandCategoryName");
        FIELD_MAP.put("铺位号", "name");//string
        FIELD_MAP.put("计租面积", "rentArea");//Double
        FIELD_MAP.put("月租金单价", "monthPriceContract");
        FIELD_MAP.put("月物业管理费单价", "monthManagePriceContract");
        FIELD_MAP.put("月运营管理费单价", "monthOperatePriceContract");
        FIELD_MAP.put("商户交付日期", "deliveryDate");//LocalDate
//        FIELD_MAP.put("签约年限", "yearLimitContract");
        FIELD_MAP.put("其他费用单价（元/㎡）", "otherPriceContract");
        FIELD_MAP.put("其他费用总额（元）", "otherFeeContract");
//        FIELD_MAP.put("租金年递增率（%）", "incrementalRate");
        FIELD_MAP.put("装修免租期（天）", "feeFreeContract"); //Integer
        FIELD_MAP.put("综合管理费（元）", "compositeManageFeeContract");
        FIELD_MAP.put("租赁保证金（元）", "rentBailFeeContract");
        FIELD_MAP.put("保证金是否突破", "bailBreak");
        FIELD_MAP.put("装修押金（元）", "decorationBailFeeContract");
        FIELD_MAP.put("POS机租金（元）", "posRentFee");
        FIELD_MAP.put("POS机押金（元）", "posBailFee");
        FIELD_MAP.put("装修期物业管理费（元）", "decorationPropertyFee");
        FIELD_MAP.put("装修期运营管理费（元）", "decorationOperateFee");
        FIELD_MAP.put("垃圾清运费（元）", "cleanFee");

    }

    // rule写死
    public static final String RULE_JSON = "{"
            + "\"签约品牌名称\":\"\","
            + "\"商业不动产名称\":\"\","
            + "\"签约类型\":\"\","
            + "\"签约品牌一级品类\":\"\","
            + "\"铺位号\":\"\","
            + "\"计租面积\":\"\","
            + "\"月租金单价\":\"\","
            + "\"月物业管理费单价\":\"\","
            + "\"月运营管理费单价\":\"\","
            + "\"商户交付日期\":\"\","
            + "\"签约年限\":\"\","
            + "\"其他费用单价（元/㎡）\":\"\","
            + "\"其他费用总额（元）\":\"\","
//            + "\"租金年递增（率）\":\"\","
            + "\"装修免租期（天）\":\"\","
            + "\"综合管理费（元）\":\"\","
            + "\"租赁保证金（元）\":\"\","
            + "\"保证金是否突破\":\"\","
            + "\"装修押金（元）\":\"\","
            + "\"POS机租金（元）\":\"\","
            + "\"POS机押金（元）\":\"\","
            + "\"装修期物业管理费（元）\":\"\","
            + "\"装修期运营管理费（元）\":\"\","
            + "\"垃圾清运费（元）\":\"\""
            + "}";


}
