package com.seewin.som.commerce.vo.resp;

import com.cscec1b.consumer.vo.FileResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractSupplierItem implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "供应商id")
    private Long supplierId;

}
