package com.seewin.som.commerce.vo.resp;

import com.seewin.som.commerce.resp.CommerceContractPrintOaVo;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintReceiptSealResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "应答状态")
    private Integer code = 0;

    @Schema(description = "应答消息")
    private String msg = "";

    @Schema(description = "返回下载链接地址")
    private List<String> data;


}
