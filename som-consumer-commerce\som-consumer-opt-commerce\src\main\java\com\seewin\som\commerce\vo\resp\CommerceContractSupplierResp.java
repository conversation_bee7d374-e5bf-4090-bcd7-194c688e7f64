package com.seewin.som.commerce.vo.resp;

import com.cscec1b.consumer.vo.FileResp;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Collections;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Getter
@Setter
public class CommerceContractSupplierResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 品牌状态 但是没有开过店或开店时间不满30天
     */
    @Schema(description = "品牌状态 没有开过店或开店时间不满30天=true, else  false")
    private boolean  brandStatus;

    /**
     * 供应商下拉框数据
     */
    @Schema(description = "供应商下拉框数据")
    private List<CommerceContractSupplierItem> supplierList;

}
