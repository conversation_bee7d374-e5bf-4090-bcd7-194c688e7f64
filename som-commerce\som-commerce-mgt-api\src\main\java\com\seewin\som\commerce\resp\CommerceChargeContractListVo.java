package com.seewin.som.commerce.resp;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 广告位合同表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-10
 */
@Getter
@Setter
public class CommerceChargeContractListVo implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 合同类型（0-广告位、1-多经点位）
     */
    private Integer shopType;
    /**
     * 广告多经ID
     */
    private Long roomId;

    /**
     * 广告多经编号
     */
    private String roomName;

    /**
     * 签约品牌名称
     */
    private String brandName;

    private Long supplierId;

    private String supplierName;

    private LocalDate rentStartDate;


}
