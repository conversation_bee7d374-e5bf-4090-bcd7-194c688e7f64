package com.seewin.som.report.vo.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import com.seewin.consumer.data.ApiPageReq;

/**
 * <p>
 * 品牌聚类结果信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ReportBrandClusterResultsListReq extends ApiPageReq {

    /**
     * 业态
     */
    @Schema(description = "业态")
    private String commercialTypeName;


}
