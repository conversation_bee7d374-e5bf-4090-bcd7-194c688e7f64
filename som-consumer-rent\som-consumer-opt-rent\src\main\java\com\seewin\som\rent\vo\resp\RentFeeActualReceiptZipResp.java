package com.seewin.som.rent.vo.resp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <p>
 * 实收收据打印
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-26
 */
@Getter
@Setter
public class RentFeeActualReceiptZipResp implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "账期年")
    private Integer periodYear;

    @Schema(description = "账期月")
    private Integer periodMonth;

    @Schema(description = "账期日")
    private Integer periodDay;

    @Schema(description = "供应商名称")
    private String supplierName;

    @Schema(description = "铺位号")
    private String shopNo;

    @Schema(description = "费用名称")
    private String feeName;

    @Schema(description = "实收金额（含税）")
    private BigDecimal actualAmount;

    @Schema(description = "出纳")
    private String cashier;

    @Schema(description = "经手人")
    private String handler;


    @Schema(description = "门店id")
    private Long roomId;

    @Schema(description = "收费项目id")
    private Long chargingItemId;

    @Schema(description = "合同编码")
    private String contractCode;

    @Schema(description = "项目名")
    private String tenantName;

    @Schema(description = "序列号")
    private String serialNumber;

}
