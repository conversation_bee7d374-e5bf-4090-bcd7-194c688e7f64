<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile default="true" name="Default" enabled="true" />
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="som-iot-mgt" />
        <module name="som-consumer-opt-report" />
        <module name="som-report-mgt-api" />
        <module name="som-consumer-open-report" />
        <module name="som-commerce-mgt-api" />
        <module name="som-rent-mgt" />
        <module name="som-rent-mgt-api" />
        <module name="som-iot-mgt-api" />
        <module name="som-consumer-evt-commerce" />
        <module name="som-consumer-cus-iot" />
        <module name="som-space-mgt-api" />
        <module name="som-consumer-opt-commerce" />
        <module name="som-consumer-eip-space" />
        <module name="som-consumer-opt-iot" />
        <module name="som-job-executor" />
        <module name="som-consumer-data-iot" />
        <module name="som-consumer-cus-rent" />
        <module name="som-consumer-evt-rent" />
        <module name="som-consumer-cus-commerce" />
        <module name="som-consumer-eip-commerce" />
        <module name="som-space-mgt" />
        <module name="som-report-mgt" />
        <module name="som-consumer-opt-rent" />
        <module name="som-consumer-cus-report" />
        <module name="seewin-gateway" />
        <module name="som-consumer-cus-space" />
        <module name="som-consumer-opt-space" />
        <module name="som-commerce-mgt" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="ibuilds-job-executor" target="1.5" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="ibuilds-consumer-space" options="-parameters" />
      <module name="ibuilds-executor" options="-parameters" />
      <module name="ibuilds-space" options="-parameters" />
      <module name="route" options="-parameters" />
      <module name="seewin-gateway" options="-parameters" />
      <module name="som-commerce" options="-parameters" />
      <module name="som-commerce-mgt" options="-parameters" />
      <module name="som-commerce-mgt-api" options="-parameters" />
      <module name="som-consumer-commerce" options="-parameters" />
      <module name="som-consumer-cus-commerce" options="-parameters" />
      <module name="som-consumer-cus-iot" options="-parameters" />
      <module name="som-consumer-cus-rent" options="-parameters" />
      <module name="som-consumer-cus-report" options="-parameters" />
      <module name="som-consumer-cus-space" options="-parameters" />
      <module name="som-consumer-data-iot" options="-parameters" />
      <module name="som-consumer-eip-commerce" options="-parameters" />
      <module name="som-consumer-eip-space" options="-parameters" />
      <module name="som-consumer-evt-commerce" options="-parameters" />
      <module name="som-consumer-evt-rent" options="-parameters" />
      <module name="som-consumer-iot" options="-parameters" />
      <module name="som-consumer-open-report" options="-parameters" />
      <module name="som-consumer-opt-commerce" options="-parameters" />
      <module name="som-consumer-opt-iot" options="-parameters" />
      <module name="som-consumer-opt-rent" options="-parameters" />
      <module name="som-consumer-opt-report" options="-parameters" />
      <module name="som-consumer-opt-space" options="-parameters" />
      <module name="som-consumer-rent" options="-parameters" />
      <module name="som-consumer-report" options="-parameters" />
      <module name="som-consumer-space" options="-parameters" />
      <module name="som-executor" options="-parameters" />
      <module name="som-iot" options="-parameters" />
      <module name="som-iot-mgt" options="-parameters" />
      <module name="som-iot-mgt-api" options="-parameters" />
      <module name="som-job-executor" options="-parameters" />
      <module name="som-rent" options="-parameters" />
      <module name="som-rent-mgt" options="-parameters" />
      <module name="som-rent-mgt-api" options="-parameters" />
      <module name="som-report" options="-parameters" />
      <module name="som-report-mgt" options="-parameters" />
      <module name="som-report-mgt-api" options="-parameters" />
      <module name="som-space" options="-parameters" />
      <module name="som-space-mgt" options="-parameters" />
      <module name="som-space-mgt-api" options="-parameters" />
    </option>
  </component>
</project>