package com.seewin.som.commerce.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.commerce.req.CommerceContractPrintAddDto;
import com.seewin.som.commerce.req.CommerceContractPrintEditDto;
import com.seewin.som.commerce.req.CommerceContractPrintListDto;
import com.seewin.som.commerce.resp.CommerceContractPrintAddVo;
import com.seewin.som.commerce.resp.CommerceContractPrintGetVo;
import com.seewin.som.commerce.resp.CommerceContractPrintListVo;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 招商合同打印表 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface CommerceContractPrintProvider {

    /**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<CommerceContractPrintListVo> page(PageQuery<CommerceContractPrintListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<CommerceContractPrintListVo> list(CommerceContractPrintListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(CommerceContractPrintListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    CommerceContractPrintGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    CommerceContractPrintGetVo get(CommerceContractPrintListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    CommerceContractPrintAddVo add(CommerceContractPrintAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(CommerceContractPrintEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(CommerceContractPrintListDto dto) throws ServiceException;

    CommerceContractPrintGetVo getBasicInfoByContractCode(String contractCode);

    void updateSignStatusBySignFlowId(String signFlowId, Integer signStatus);

    String selectSealId(Integer signCompanyCode,Integer sealTypeCode);

    Map<String, Object> getSignInfoBySignCompanyCode(Integer signCompanyCode);

    Integer selectCompanyCodeByLessor(String lessor);

    CommerceContractPrintGetVo selectSignInfoBySignFlowId(String signFlowId);

    List<CommerceContractPrintListVo> getOtherFileById(Long id);

    List<CommerceContractPrintGetVo> getAllFileBySameContractId(String sameContractId);

    void batchUpdateTemplateId(Long newTemplateId,  Long templateId);

    Map<String, Object> selectSealIdByOaUploadReq(String signCompanyName,String sealTypeName);
}
