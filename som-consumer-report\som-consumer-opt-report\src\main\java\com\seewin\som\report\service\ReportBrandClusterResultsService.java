package com.seewin.som.report.service;

import com.seewin.consumer.vo.PageResp;
import com.seewin.som.report.vo.req.*;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsListItem;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsGetResp;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsAddResp;

import java.util.List;

/**
 * <p>
 * 品牌聚类结果信息表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface ReportBrandClusterResultsService {
    
    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    PageResp<ReportBrandClusterResultsListItem> page(ReportBrandClusterResultsListReq listReq);

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    ReportBrandClusterResultsGetResp get(ReportBrandClusterResultsGetReq getReq);

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    ReportBrandClusterResultsAddResp add(ReportBrandClusterResultsAddReq addReq);

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    void edit(ReportBrandClusterResultsEditReq editReq);

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    void del(ReportBrandClusterResultsDelReq delReq);

    List<ReportBrandClusterResultsListItem> list(ReportBrandClusterResultsListReq listReq);

}
