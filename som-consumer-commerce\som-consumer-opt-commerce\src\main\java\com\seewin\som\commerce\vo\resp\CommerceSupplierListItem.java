package com.seewin.som.commerce.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 零售供应商表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Getter
@Setter
public class CommerceSupplierListItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 租户名称
     */
    @Schema(description = "租户名称")
    private String tenantName;
    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;
    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    private String orgFid;
    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    private String orgFname;
    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String name;
    /**
     * 1.身份证、2.营业执照

     */
    /**
     * 品牌类别，、分割
     */
    @Schema(description = "品牌名称列表")
    private String brandList;
    @Schema(description = "1.身份证、2.营业执照 ")
    private Integer certificateType;
    /**
     * 零售供应商身份证地址
     */
    @Schema(description = "零售供应商身份证地址")
    private String cardAddress;
    /**
     * 零售供应商身份证证件号
     */
    @Schema(description = "零售供应商身份证证件号")
    private String cardNumber;
    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    private String contactName;
    /**
     * 联系人岗位，数据字典：contact_position
     */
    @Schema(description = "联系人岗位，数据字典：contact_position")
    private String contactJob;
    /**
     * 联系人方式（号码）
     */
    @Schema(description = "联系人方式（号码）")
    private String contactPhon;
    /**
     * 营业执照公司名称
     */
    @Schema(description = "营业执照公司名称")
    private String companyName;
    /**
     * 营业执照统一社会信用代码
     */
    @Schema(description = "营业执照统一社会信用代码")
    private String socialCreditCode;
    /**
     * 营业执照法定代表人
     */
    @Schema(description = "营业执照法定代表人")
    private String legalRepresentative;
    /**
     * 在营开店数
     */
    @Schema(description = "在营开店数")
    private Integer storeNum;
    /**
     * 开店城市，、分隔
     */
    @Schema(description = "开店城市，、分隔")
    private String citys;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    /**
     * 税号
     */
    @Schema(description = "税号")
    private String taxNo;

    @Schema(description = "零售供应商质量事件")
    private Integer qualityNum;

    @Schema(description = "零售商安全事件")
    private Integer safeNum;

    @Schema(description = "零售商供应商违约事件")
    private Integer breakNum;
    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createBy;
    /**
     * 创建人账号/手机号
     */
    @Schema(description = "创建人账号/手机号")
    private String createUser;
    /**
     * 创建人姓名/昵称
     */
    @Schema(description = "创建人姓名/昵称")
    private String createUserName;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    private Long updateBy;
    /**
     * 修改人账号/手机号
     */
    @Schema(description = "修改人账号/手机号")
    private String updateUser;
    /**
     * 修改人姓名/昵称
     */
    @Schema(description = "修改人姓名/昵称")
    private String updateUserName;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;

    @Schema(description = "审批状态: 2 审批中，3 已通过，4 已驳回")
    private Integer approveStatus;

    @Schema(description = "审批时间")
    private LocalDate approveDate;

    @Schema(description = "敏感数据权限 0-无权限 1-有权限")
    private Integer sensitiveDataAuth;

    @Schema(description = "报税人类型 1.一般纳税人 2.小规模纳税人 3.个人 ")
    private Integer taxPeopleType;

}
