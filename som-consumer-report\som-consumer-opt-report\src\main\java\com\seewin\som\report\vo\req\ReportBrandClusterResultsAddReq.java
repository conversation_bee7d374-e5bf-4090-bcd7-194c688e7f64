package com.seewin.som.report.vo.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;

import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 品牌聚类结果信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ReportBrandClusterResultsAddReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 簇序号
     */
    @Schema(description = "簇序号")
    @NotNull(message = "簇序号不能为空")
    private Integer cluster;

    /**
     * 业态
     */
    @Schema(description = "业态")
    @NotBlank(message = "业态不能为空")
    @Size(max=255,message = "业态最大长度不能超过255")
    private String commercialTypeName;

    /**
     * 平均销售坪效
     */
    @Schema(description = "平均销售坪效")
    private BigDecimal salesPerSquareFootMean;

    /**
     * 平均实用面积
     */
    @Schema(description = "平均实用面积")
    private BigDecimal actualAreaMean;

    /**
     * 平均销售笔数
     */
    @Schema(description = "平均销售笔数")
    private BigDecimal totalOrderMean;

    /**
     * 平均客单价
     */
    @Schema(description = "平均客单价")
    private BigDecimal averageOrderValueMean;

    /**
     * 平均租金坪效
     */
    @Schema(description = "平均租金坪效")
    private BigDecimal rentPerSquareFootMean;

    /**
     * 平均租售比
     */
    @Schema(description = "平均租售比")
    private BigDecimal rentToSalesRatioMean;

    /**
     * 平均进店转化率
     */
    @Schema(description = "平均进店转化率")
    private BigDecimal enterConversionRateMean;


}
