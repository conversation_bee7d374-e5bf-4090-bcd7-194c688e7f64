package com.seewin.som.commerce.provider.impl;

import com.seewin.model.base.User;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.commerce.entity.CommerceContractPrint;
import com.seewin.som.commerce.req.CommerceContractPrintAddDto;
import com.seewin.som.commerce.req.CommerceContractPrintEditDto;
import com.seewin.som.commerce.req.CommerceContractPrintListDto;
import com.seewin.som.commerce.resp.CommerceContractPrintAddVo;
import com.seewin.som.commerce.resp.CommerceContractPrintGetVo;
import com.seewin.som.commerce.resp.CommerceContractPrintListVo;
import com.seewin.util.bean.BeanUtils;


import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.seewin.som.commerce.service.CommerceContractPrintService;
import com.seewin.som.commerce.provider.CommerceContractPrintProvider;
/**
 * <p>
 * 招商合同打印表 API接口实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@DubboService
public class CommerceContractPrintProviderImpl implements CommerceContractPrintProvider{

	@Autowired
	private CommerceContractPrintService commerceContractPrintService;
	
	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public PageResult<CommerceContractPrintListVo> page(PageQuery<CommerceContractPrintListDto> pageQuery) throws ServiceException
    {
    	CommerceContractPrintListDto dto = pageQuery.getQueryDto();

        //设置分页
        Page<CommerceContractPrint> page = new Page<>(pageQuery.getPageNum(), pageQuery.getPageSize());

        //构造查询条件
        QueryWrapper<CommerceContractPrint> queryWrapper = queryBuild(dto);

        //查询数据
        page = commerceContractPrintService.page(page, queryWrapper);
        List<CommerceContractPrint> records = page.getRecords();

        //响应结果封装
        PageResult<CommerceContractPrintListVo> result = new PageResult<>();
        List<CommerceContractPrintListVo> items = BeanUtils.copyProperties(records, CommerceContractPrintListVo.class);
        
        result.setItems(items);
        result.setPages((int)page.getPages());
        result.setTotal((int)page.getTotal());
        result.setPageNum(pageQuery.getPageNum());
        result.setPageSize(pageQuery.getPageSize());

        //返回查询结果
        return result;
    }

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    @Override
    public List<CommerceContractPrintListVo> list(CommerceContractPrintListDto dto) throws ServiceException
    {
    	  //构造查询条件
        QueryWrapper<CommerceContractPrint> queryWrapper = queryBuild(dto);

        //查询数据
        List<CommerceContractPrint> records = commerceContractPrintService.list(queryWrapper);

        //响应结果封装
        List<CommerceContractPrintListVo> result = Collections.emptyList();
        result = BeanUtils.copyProperties(records, CommerceContractPrintListVo.class);

        //返回查询结果
        return result;
    }

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    @Override
    public int count(CommerceContractPrintListDto dto) throws ServiceException
    {
     	//构造查询条件
        QueryWrapper<CommerceContractPrint> queryWrapper = queryBuild(dto, false);

        //查询数据
        int result = (int) commerceContractPrintService.count(queryWrapper);

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractPrintGetVo get(Long id) throws ServiceException
    {
    	//查询数据
        CommerceContractPrint item = commerceContractPrintService.getById(id);

        //响应结果封装
        CommerceContractPrintGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, CommerceContractPrintGetVo.class);
        }

        //返回查询结果
        return result;
    }

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
     @Override
    public CommerceContractPrintGetVo get(CommerceContractPrintListDto dto) throws ServiceException
    {
        //构造查询条件
        QueryWrapper<CommerceContractPrint> queryWrapper = queryBuild(dto);
        queryWrapper.last(PageQuery.LIMIT_ONE);

        //查询数据
        CommerceContractPrint item = commerceContractPrintService.getOne(queryWrapper);

        //响应结果封装
        CommerceContractPrintGetVo result = null;
        if (item != null) {
            result = BeanUtils.copyProperties(item, CommerceContractPrintGetVo.class);
        }

        //返回查询结果
        return result; 	
    }

    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    @Override
    public CommerceContractPrintAddVo add(CommerceContractPrintAddDto dto) throws ServiceException
    {
        CommerceContractPrint entity = BeanUtils.copyProperties(dto, CommerceContractPrint.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setId(IdWorker.getId());
        entity.setCreateTime(nowTime);
        entity.setCreateBy(dto.getCreateBy());
        entity.setCreateUser(dto.getCreateUser());
        entity.setCreateUserName(dto.getCreateUserName());
        entity.setUpdateBy(dto.getCreateBy());
        entity.setUpdateUser(dto.getCreateUser());
        entity.setUpdateUserName(dto.getCreateUserName());
        entity.setUpdateTime(nowTime);
        entity.setDelStatus(0);
        commerceContractPrintService.save(entity);

        //响应结果封装
        CommerceContractPrintAddVo result = new CommerceContractPrintAddVo();
        result.setId(entity.getId());

        return result;
    }

    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void edit(CommerceContractPrintEditDto dto) throws ServiceException
    {
    	CommerceContractPrint entity = BeanUtils.copyProperties(dto, CommerceContractPrint.class);

        LocalDateTime nowTime = LocalDateTime.now();
        entity.setUpdateBy(dto.getUpdateBy());
        entity.setUpdateUser(dto.getUpdateUser());
        entity.setUpdateUserName(dto.getUpdateUserName());
        entity.setUpdateTime(nowTime);

        commerceContractPrintService.updateById(entity);
    }

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(Long id) throws ServiceException
    {
    	 commerceContractPrintService.removeById(id);
    }

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
     @Override
    public void delete(CommerceContractPrintListDto dto) throws ServiceException
    {
    	//构造查询条件
        QueryWrapper<CommerceContractPrint> queryWrapper = queryBuild(dto, false);

        //删除操作
        commerceContractPrintService.remove(queryWrapper);
    }

    @Override
    public CommerceContractPrintGetVo getBasicInfoByContractCode(String contractCode) {
        return commerceContractPrintService.getBasicInfoByContractCode(contractCode);
    }

    @Override
    public CommerceContractPrintGetVo selectSignInfoBySignFlowId(String signFlowId) {
        return commerceContractPrintService.selectSignInfoBySignFlowId(signFlowId);
    }

    @Override
    public List<CommerceContractPrintListVo> getOtherFileById(Long id) {
        return commerceContractPrintService.getOtherFileById(id);
    }

    @Override
    public List<CommerceContractPrintGetVo> getAllFileBySameContractId(String sameContractId) {
        return commerceContractPrintService.getAllFileBySameContractId(sameContractId);
    }

    @Override
    public void batchUpdateTemplateId(Long newTemplateId, Long templateId) {
         commerceContractPrintService.batchUpdateTemplateId(newTemplateId,templateId);
    }


    @Override
    public void updateSignStatusBySignFlowId(String signFlowId, Integer signStatus) throws ServiceException
    {
        commerceContractPrintService.updateSignStatusBySignFlowId(signFlowId,signStatus);
    }

    @Override
    public String selectSealId(Integer signCompanyCode,Integer sealTypeCode) throws ServiceException
    {
         return commerceContractPrintService.selectSealId(signCompanyCode,sealTypeCode);
    }

    @Override
    public Map<String, Object> getSignInfoBySignCompanyCode(Integer signCompanyCode) throws ServiceException
    {
        return commerceContractPrintService.getSignInfoBySignCompanyCode(signCompanyCode);
    }


    @Override
    public Map<String, Object> selectSealIdByOaUploadReq(String signCompanyName,String sealTypeName) throws ServiceException
    {
        return commerceContractPrintService.selectSealIdByOaUploadReq(signCompanyName,sealTypeName);
    }


    @Override
    public Integer selectCompanyCodeByLessor(String lessor) throws ServiceException
    {
        return commerceContractPrintService.selectCompanyCodeByLessor(lessor);
    }

    /**
     * <p>构造查询条件<br>
     * <p>默认构造排序条件<br>
     *
     * @param dto 查询条件Dto
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<CommerceContractPrint> queryBuild(CommerceContractPrintListDto dto) throws ServiceException {
        return queryBuild(dto, true);
    }

    /**
     * <p>构造查询条件<br>
     *
     * @param dto     查询条件Dto
     * @param orderBy 是否构造排序条件
     * @return 查询条件构造器
     * @throws ServiceException 服务处理异常
     */
    private QueryWrapper<CommerceContractPrint> queryBuild(CommerceContractPrintListDto dto, boolean orderBy) throws ServiceException {
        QueryWrapper<CommerceContractPrint> queryWrapper = new QueryWrapper<>();

        CommerceContractPrint entity = BeanUtils.copyProperties(dto, CommerceContractPrint.class);
	    entity.setDelStatus(0);

        /** 添加条件样例参考，不用请删除
        if (StringUtils.isNotBlank(dto.getName())) {
            entity.setName(null);
            queryWrapper.like("name", dto.getName());
        }

        queryWrapper.in(dto.getStatusIn() != null, "status", dto.getStatusIn());

        if (orderBy) {
            if (dto.getTypeOrder() != null) {
                queryWrapper.orderBy(true, dto.getTypeOrder().isAsc(), "type");
            }

            queryWrapper.orderByAsc("order_by");
        }
        */
        if (StringUtils.isNotBlank(dto.getBrandName())) {
            entity.setBrandName(null);
            queryWrapper.like("brand_name", dto.getBrandName());
        }
        if (StringUtils.isNotBlank(dto.getContractCode())) {
            entity.setContractCode(null);
            queryWrapper.like("contract_code", dto.getContractCode());
        }
        if (StringUtils.isNotBlank(dto.getContractNo())) {
            entity.setContractNo(null);
            queryWrapper.like("contract_no", dto.getContractNo());
        }
        if (StringUtils.isNotBlank(dto.getInvoiceNo())) {
            entity.setInvoiceNo(null);
            queryWrapper.like("invoice_no", dto.getInvoiceNo());
        }
        //按创建时间倒序排序，根据需要添加
        queryWrapper.orderByDesc("create_time");

        queryWrapper.setEntity(entity);

        return queryWrapper;
    }
}
