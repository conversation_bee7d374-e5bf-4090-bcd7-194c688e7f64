package com.seewin.som.commerce.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.List;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 开闭店管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageListDto implements Serializable {

    private static final long serialVersionUID = 1L;


    /**
     * 租户id(项目id)
     */
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    private String tenantName;

    /**
     * 企业ID
     */
    private Long entId;

    /**
     * 所属组织ID路径
     */
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    private String orgFname;

    /**
     * 铺位id
     */
    private Long roomId;

    /**
     * 铺位id集合
     */
    private List<Long> roomIdList;

    /**
     * 参考开店时间
     */
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    private BigDecimal referOpenLength;

    /**
     * 参考值
     */
    private BigDecimal referValue;

    /**
     * 参考客流开店时间
     */
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    private BigDecimal referFlowOpenLength;

    /**
     * 参考类型 0-电表 1-客流
     */
    private Integer referType;

    /**
     * 最后时间
     */
    private String lastPowerTime;

    /**
     * 最后数值
     */
    private BigDecimal lastPowerValue;

    /**
     * 电表开店时间
     */
    private String electricityOpenTime;

    /**
     * 客流仪开店时间
     */
    private String flowOpenTime;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    private Integer storeStatus;

    /**
     * 消息通知开关： 0：关； 1：开
     */
    private Integer messageNotify;

    /**
     * 巡查显示开关： 0：关； 1：开
     */
    private Integer patrolShow;

    /**
     * 异常时间间隔(分钟)
     */
    private Integer timePeriod;

    /**
     * 创建人id
     */
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


}
