package com.seewin.som.commerce.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.provider.CommerceEntexitProvider;
import com.seewin.som.commerce.req.CommerceEntexitListDto;
import com.seewin.som.commerce.resp.CommerceEntexitGetVo;
import com.seewin.som.commerce.service.CommerceStoreManageService;
import com.seewin.som.commerce.vo.resp.CommerceStoreManageExp;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.som.space.resp.RoomsListVo;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.commerce.provider.CommerceStoreManageProvider;
import com.seewin.som.commerce.req.CommerceStoreManageAddDto;
import com.seewin.som.commerce.req.CommerceStoreManageEditDto;
import com.seewin.som.commerce.req.CommerceStoreManageListDto;
import com.seewin.som.commerce.resp.CommerceStoreManageAddVo;
import com.seewin.som.commerce.resp.CommerceStoreManageGetVo;
import com.seewin.som.commerce.resp.CommerceStoreManageListVo;

import com.seewin.som.commerce.vo.req.CommerceStoreManageListReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageGetReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageAddReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageEditReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageDelReq;
import com.seewin.som.commerce.vo.resp.CommerceStoreManageListItem;
import com.seewin.som.commerce.vo.resp.CommerceStoreManageGetResp;
import com.seewin.som.commerce.vo.resp.CommerceStoreManageAddResp;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 * 开闭店管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
public class CommerceStoreManageServiceImpl implements CommerceStoreManageService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceStoreManageProvider commerceStoreManageProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceEntexitProvider commerceEntexitProvider;
	
	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<CommerceStoreManageListItem> page(CommerceStoreManageListReq listReq) {
        PageResp<CommerceStoreManageListItem> pageResp = new PageResp<>();
        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());

		User curUser = ApiUtils.getUser(User.class);
		
        CommerceStoreManageListDto queryDto = BeanUtils.copyProperties(listReq, CommerceStoreManageListDto.class);
		queryDto.setTenantId(curUser.getTenantId());

        String brandName = listReq.getBrandName();
        String roomNo = listReq.getRoomNo();
        if (StringUtils.isNotEmpty(brandName) || StringUtils.isNotEmpty(roomNo)){
            RoomsListDto roomsListDto = new RoomsListDto();
            roomsListDto.setTenantId(curUser.getTenantId());
            if (StringUtils.isNotEmpty(brandName)){
                roomsListDto.setBrandName(brandName);
            }
            if (StringUtils.isNotEmpty(roomNo)){
                roomsListDto.setName(roomNo);
            }
            List<RoomsListVo> roomList = roomProvider.list(roomsListDto);
            if (CollectionUtil.isEmpty(roomList)){
                pageResp.setPages(0);
                pageResp.setTotal(0);
                pageResp.setItems(new ArrayList<>());
                return pageResp;
            }
            List<Long> roomIdList = roomList.stream().map(f -> f.getId()).collect(Collectors.toList());
            queryDto.setRoomIdList(roomIdList);
        }
        
        PageQuery<CommerceStoreManageListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<CommerceStoreManageListVo> pageResult = commerceStoreManageProvider.page(pageQuery);

        List<CommerceStoreManageListVo> items = pageResult.getItems();
        List<CommerceStoreManageListItem> storeItems = BeanUtils.copyProperties(items, CommerceStoreManageListItem.class);
        if (CollectionUtil.isNotEmpty(storeItems)){
            for (CommerceStoreManageListItem item : storeItems) {
                RoomsGetVo roomsGetVo = roomProvider.get(item.getRoomId());
                if (roomsGetVo!=null){
                    item.setRoomNo(roomsGetVo.getName());
                    item.setStatus(roomsGetVo.getStatus());
                    item.setStoreId(roomsGetVo.getStoreId());
                    item.setBrandName(roomsGetVo.getBrandName());
                    CommerceEntexitListDto exitDto = new CommerceEntexitListDto();
                    exitDto.setShopId(roomsGetVo.getId());
                    exitDto.setTenantId(roomsGetVo.getTenantId());
                    exitDto.setSearchType(4);
                    CommerceEntexitGetVo getVo = commerceEntexitProvider.get(exitDto);
                    if (!ObjectUtils.isEmpty(getVo)) {
                        item.setRoomStatus(getVo.getRoomStatus());
                    }
                }
                // 是否置灰
                item.setGrayFlag(1);
                if (item.getReferType()==null){
                    item.setGrayFlag(0);
                }
                if (item.getReferType()!=null && item.getReferType()==0 && StringUtils.isNotEmpty(item.getReferFlowOpenTime())){
                    item.setGrayFlag(0);
                }
                if (item.getReferType()!=null && item.getReferType()==1 && StringUtils.isNotEmpty(item.getReferOpenTime())){
                    item.setGrayFlag(0);
                }
            }
        }

        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(storeItems);
        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public CommerceStoreManageGetResp get(CommerceStoreManageGetReq getReq) {
        CommerceStoreManageGetVo getVo = commerceStoreManageProvider.get(getReq.getId());

        CommerceStoreManageGetResp getResp = BeanUtils.copyProperties(getVo, CommerceStoreManageGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public CommerceStoreManageAddResp add(CommerceStoreManageAddReq addReq) {
        CommerceStoreManageAddDto dto = BeanUtils.copyProperties(addReq, CommerceStoreManageAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		dto.setTenantId(curUser.getTenantId());
		
        CommerceStoreManageAddVo addVo = commerceStoreManageProvider.add(dto);

        CommerceStoreManageAddResp addResp = BeanUtils.copyProperties(addVo, CommerceStoreManageAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(CommerceStoreManageEditReq editReq) {
        CommerceStoreManageEditDto dto = BeanUtils.copyProperties(editReq, CommerceStoreManageEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        commerceStoreManageProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(CommerceStoreManageDelReq delReq) {
        commerceStoreManageProvider.delete(delReq.getId());
    }

    @Override
    public void exp(HttpServletResponse response, CommerceStoreManageListReq req) {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        String tenantName = optUser.getTenantName();

        req.setPageNum(1);
        req.setPageSize(-1);
        List<CommerceStoreManageListItem> storeList = page(req).getItems();
        if (CollectionUtil.isEmpty(storeList)){
            return;
        }
        List<CommerceStoreManageExp> expResp = new ArrayList<>();

        for (CommerceStoreManageListItem storeItem : storeList) {
            CommerceStoreManageExp storeExp = new CommerceStoreManageExp();
            storeExp.setTenantName(tenantName);
            storeExp.setRoomNo(storeItem.getRoomNo());
            String statusStr = "";
            Integer status = storeItem.getStatus();
            if (status!=null){
                if (status==1){
                    statusStr = "空置";
                }else if (status==2){
                    statusStr = "在营";
                }else if (status==3){
                    statusStr = "已签约";
                }
            }
            storeExp.setStatus(statusStr);
            storeExp.setStoreId(storeItem.getStoreId());
            storeExp.setBrandName(storeItem.getBrandName());

            storeExp.setElectricityOpenTime(storeItem.getElectricityOpenTime());
            storeExp.setFlowOpenTime(storeItem.getFlowOpenTime());

            String storeStatusStr = "";
            Integer storeStatus = storeItem.getStoreStatus();
            if (storeStatus!=null){
                if (storeStatus==1){
                    storeStatusStr = "异常";
                }else if (storeStatus==0){
                    storeStatusStr = "正常";
                }
            }
            storeExp.setStoreStatus(storeStatusStr);

            String roomStatusStr = "";
            String roomStatus = storeItem.getRoomStatus();
            if (StringUtils.isNotEmpty(roomStatus)){
                if (roomStatus.equals("no_delivered")){
                    roomStatusStr = "未交付";
                    storeExp.setElectricityOpenTime(null);
                    storeExp.setFlowOpenTime(null);
                    storeExp.setStoreStatus(null);
                }else if (roomStatus.equals("no_open")){
                    roomStatusStr = "未开业";
                    storeExp.setElectricityOpenTime(null);
                    storeExp.setFlowOpenTime(null);
                    storeExp.setStoreStatus(null);
                }else if (roomStatus.equals("open")){
                    roomStatusStr = "已开业";
                }else if (roomStatus.equals("be_renewed")){
                    roomStatusStr = "待续约";
                }else if (roomStatus.equals("awaited_withdrawal")){
                    roomStatusStr = "待撤场";
                }else if (roomStatus.equals("withdrawal")){
                    roomStatusStr = "已撤场";
                }
            }
            storeExp.setRoomStatus(roomStatusStr);

            storeExp.setReferOpenTime(storeItem.getReferOpenTime());
            storeExp.setReferCloseTime(storeItem.getReferCloseTime());

            storeExp.setReferFlowOpenTime(storeItem.getReferFlowOpenTime());
            storeExp.setReferFlowCloseTime(storeItem.getReferFlowCloseTime());

            BigDecimal referOpenLength = storeItem.getReferOpenLength();
            storeExp.setReferOpenLength(referOpenLength!=null ? referOpenLength.toString() : null);

            BigDecimal referFlowOpenLength = storeItem.getReferFlowOpenLength();
            storeExp.setReferFlowOpenLength(referFlowOpenLength!=null ? referFlowOpenLength.toString() : null);

            expResp.add(storeExp);
        }

        try {
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = "开闭店管理导出报表";
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0, "开闭店管理")
                    .head(CommerceStoreManageExp.class)
                    .build();
            excelWriter.write(expResp, sheet1);

            excelWriter.finish();

        } catch (Exception ex) {
            throw new ServiceException("开闭店管理数据导出：" + ex.getMessage());
        }
    }

    @Override
    public Integer count(CommerceStoreManageListReq req) {
        Integer count = 0;
        User curUser = ApiUtils.getUser(User.class);
        CommerceStoreManageListDto listDto = new CommerceStoreManageListDto();
        listDto.setTenantId(curUser.getTenantId());
        listDto.setStoreStatus(1);
        List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(listDto);
        if (CollectionUtil.isNotEmpty(storeList)){
            count = storeList.size();
        }
        return count;
    }

}
