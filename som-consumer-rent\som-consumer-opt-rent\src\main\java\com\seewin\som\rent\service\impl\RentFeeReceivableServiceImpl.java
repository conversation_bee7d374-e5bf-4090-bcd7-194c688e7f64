package com.seewin.som.rent.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.seewin.model.base.CusUser;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.provider.CommerceAdvertContractProvider;
import com.seewin.som.commerce.provider.CommerceContractInfoProvider;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.req.CommerceAdvertContractListDto;
import com.seewin.som.commerce.resp.CommerceAdvertContractAddVo;
import com.seewin.som.commerce.resp.CommerceAdvertContractGetVo;
import com.seewin.som.commerce.resp.CommerceContractInfoGetVo;
import com.seewin.som.commerce.resp.CommerceEffectiveContractVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.rent.enums.RentTypeEnum;
import com.seewin.som.rent.provider.*;
import com.seewin.som.rent.req.*;
import com.seewin.som.rent.resp.*;
import com.seewin.som.rent.service.RentFeeReceivableDetailService;
import com.seewin.som.rent.service.RentFeeReceivableService;
import com.seewin.som.rent.util.BigDecimalUtil;
import com.seewin.som.rent.util.DateUtil;
import com.seewin.som.rent.util.FileDownloadUtils;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import com.seewin.som.space.provider.AdvertProvider;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.resp.AdvertGetVo;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.system.service.FileService;
import com.seewin.util.exception.ServiceException;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Month;
import java.time.YearMonth;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 应收台账 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
@Slf4j
public class RentFeeReceivableServiceImpl implements RentFeeReceivableService {

	/**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeChargeProvider rentFeeChargeProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableProvider rentFeeReceivableProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableLogProvider rentFeeReceivableLogProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableDetailProvider rentFeeReceivableDetailProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualProvider rentFeeActualProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualDetailProvider rentFeeActualDetailProvider;
    @DubboReference(providedBy = "som-storage-mgt")
    private SysFileProvider sysFileProvider;

    @Autowired
    private FileService fileService;

    @Autowired
    private RentFeeReceivableDetailService receivableDetailService;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeImportProvider rentFeeImportProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableOperateLogProvider rentFeeReceivableOperateLogProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingItemProvider rentChargingItemProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingStandardStoreProvider chargingStandardStoreProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeCalculateRuleProvider calculateRuleProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualLogProvider rentFeeActualLogProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractInfoProvider commerceContractInfoProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceAdvertContractProvider advertContractProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private AdvertProvider advertProvider;
	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<RentFeeReceivableListItem> page(RentFeeReceivableListReq listReq) {
        PageResp<RentFeeReceivableListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);

        RentFeeReceivableListDto queryDto = BeanUtils.copyProperties(listReq, RentFeeReceivableListDto.class);
				queryDto.setTenantId(curUser.getTenantId());

        PageQuery<RentFeeReceivableListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<RentFeeReceivableListVo> pageResult = rentFeeReceivableProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), RentFeeReceivableListItem.class));

        return pageResp;
    }
    @Override
    public List<RentFeeReceivableListItem> list(RentFeeReceivableListReq listReq) {
        User curUser = ApiUtils.getUser(User.class);
        RentFeeReceivableListDto queryDto = BeanUtils.copyProperties(listReq, RentFeeReceivableListDto.class);
        queryDto.setTenantId(curUser.getTenantId());
        List<RentFeeReceivableListVo> pageResult = rentFeeReceivableProvider.list(queryDto);
        return BeanUtils.copyProperties(pageResult, RentFeeReceivableListItem.class);
    }
    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public List<RentFeeReceivableGetResp> get(RentFeeReceivableGetReq getReq) {
        List<RentFeeReceivableGetResp> getRespList = new ArrayList<>();
        //"应"收账单明细：
        //先查询"应"收台账数据list 在循环获取明细。
        RentFeeReceivableListDto receivableQuery = BeanUtils.copyProperties(getReq, RentFeeReceivableListDto.class);
        receivableQuery.setFname(null);
        receivableQuery.setRoomName(null);
        List<RentFeeReceivableListVo> receivableList = rentFeeReceivableProvider.list(receivableQuery);
        Map<Integer, RentFeeReceivableListVo> receivableMap = receivableList.stream().collect(Collectors.toMap(RentFeeReceivableListVo::getPeriodMonth, (t) -> t));
        //根据"应"收台账查询应收明细
        String roomName = getReq.getRoomName();
        String fname = getReq.getFname();
        if(getReq.getRoomId()!=null){
            if (StringUtils.isBlank(getReq.getRoomName())) {
                //方法让费用台账也能调用
                RoomsGetVo roomsGetVo = roomsProvider.get(getReq.getRoomId());
                if(roomsGetVo!=null){
                    roomName = roomsGetVo.getName();
                    fname = roomsGetVo.getFname().replace("/" + roomsGetVo.getName(), "");
                }
            }
        }else{
            CommerceAdvertContractListDto advertContractListDto = new CommerceAdvertContractListDto();
            advertContractListDto.setContractCode(getReq.getContractCode());
            CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(advertContractListDto);
            if(commerceAdvertContractGetVo!=null){
                roomName=commerceAdvertContractGetVo.getAdvertName();
            }
        }
        for (int month = 1; month <= 12; month++) {
            RentFeeReceivableListVo rentFeeReceivableListVo = receivableMap.get(month);
            String period = getReq.getPeriodYear() + "/" + String.format("%02d", month);
            if (rentFeeReceivableListVo == null) {
                //没有数据的需要补足普通数据
                RentFeeReceivableGetResp getResp = new RentFeeReceivableGetResp();
                getResp.setRoomName(roomName);
                getResp.setFname(fname);
                getResp.setPeriod(period);
                getResp.setReceivableDetailList(new ArrayList<>());
                getRespList.add(getResp);
            } else {
                RentFeeReceivableGetResp getResp = BeanUtils.copyProperties(rentFeeReceivableListVo, RentFeeReceivableGetResp.class);
                getResp.setRoomName(roomName);
                getResp.setFname(fname);
                getResp.setPeriod(period);
                RentFeeReceivableDetailListDto receivableDetailQuery = new RentFeeReceivableDetailListDto();
                receivableDetailQuery.setReceivableId(rentFeeReceivableListVo.getId());
                List<RentFeeReceivableDetailListVo> receivableDetailList = rentFeeReceivableDetailProvider.list(receivableDetailQuery);
                List<RentFeeReceivableDetailListItem> rentFeeReceivableDetailListItem = BeanUtils.copyProperties(receivableDetailList, RentFeeReceivableDetailListItem.class);
                getResp.setReceivableDetailList(rentFeeReceivableDetailListItem);
                getRespList.add(getResp);
            }
        }
        return getRespList;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public RentFeeReceivableAddResp add(RentFeeReceivableAddReq addReq) {
        RentFeeReceivableAddDto dto = BeanUtils.copyProperties(addReq, RentFeeReceivableAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());

		dto.setTenantId(curUser.getTenantId());

        RentFeeReceivableAddVo addVo = rentFeeReceivableProvider.add(dto);

        RentFeeReceivableAddResp addResp = BeanUtils.copyProperties(addVo, RentFeeReceivableAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void edit(RentFeeReceivableEditReq editReq) {
        LocalDateTime now = LocalDateTime.now();
        RentFeeReceivableEditDto dto = BeanUtils.copyProperties(editReq, RentFeeReceivableEditDto.class);
        List<RentFeeReceivableDetailListItem> updateList = editReq.getReceivableDetailList();
        if (CollectionUtil.isNotEmpty(updateList)) {
            for (RentFeeReceivableDetailListItem item : updateList) {
                RentFeeReceivableDetailEditDto detailEditDto = BeanUtils.copyProperties(item, RentFeeReceivableDetailEditDto.class);
                rentFeeReceivableDetailProvider.edit(detailEditDto);
                //修改实收明细数据。
                RentFeeActualDetailEditDto actualDetailEditDto = new RentFeeActualDetailEditDto();
                actualDetailEditDto.setReceivableDetailId(item.getId());
                actualDetailEditDto.setReceivableAmount(item.getAmount());
                rentFeeActualDetailProvider.editByReceivableId(actualDetailEditDto);
            }

            //设置修改人信息
            User curUser = ApiUtils.getUser(User.class);
            //新增应收的操作日志
            RentFeeReceivableOperateLogAddDto operateLogAddDto = new RentFeeReceivableOperateLogAddDto();
            operateLogAddDto.setReceivableId(editReq.getId());
            operateLogAddDto.setCreateBy(curUser.getUserId());
            operateLogAddDto.setCreateUser(curUser.getUserName());
            operateLogAddDto.setCreateUserName(curUser.getRealName());
            rentFeeReceivableOperateLogProvider.add(operateLogAddDto);
            BigDecimal receivableAmount = updateList.stream()
                    .map(RentFeeReceivableDetailListItem::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            //修改应收的数据。
            RentFeeReceivableEditDto receivableEditDto = createReceivableEditDto(curUser, now);
            receivableEditDto.setReceivableAmount(receivableAmount);
            receivableEditDto.setId(editReq.getId());
            rentFeeReceivableProvider.edit(receivableEditDto);
            //修改费用台账数据。
            RentFeeReceivableGetVo rentFeeReceivableGetVo = rentFeeReceivableProvider.get(editReq.getId());
            Long chargeId = rentFeeReceivableGetVo.getChargeId();
            RentFeeChargeGetVo rentFeeChargeGetVo = rentFeeChargeProvider.get(chargeId);
            RentFeeChargeEditDto chargeEditDto = createChargeEditDto(curUser, now);
            chargeEditDto.setReceivableAmount(receivableAmount);
            chargeEditDto.setId(chargeId);
            chargeEditDto.setArrearsAmount(BigDecimalUtil.subtract(receivableAmount, rentFeeChargeGetVo.getActualAmount()));
            rentFeeChargeProvider.edit(chargeEditDto);
            //修改实收台账数据。
            RentFeeActualEditDto actualEditDto = createActualEditDto(curUser, now);
            actualEditDto.setReceivableAmount(receivableAmount);
            actualEditDto.setChargeId(chargeId);
            rentFeeActualProvider.editByChargeId(actualEditDto);
        }

        // 滞纳金重新生成
        reGenerateArrearsAmount(editReq);
    }

    /**
     * 传应收id过来就好
     * @param editReq
     */
    private void reGenerateArrearsAmount(RentFeeReceivableEditReq editReq) {
        // 获取应收id、结算日期
        Long receivableId = editReq.getId();
        RentFeeReceivableGetVo receivableGetVo = rentFeeReceivableProvider.get(receivableId);
        // 结算日期大于今天直接跳过
        LocalDate damagesDate = receivableGetVo.getReceivableDamagesDate();
        LocalDate endLocalDate = LocalDate.now();
        if (damagesDate.isAfter(endLocalDate)){
            return;
        }

        // 获取应收金额、费用台账id、实收id
        BigDecimal receivableAmount = receivableGetVo.getReceivableAmount();
        Long chargeId = receivableGetVo.getChargeId();

        RentFeeActualListDto actualDto = new RentFeeActualListDto();
        actualDto.setChargeId(chargeId);
        RentFeeActualGetVo actualGetVo = rentFeeActualProvider.get(actualDto);
        Long actualId = actualGetVo.getId();

        // 清空应收滞纳金日志表的滞纳金
        rentFeeReceivableLogProvider.emptyArrearsAmount(actualId);
        // 清空实收明细表的滞纳金
        RentFeeActualDetailEditDto actDetailEditDto = new RentFeeActualDetailEditDto();
        actDetailEditDto.setActualId(actualId);
        actDetailEditDto.setArrearsAmount(BigDecimal.ZERO);
        rentFeeActualDetailProvider.emptyArrearsAmount(actDetailEditDto);
        // 清空应收表的滞纳金
        RentFeeReceivableEditDto recEditDto = new RentFeeReceivableEditDto();
        recEditDto.setId(receivableId);
        recEditDto.setReceivableDamagesAmount(BigDecimal.ZERO);
        rentFeeReceivableProvider.edit(recEditDto);
        // 清空费用台账表的滞纳金
        RentFeeChargeEditDto chargeEditDto = new RentFeeChargeEditDto();
        chargeEditDto.setId(chargeId);
        chargeEditDto.setReceivableDamagesAmount(BigDecimal.ZERO);
        chargeEditDto.setArrearsDamagesAmount(BigDecimal.ZERO);
        rentFeeChargeProvider.edit(chargeEditDto);
        // 清空实收表的滞纳金
        RentFeeActualEditDto actualEditDto = new RentFeeActualEditDto();
        actualEditDto.setId(actualId);
        actualEditDto.setReceivableAmount(receivableAmount);
        rentFeeActualProvider.edit(actualEditDto);

        String contractCode = receivableGetVo.getContractCode();
        Integer periodYear = receivableGetVo.getPeriodYear();
        Integer periodMonth = receivableGetVo.getPeriodMonth();
        log.info("contractCode:{}", contractCode);
        log.info("periodYear:{}", periodYear);
        log.info("periodMonth:{}", periodMonth);

        // 账单年月和租赁起始年月一样，无需生成滞纳金
        CommerceContractInfoGetVo contractInfoGetVo = null;
        if (contractCode.startsWith("D")||contractCode.startsWith("G")) {
            CommerceAdvertContractListDto advertDto = new CommerceAdvertContractListDto();
            advertDto.setContractCode(contractCode);
            CommerceAdvertContractGetVo advertGetVo = advertContractProvider.get(advertDto);
            contractInfoGetVo = BeanUtils.copyProperties(advertGetVo, CommerceContractInfoGetVo.class);
        }else {
            contractInfoGetVo = commerceContractInfoProvider.getByContractCode(contractCode);
        }
        LocalDate rentStartDate = contractInfoGetVo.getRentStartDate();
        Integer yearValue = Integer.valueOf(rentStartDate.getYear());
        Integer monthValue = Integer.valueOf(rentStartDate.getMonthValue());
        if (periodYear.equals(yearValue) && periodMonth.equals(monthValue)){
            return;
        }

        // 获取日期列表
        List<LocalDate> dateList = new ArrayList<>();
        dateList.add(damagesDate);
        if (endLocalDate!=null){
            LocalDate tempDate = damagesDate.plusDays(1);
            while (tempDate.isBefore(endLocalDate) || tempDate.isEqual(endLocalDate)){
                dateList.add(tempDate);
                tempDate = tempDate.plusDays(1);
            }
        }

        // 获取产生的滞纳金
        List<RentFeeReceivableLogAddDto> dtoList = rentFeeActualProvider.obtainGenerateByAcutualId(damagesDate, actualId);

        BigDecimal totalArrears = BigDecimal.ZERO;
        // 重新生成滞纳金
        for (LocalDate now : dateList) {
            log.info("滞纳金生成日期:{}", now);
            if (CollectionUtil.isNotEmpty(dtoList)) {
                log.info("滞纳金数量:{}", dtoList.size());
                for (RentFeeReceivableLogAddDto receivableLogAddDto : dtoList) {
                    receivableLogAddDto.setArrearsDate(now);
                    rentFeeReceivableLogProvider.add(receivableLogAddDto);
                    // 填充实收明细 滞纳金
                    Long obtainActualId = receivableLogAddDto.getActualId();
                    Long chargingItemId = receivableLogAddDto.getChargingItemId();
                    BigDecimal arrearsAmount = receivableLogAddDto.getArrearsAmount();
                    rentFeeActualDetailProvider.updateArrears(obtainActualId, chargingItemId, arrearsAmount);
                    totalArrears = totalArrears.add(arrearsAmount);
                }
                // 更新台账的欠费状态
                RentFeeChargeGetVo rentFeeChargeGetVo = rentFeeChargeProvider.get(chargeId);
                if (rentFeeChargeGetVo != null && rentFeeChargeGetVo.getArrearsDate() == null) {
                    RentFeeChargeEditDto dto = new RentFeeChargeEditDto();
                    dto.setId(chargeId);
                    dto.setArrearsStatus(0);
                    dto.setArrearsDate(now);
                    rentFeeChargeProvider.edit(dto);
                }
            }
        }

        if (totalArrears.compareTo(BigDecimal.ZERO)>0){
            // 更新应收表滞纳金
            rentFeeReceivableProvider.updateArrears(receivableId, totalArrears);
            // 更新费用台账滞纳金
            rentFeeChargeProvider.updateArrears(chargeId, totalArrears);
            // 更新实收表滞纳金
            rentFeeActualProvider.updateArrears(actualId, totalArrears);
        }
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(RentFeeReceivableDelReq delReq) {
        rentFeeReceivableProvider.delete(delReq.getId());
    }

    @Override
    public RentFeeReceivableGetBaseResp getBaseById(RentFeeReceivableGetBaseReq getReq) {
        Long receivableId = getReq.getId();
        RentFeeReceivableGetBaseResp getResp = new RentFeeReceivableGetBaseResp();
        RentFeeReceivableGetVo rentFeeReceivableGetVo = rentFeeReceivableProvider.get(receivableId);
        if (rentFeeReceivableGetVo != null) {
            getResp = BeanUtils.copyProperties(rentFeeReceivableGetVo, RentFeeReceivableGetBaseResp.class);
            getResp.setPeriod(rentFeeReceivableGetVo.getPeriodYear() + "/" + String.format("%02d", rentFeeReceivableGetVo.getPeriodMonth()));
            RentFeeReceivableDetailListDto dto = new RentFeeReceivableDetailListDto();
            dto.setReceivableId(receivableId);
            //查询应收的明细数据
            List<RentFeeReceivableDetailListVo> list = rentFeeReceivableDetailProvider.list(dto);
            RentFeeReceivableOperateLogListDto logListDto=new RentFeeReceivableOperateLogListDto();
            logListDto.setReceivableId(receivableId);
            List<RentFeeReceivableOperateLogListVo> logList = rentFeeReceivableOperateLogProvider.list(logListDto);
            List<RentFeeReceivableDetailListItem> rentFeeReceivableDetailListItems = BeanUtils.copyProperties(list, RentFeeReceivableDetailListItem.class);
            RentChargingStandardStoreListDto storeDto = new RentChargingStandardStoreListDto();
            List<String> contractIds = Arrays.asList(rentFeeReceivableGetVo.getContractCode());
            storeDto.setContractCodeList(contractIds);
            List<RentChargingBaseListVo> rentChargingBaseList = chargingStandardStoreProvider.getRentChargingBaseList(storeDto);
            Map<Long, RentChargingBaseListVo> chargeBaseMap =  rentChargingBaseList.stream().collect(Collectors.toMap(vo -> vo.getChargingItemId(), t -> t, (existingValue, newValue) -> existingValue));

            for (RentFeeReceivableDetailListItem rentFeeReceivableDetailListItem : rentFeeReceivableDetailListItems) {
                RentChargingBaseListVo rentChargingBaseListVo = chargeBaseMap.get(rentFeeReceivableDetailListItem.getChargingItemId());
                if(rentChargingBaseListVo==null){
                    throw new ServiceException("计费规则发生变更，请更新应收");
                }
                rentFeeReceivableDetailListItem.setPriceFlag(rentChargingBaseListVo.getPriceFlag());
                rentFeeReceivableDetailListItem.setReadingFlag(rentChargingBaseListVo.getReadingFlag());
                rentFeeReceivableDetailListItem.setMagnificationFlag(rentChargingBaseListVo.getMagnificationFlag());
            }
            getResp.setReceivableDetailList(rentFeeReceivableDetailListItems);
            getResp.setReceivableOperateLogListItemList(BeanUtils.copyProperties(logList, RentFeeReceivableOperateLogListItem.class));
        }
        return getResp;
    }

    @Override
    public void download(RentFeeReceivableListReq listReq) {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName =URLEncoder.encode("应收账单导入模板.xlsx","UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename="+endCodeFileName);
            //查询账期年月的数据
            List<RentFeeReceivableExcelTemplateItem> excelTemplateItem = new ArrayList<>();
            //暂时不需要导出应收模板时带出数据。
            if(listReq.getPeriodYear()==null||listReq.getPeriodMonth()==null){
                throw new ServiceException("账期年月不能为空。");
            }
            OptUser user = ApiUtils.getUser(OptUser.class);

            RentFeeReceivableListDto rentFeeReceivableListDto = BeanUtils.copyProperties(listReq, RentFeeReceivableListDto.class);
            rentFeeReceivableListDto.setTenantId(user.getTenantId());
            List<RentFeeReceivableListVo> list = rentFeeReceivableProvider.list(rentFeeReceivableListDto);
            for (RentFeeReceivableListVo vo : list) {
                RentFeeReceivableDetailListDto dto = new RentFeeReceivableDetailListDto();
                dto.setReceivableId(vo.getId());
                //查询应收的明细数据
                List<RentFeeReceivableDetailListVo> detailListVoList = rentFeeReceivableDetailProvider.list(dto);
                for (RentFeeReceivableDetailListVo item : detailListVoList) {
                     RentFeeReceivableExcelTemplateItem excelTemplateItemVo = BeanUtils.copyProperties(item, RentFeeReceivableExcelTemplateItem.class);
                    excelTemplateItemVo.setContractCode(vo.getContractCode());
                    excelTemplateItemVo.setBrandName(vo.getBrandName());
                    excelTemplateItemVo.setStoreId(vo.getStoreId());
                    excelTemplateItemVo.setPeriod(vo.getPeriodYear() + "/" + String.format("%02d", vo.getPeriodMonth()));
                     excelTemplateItem.add(excelTemplateItemVo);
                }
            }
            try (OutputStream outputStream = response.getOutputStream()) {
                EasyExcel.write(outputStream, RentFeeReceivableExcelTemplateItem.class)
                        .sheet("应收账单导入模板")
                        .doWrite(excelTemplateItem);
            }
        } catch (Exception e) {
            log.error("导出模板异常: {}", e.getMessage(), e);
            throw new ServiceException("导出失败，请稍后再试。", e);
        }
    }

    @Override
    public void imp(RentFeeImportReq req) {
        if (CollectionUtils.isEmpty(req.getFileIds())) {
            throw new ServiceException("附件上传异常,请重新上传");
        }
            for (Long fileId : req.getFileIds()) {
                //上传附件
                SysFileGetVo sysFileGetVo = null;
                try {
                    sysFileGetVo = sysFileProvider.get(fileId);
                    if (sysFileGetVo == null) {
                        throw new ServiceException("附件上传异常,请重新上传");
                    }
                } catch (Exception e) {
                    throw new ServiceException("附件上传异常,请重新上传");
                }
                //解析数据
                InputStream inputstream = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());
                importExcel(inputstream,sysFileGetVo,fileId);
            }
    }

    @Override
    public void importSingleExcel(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        SysFileGetVo sysFileGetVo=new SysFileGetVo();
        sysFileGetVo.setFileName(file.getOriginalFilename());
        importExcel(inputStream,sysFileGetVo,-1l);
    }

    @Override
    public void exp(RentFeeReceivableExpReq expReq) {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        List<RentFeeReceivableDetailExportItem> exportList = new ArrayList<>();
        LocalDate localDate = LocalDate.now();
        int periodYear = expReq.getPeriodYear() == null ? localDate.getYear() : expReq.getPeriodYear();
        int periodMonth = expReq.getPeriodMonth() == null ? localDate.getMonthValue() : expReq.getPeriodMonth();
        String period = periodYear + "/" + String.format("%02d", periodMonth);
        RentFeeReceivableListDto dto = new RentFeeReceivableListDto();
        dto.setPeriodYear(periodYear);
        dto.setPeriodMonth(periodMonth);
        dto.setTenantId(optUser.getTenantId());
        List<RentFeeReceivableExportVo> rentFeeReceivableExportVos = rentFeeReceivableProvider.exportReceivable(dto);
        //根据合同编号查询合同供应商-铺位计租面积
        Set<String> contractCodes = rentFeeReceivableExportVos.stream().map(RentFeeReceivableExportVo::getContractCode).collect(Collectors.toSet());
        Map<String, CommerceEffectiveContractVo> effectiveContractMap = new HashMap<>();
        for (String contractCode : contractCodes) {
            CommerceEffectiveContractVo contractVo ;
            if (contractCode.startsWith("D")||contractCode.startsWith("G")) {
                CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
                commerceAdvertContractListDto.setContractCode(contractCode);
                CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(commerceAdvertContractListDto);
                contractVo=BeanUtils.copyProperties(commerceAdvertContractGetVo,  CommerceEffectiveContractVo.class);
                contractVo.setRentArea(commerceAdvertContractGetVo.getAdvertArea());
            }else{
                 contractVo =commerceContractProvider.getRentFeeCalculateInfo(contractCode);
            }
            effectiveContractMap.put(contractCode,contractVo );
        }
        exportList = BeanUtils.copyProperties(rentFeeReceivableExportVos, RentFeeReceivableDetailExportItem.class);
        for (RentFeeReceivableDetailExportItem item : exportList) {
            item.setPeriod(period);
            item.setRentArea(effectiveContractMap.get(item.getContractCode()).getRentArea());
            item.setSupplierName(effectiveContractMap.get(item.getContractCode()).getSupplierName());
        }
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("应收账单导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), RentFeeReceivableDetailExportItem.class)
                    .sheet("应收账单导出").doWrite(exportList);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("捕获到的导出异常{}", JSON.toJSONString(e.getMessage()));
        }
    }

    @Override
    public void expZip(RentFeeReceivableExpZipReq expReq) {
        HttpServletResponse response = ApiUtils.getResponse();
        // todo 后续换成根据附件ID获取附件流
        String filePath = "D:\\A-WXX\\seewin\\code\\som-consumer-rent\\som-consumer-opt-rent\\src\\main\\resources\\template\\template1.xlsx";
        try {
            if(CollectionUtils.isEmpty(expReq.getIds())){
                throw new ServiceException("请选择导出数据");
            }
            //上传附件
            SysFileGetVo sysFileGetVo = null;
            InputStream fis =null;

            LocalDate nextMonth = LocalDate.now().plusMonths(1);
            //解析数据
            Map<String, byte[]> excelBytes = new HashMap<>();
            for (Long id : expReq.getIds()) {
                try {
                    sysFileGetVo = sysFileProvider.get(expReq.getTemplateId());
                    if (sysFileGetVo == null) {
                        fis = new FileInputStream(filePath);
                    }else{
                        fis = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());
                    }
                } catch (Exception e) {
                    fis = new FileInputStream(filePath);
                    if(fis==null){
                        throw new ServiceException("模板表获取异常！");
                    }
                }
                RentFeeReceivableDetailListDto dto = new RentFeeReceivableDetailListDto();
                dto.setReceivableId(id);
                RentFeeReceivableGetVo rentFeeReceivableGetVo = rentFeeReceivableProvider.get(id);
                //欠费数据组装给导出
                RentFeeReceivableEditDto receivableEditDto=new RentFeeReceivableEditDto();
                receivableEditDto.setId(id);
                receivableEditDto.setContractCode(rentFeeReceivableGetVo.getContractCode());
                receivableEditDto.setVoucherTenantInfoId(rentFeeReceivableGetVo.getVoucherTenantInfoId());
                receivableEditDto.setPeriodYear(expReq.getPeriodYear());
                receivableEditDto.setPeriodMonth(expReq.getPeriodMonth());
                List<RentFeeReceivableExportCheckListVo> rentFeeReceivableExportCheckListVos = rentFeeReceivableProvider.exportReceivableZip(receivableEditDto);
                List<RentFeeReceivableExportCheckListVo> expList =new LinkedList<>();
                expList.addAll(rentFeeReceivableExportCheckListVos);
                BigDecimal totalAmount=BigDecimal.ZERO;
                BigDecimal totalHistoricalArrearsAmount=BigDecimal.ZERO;
                BigDecimal totalArrearsAmount=BigDecimal.ZERO;
                BigDecimal totalAllAmount=BigDecimal.ZERO;
                for (RentFeeReceivableExportCheckListVo item : expList) {
                    //如果账期是最新账期 就给本月费用赋值并清空往月欠费字段数据
                    if(item.getPeriodYear().equals(expReq.getPeriodYear())&&item.getPeriodMonth().equals(expReq.getPeriodMonth())){
                        BigDecimal historicalArrearsAmount = item.getHistoricalArrearsAmount();
                        item.setAmount(historicalArrearsAmount);
                        item.setHistoricalArrearsAmount(null);
                    }
                    item.setRemark("");
                    item.setRoomName(rentFeeReceivableGetVo.getRoomName());
                    totalAmount= BigDecimalUtil.add(totalAmount,item.getAmount());
                    totalHistoricalArrearsAmount=BigDecimalUtil.add(totalHistoricalArrearsAmount,item.getHistoricalArrearsAmount());
                    totalArrearsAmount=BigDecimalUtil.add(totalArrearsAmount,item.getArrearsAmount());
                    totalAllAmount=BigDecimalUtil.add(totalAllAmount,item.getTotalAmount());
                }
                RentFeeReceivableExportCheckExcelVo exp=new RentFeeReceivableExportCheckExcelVo();

                exp.setTenantName(rentFeeReceivableGetVo.getTenantName());
                exp.setBrandName(rentFeeReceivableGetVo.getBrandName());
                CommerceEffectiveContractVo contractVo = null;
                String contractCode = rentFeeReceivableGetVo.getContractCode();
                if (contractCode.startsWith("D")||contractCode.startsWith("G")) {
                    CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
                    commerceAdvertContractListDto.setContractCode(contractCode);
                    CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(commerceAdvertContractListDto);
                    contractVo=BeanUtils.copyProperties(commerceAdvertContractGetVo,  CommerceEffectiveContractVo.class);
                }else{
                    contractVo = commerceContractProvider.getRentFeeCalculateInfo(contractCode);
                }
                exp.setAccountingOrgName(rentFeeReceivableGetVo.getAccountingOrgName());
                exp.setSupplierName(contractVo.getSupplierName());
                exp.setTotalAmount(totalAmount);
                exp.setTotalHistoricalArrearsAmount(totalHistoricalArrearsAmount);
                exp.setTotalArrearsAmount(totalArrearsAmount);
                exp.setTotalAllAmount(totalAllAmount);
                exp.setAmountInWords(BigDecimalUtil.convertAmountToChinese(totalAllAmount));
                exp.setCurrDate(LocalDate.now().toString());
                exp.setYear(rentFeeReceivableGetVo.getPeriodYear());
                exp.setMonth(rentFeeReceivableGetVo.getPeriodMonth());
                exp.setDataList(expList);
                //  todo 后续换成文件流传输

                byte[] excel = FileDownloadUtils.createExcel(fis, exp);
//                byte[] excel = FileDownloadUtils.createExcelToImg(filePath, exp);
                //附件名称
                String excelName=rentFeeReceivableGetVo.getPeriodYear() + "-" + String.format("%02d", rentFeeReceivableGetVo.getPeriodMonth())+"账期"+rentFeeReceivableGetVo.getRoomName()+rentFeeReceivableGetVo.getBrandName()+rentFeeReceivableGetVo.getAccountingOrgName()+"缴款通知单.xlsx";
                excelBytes.put(excelName, excel);
            }

            // 配置文件下载
            response.setCharacterEncoding("UTF-8"); //设置编码字符
            response.setContentType("application/octet-stream;charset=UTF-8");
            //压缩包文件名
            SimpleDateFormat fileZipName = new SimpleDateFormat("yyyyMMddhhmmss");
            // 下载文件时候防止显示中文乱码 指定文件类型 .zip
            response.setHeader("Content-Disposition", "attachment;filename=" +
                    URLEncoder.encode(fileZipName.format(new Date()) + ".zip", "UTF-8"));
            FileDownloadUtils.getZipFile(excelBytes, response.getOutputStream());
        } catch (Exception e) {
            log.error("导出异常", e);
            throw new ServiceException(e.getMessage());
        }

    }




    private void importExcel(InputStream inputstream,SysFileGetVo sysFileGetVo,Long fileId){
        //导入应收明细数据，需要修改费用、应收、实收台账的应收金额和实收明细里面的应收金额
        LocalDateTime now = LocalDateTime.now();
        OptUser curUser = null;
        if (fileId == -1) {
            curUser = ApiUtils.getUser(OptUser.class);
            if(curUser==null){
                curUser =new OptUser();
                curUser.setTenantId(1779852125880651777l);
            }
        } else {
            curUser = ApiUtils.getUser(OptUser.class);
        }
        //解析数据
        List<RentFeeReceivableImportItem> addBatchList = EasyExcel.read(inputstream).head(RentFeeReceivableImportItem.class).sheet(0).headRowNumber(1).doReadSync();
        log.info("导入记录数：{}", addBatchList.size());
        //导入记录
        RentFeeImportAddDto rentFeeImportAddDto = new RentFeeImportAddDto();
        rentFeeImportAddDto.setTenantId(curUser.getTenantId());
        rentFeeImportAddDto.setFileName(sysFileGetVo.getFileName());
        rentFeeImportAddDto.setFileId(fileId);
        rentFeeImportAddDto.setStatus(1);
        rentFeeImportAddDto.setBizType(1);
        rentFeeImportAddDto.setTotalCount(0);
        rentFeeImportAddDto.setFailCount(0);

        if (CollectionUtils.isEmpty(addBatchList) || addBatchList.size() < 1) {
            //空数据
            rentFeeImportAddDto.setTotalCount(0);
            rentFeeImportAddDto.setFailCount(0);
            rentFeeImportAddDto.setCreateTime(now);
            rentFeeImportProvider.add(rentFeeImportAddDto);
            return;
        }
        List<String> contractCodeList = addBatchList.stream().map(RentFeeReceivableImportItem::getContractCode).collect(Collectors.toList());
        List<RentFeeReceivableListVo> rentFeeReceivableList = getRentFeeReceivableList(contractCodeList);
        //根据账期、合同、收费项目分组，
        Map<String, Object> map = getRentFeeReceivableDetailMap(rentFeeReceivableList);
        List<RentFeeImportDetailAddDto> rentFeeImportDetailAddDtos = new ArrayList<>();
        List<RentFeeReceivableDetailImpItem> resultList = new ArrayList<>();
        Map<String, RentFeeReceivableDetailImpItem> receivableDetailMap = (Map<String, RentFeeReceivableDetailImpItem>) map.get("rentFeeReceivableDetailImpItemMap");
        //获取该合同所有应收下面
        Map<Long, List<RentFeeReceivableDetailImpItem>> receivableDetailItemMapByReceivableId = (Map<Long, List<RentFeeReceivableDetailImpItem>>) map.get("receivableDetailItemMapByReceivableId");
        for (int i = 0; i < addBatchList.size(); i++) {
            RentFeeReceivableImportItem item = addBatchList.get(i);
            try {
                if (StringUtils.isEmpty(item.getContractCode())) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "合同编号不能为空", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (!DateUtil.isValidYMDFormat(item.getPeriod())) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"账期格式有误，需要按照yyyy/mm的格式填写", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                String startDate = item.getPeriodStart();
                String endDate = item.getPeriodEnd();
                //日期要么都空， 要么都有值。
                if ((StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) ||
                        (StringUtils.isEmpty(startDate) && StringUtils.isEmpty(endDate))) {
                    //有值需要校验日期是否符合规则。
                    if (StringUtils.isNotEmpty(startDate) && StringUtils.isNotEmpty(endDate)) {
                        if (DateUtil.isValidDate(startDate) && DateUtil.isValidDate(endDate)) {
                            if (!DateUtil.isEndDateAfterOrEqualStartDate(startDate, endDate)) {
                                rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"属期结束日期小于属期开始日期", item.getContractCode(), item.getStoreId()));
                                continue;
                            }
                        } else {
                            rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"提供的”属期开始日期“或者“属期结束日期”不符合日期格式!请使用yyyy-MM-dd的格式", item.getContractCode(), item.getStoreId()));
                            continue;
                        }
                    }
                } else {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"提供的”属期开始日期“或者“属期结束日期”要么都为空要么都有值!", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //上次读数如果为空不校验，不为空不能小于0
                if (item.getLastCount() != null && item.getLastCount().compareTo(BigDecimal.ZERO) < 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"上次读数不能小于0", item.getContractCode(), item.getStoreId()));
                    continue;
                }

                //本次读数如果为空不校验，不为空不能小于0
                if (item.getThisCount() != null && item.getThisCount().compareTo(BigDecimal.ZERO) < 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"本次读数不能小于0", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //上次读数不能小于本次读数
                if (item.getLastCount()!=null&&item.getLastCount().compareTo(item.getThisCount()) > 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"本次读数不能小于上次读数", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //倍率不为空时不能小于0
                if (item.getMagnification() != null && item.getMagnification().compareTo(BigDecimal.ZERO) < 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"倍率不能小于0", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //单价不能小于0
                if (item.getUnitPrice() != null && item.getUnitPrice().compareTo(BigDecimal.ZERO) < 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"单价不能小于0", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                log.info("导入数据：========>"+JSON.toJSONString(item));
                //导入模板的数据
                if (StringUtils.isEmpty(startDate)) {
                    item.setPeriodStart("");
                    item.setPeriodEnd("");
                } else {
                    item.setPeriodStart(DateUtil.parseDateString(startDate).toString());
                    item.setPeriodEnd(DateUtil.parseDateString(item.getPeriodEnd()).toString());
                }
                RentFeeReceivableDetailImpItem impItem = BeanUtils.copyProperties(item, RentFeeReceivableDetailImpItem.class);
                if (StringUtils.isEmpty(startDate)) {
                    impItem.setPeriodStart(null);
                    impItem.setPeriodEnd(null);
                } else {
                    impItem.setPeriodStart(DateUtil.parseDateString(startDate));
                    impItem.setPeriodEnd(DateUtil.parseDateString(item.getPeriodEnd()));
                }
                //导入模板数据映射的数据库数据（包含ID），修改数据库数据
                RentFeeReceivableDetailImpItem receivableDetailImpItem = receivableDetailMap.get(generateKey(impItem));
                //判断导入数据是否在应收中存在
                if (receivableDetailImpItem == null) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"数据在系统中不存在应收明细", item.getContractCode(), item.getStoreId()));
                    continue;
                }
                receivableDetailImpItem.setLastCount(impItem.getLastCount());
                receivableDetailImpItem.setThisCount(impItem.getThisCount());
                receivableDetailImpItem.setMagnification(impItem.getMagnification());
                receivableDetailImpItem.setUnitPrice(impItem.getUnitPrice());
                receivableDetailImpItem.setPeriodStart(impItem.getPeriodStart());
                receivableDetailImpItem.setPeriodEnd(impItem.getPeriodEnd());
                //计算用量
                BigDecimal count = BigDecimalUtil.subtract(impItem.getThisCount(), impItem.getLastCount());
                    RentChargingItemGetVo rentChargingItemGetVo = rentChargingItemProvider.get(receivableDetailImpItem.getChargingItemId());
                    if(StringUtils.equals(rentChargingItemGetVo.getCode(), "WATER_ITEM")){
                        receivableDetailImpItem.setTotalCount(count);
                    }
                    if(StringUtils.equals(rentChargingItemGetVo.getCode(), "WATER_LOSS_ITEM")){
                        receivableDetailImpItem.setTotalCount(count);
                    }
                    //电费存在多条明细的情况。用包含的方式处理
                    if(rentChargingItemGetVo.getCode().contains( "ELECTRICITY_ITEM")){
                        receivableDetailImpItem.setTotalCount(BigDecimalUtil.multiply(count, impItem.getMagnification()));
                    }
                    if(rentChargingItemGetVo.getCode().contains( "ELECTRICAL_LOSS_ITEM")){
                        receivableDetailImpItem.setTotalCount(BigDecimalUtil.multiply(count, impItem.getMagnification()));
                    }
                    if (StringUtils.equals(rentChargingItemGetVo.getCode(), "ELECTRICITY_SHARE_ITEM")) {
                        receivableDetailImpItem.setTotalCount(BigDecimalUtil.multiply(count, impItem.getMagnification()));
                    }
                //计算应收金额。
                receivableDetailImpItem.setAmount(getItemPrice(receivableDetailImpItem));
                //保留导入模板和系统关联上的数据。
                resultList.add(receivableDetailImpItem);
                //导入成功记录
                RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
                addDto.setLine(i + 1);
                addDto.setStatus(1);
                addDto.setFname(item.getContractCode());
                addDto.setName(item.getStoreId());
                rentFeeImportDetailAddDtos.add(addDto);
                rentFeeImportAddDto.setTotalCount(rentFeeImportAddDto.getTotalCount()+1);
            } catch (Exception e) {
                log.error("导入数据异常：" + e.getMessage(), e);
                rentFeeImportDetailAddDtos.add(createImportDetailDto(i, item.getChargingItemName()+"数据异常", item.getContractCode(), item.getStoreId()));
                rentFeeImportAddDto.setFailCount(rentFeeImportAddDto.getFailCount()+1);
            }
        }
        //  修改应收数据。修改实收数据的应收  修改费用台账。
        //插入导入日志。
        rentFeeReceivableProvider.importList(rentFeeImportAddDto, rentFeeImportDetailAddDtos);
        //所有导入模板映射系统中应收明细数据
        //根据应收ID分组求和算出每个应收对应的数据。
        Set<Long> receivableIds = resultList.stream().map(RentFeeReceivableDetailImpItem::getReceivableId).collect(Collectors.toSet());
        //获取应收对应的费用台账ID。
        Map<Long, Long> receivableIdAndChargeIdMap = resultList.stream().collect(Collectors.toMap(RentFeeReceivableDetailImpItem::getReceivableId, RentFeeReceivableDetailImpItem::getChargeId, (existingValue, newValue) -> existingValue));

        //修改应收明细数据。
        for (RentFeeReceivableDetailImpItem rentFeeReceivableDetailImpItem : resultList) {
            RentFeeReceivableDetailEditDto rentFeeReceivableDetailEditDto = BeanUtils.copyProperties(rentFeeReceivableDetailImpItem, RentFeeReceivableDetailEditDto.class);
            // todo 暂时赋值合计金额为本月费用
            rentFeeReceivableDetailEditDto.setTotalAmount(rentFeeReceivableDetailEditDto.getAmount());
            rentFeeReceivableDetailProvider.edit(rentFeeReceivableDetailEditDto);
            //修改实收明细数据。
            RentFeeActualDetailEditDto dto = new RentFeeActualDetailEditDto();
            dto.setReceivableDetailId(rentFeeReceivableDetailImpItem.getId());
            dto.setReceivableAmount(rentFeeReceivableDetailImpItem.getAmount());
            rentFeeActualDetailProvider.editByReceivableId(dto);
        }
        for (Long receivableId : receivableIds) {
            //  计算包含本次导入的正确结果+本次应收的对应结果。
            List<RentFeeReceivableDetailImpItem> rentFeeReceivableDetailImpItems = receivableDetailItemMapByReceivableId.get(receivableId);
            //计算除开本次导入应收项目的数据的应收金额
            updateAmount(rentFeeReceivableDetailImpItems, resultList);

            BigDecimal receivableAmount = rentFeeReceivableDetailImpItems.stream()
                    .map(RentFeeReceivableDetailImpItem::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);

            //循环修改不同应收的数据。
            RentFeeReceivableEditDto receivableEditDto = createReceivableEditDto(curUser, now);
            receivableEditDto.setReceivableAmount(receivableAmount);
            receivableEditDto.setId(receivableId);
            rentFeeReceivableProvider.edit(receivableEditDto);
            //修改费用台账数据。
            Long chargeId = receivableIdAndChargeIdMap.get(receivableId);
            //查询费用台账数据
            RentFeeChargeGetVo rentFeeChargeGetVo = rentFeeChargeProvider.get(chargeId);
            RentFeeChargeEditDto chargeEditDto = createChargeEditDto(curUser, now);
            chargeEditDto.setReceivableAmount(receivableAmount);
            chargeEditDto.setId(chargeId);
            chargeEditDto.setArrearsAmount(BigDecimalUtil.subtract(receivableAmount, rentFeeChargeGetVo.getActualAmount()));
            rentFeeChargeProvider.edit(chargeEditDto);
            //修改实收台账数据。
            RentFeeActualEditDto actualEditDto = createActualEditDto(curUser, now);
            actualEditDto.setReceivableAmount(receivableAmount);
            actualEditDto.setChargeId(chargeId);
            rentFeeActualProvider.editByChargeId(actualEditDto);
            //新增应收的操作日志
            RentFeeReceivableOperateLogAddDto operateLogAddDto = new RentFeeReceivableOperateLogAddDto();
            operateLogAddDto.setReceivableId(receivableId);
            operateLogAddDto.setCreateBy(curUser.getUserId());
            operateLogAddDto.setCreateUser(curUser.getUserName());
            operateLogAddDto.setCreateUserName(curUser.getRealName());
            rentFeeReceivableOperateLogProvider.add(operateLogAddDto);

            // 滞纳金重新生成
            RentFeeReceivableEditReq editReq = new RentFeeReceivableEditReq();
            editReq.setId(receivableId);
            reGenerateArrearsAmount(editReq);
        }
    }
    /**
     * 根据合同编号集合查出应收账单数据。
     * @param contractCodeList
     * @return
     */
    private List<RentFeeReceivableListVo> getRentFeeReceivableList(List<String>contractCodeList){
        if(CollectionUtil.isEmpty(contractCodeList))return new ArrayList<>();
        //查询该导入文件中所有合同的应收账单明细数据。
        RentFeeReceivableListDto dto=new RentFeeReceivableListDto();
        dto.setContractCodeList(contractCodeList);
        return rentFeeReceivableProvider.list(dto);
    }

    /**
     * 根据应收账单ID查出所有对应的应收明细数据。
     * @param rentFeeReceivableList
     * @return  Map<String,RentFeeReceivableDetailImpItem>
     */
    private Map<String,Object> getRentFeeReceivableDetailMap(List<RentFeeReceivableListVo> rentFeeReceivableList) {
        if (CollectionUtil.isEmpty(rentFeeReceivableList)) return new HashMap<>();
        Map<String,Object> result=new HashMap<>();
        Map<String,RentFeeReceivableDetailImpItem> rentFeeReceivableDetailImpItemMap = new HashMap<>();
        Map<Long,List<RentFeeReceivableDetailImpItem>> receivableDetailItemMapByReceivableId =  new HashMap<>();
        result.put("rentFeeReceivableDetailImpItemMap", rentFeeReceivableDetailImpItemMap);
        result.put("receivableDetailItemMapByReceivableId", receivableDetailItemMapByReceivableId);
        //查询该导入文件中所有合同的应收账单明细数据。
        for (RentFeeReceivableListVo vo : rentFeeReceivableList) {
            RentFeeReceivableDetailListDto dto = new RentFeeReceivableDetailListDto();
            dto.setReceivableId(vo.getId());
            List<RentFeeReceivableDetailListVo> list = rentFeeReceivableDetailProvider.list(dto);
            List<RentFeeReceivableDetailImpItem> detailItem = BeanUtils.copyProperties(list, RentFeeReceivableDetailImpItem.class);
            detailItem.forEach(item -> {
                item.setContractCode(vo.getContractCode());
                item.setPeriod(vo.getPeriodYear() + "/" + String.format("%02d", vo.getPeriodMonth()));
                item.setChargeId(vo.getChargeId());
                String key = generateKey(item);
                rentFeeReceivableDetailImpItemMap.put(key, item);
            });
            receivableDetailItemMapByReceivableId.put(vo.getId(), detailItem);
        }
        return result;
    }
    private String generateKey(RentFeeReceivableDetailImpItem item) {
        return item.getContractCode() + ":" + item.getPeriod() + ":" + item.getChargingItemName();
    }

    /**
     * 创建错误实体
     * @param i
     * @param msg
     * @return
     */
    private RentFeeImportDetailAddDto createImportDetailDto(int i, String msg,String fname,String name) {
        RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
        CusUser curUser=ApiUtils.getUser(CusUser.class);
        addDto.setLine(i + 1);
        addDto.setStatus(2);
        //合同名称
        addDto.setFname(fname);
        //门店ID
        addDto.setName(name);
        addDto.setFailReason(msg);
        addDto.setCreateBy(curUser.getUserId());
        addDto.setCreateUser(curUser.getUserName());
        addDto.setCreateUserName(curUser.getRealName());
        addDto.setCreateTime(LocalDateTime.now());
        return addDto;
    }


    private BigDecimal getItemPrice(RentFeeReceivableDetailImpItem item){
        RentFeeReceivableDetailEditReq query=new RentFeeReceivableDetailEditReq();
        query.setId(item.getId());
        query.setPeriodStart(item.getPeriodStart());
        query.setPeriodEnd(item.getPeriodEnd());
        query.setLastCount(item.getLastCount());
        query.setThisCount(item.getThisCount());
        query.setMagnification(item.getMagnification());
        query.setUnitPrice(item.getUnitPrice());
        return receivableDetailService.calculate(query);
    }

    private RentFeeReceivableEditDto createReceivableEditDto(User curUser,LocalDateTime now) {
        RentFeeReceivableEditDto dto=new RentFeeReceivableEditDto();
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        dto.setUpdateTime(now);
        return dto;
    }
    private RentFeeChargeEditDto createChargeEditDto(User curUser, LocalDateTime now) {
        RentFeeChargeEditDto dto=new RentFeeChargeEditDto();
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        dto.setUpdateTime(now);
        return dto;
    }
    private RentFeeActualEditDto createActualEditDto(User curUser, LocalDateTime now) {
        RentFeeActualEditDto dto=new RentFeeActualEditDto();
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        dto.setUpdateTime(now);
        return dto;
    }

    /**
     *  第二个参数的数据时导入数据。  需要修正导入前的查询的应收数据
     * @param listA
     * @param listB
     */
    public void updateAmount(List<RentFeeReceivableDetailImpItem> listA, List<RentFeeReceivableDetailImpItem> listB) {
        // 创建一个映射关系
        Map<Long, BigDecimal> tenantNameMap = new HashMap<>();

        // 遍历 listB，构建映射关系
        for (RentFeeReceivableDetailImpItem temp : listB) {
            tenantNameMap.put(temp.getId(), temp.getAmount());
        }

        // 遍历 listA，更新 name 字段
        for (RentFeeReceivableDetailImpItem item : listA) {
            BigDecimal amount = tenantNameMap.get(item.getId());
            if (amount != null) {
                item.setAmount(amount);
            }
        }
    }

    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void createChargeByContractCode(RentFeeReceivableAddChargeReq addChargeReq) {
        List<RentFeeChargeGetItem> receivableList = addChargeReq.getReceivableList();
        if (CollectionUtils.isEmpty(receivableList)) throw new ServiceException("应收信息不能为空");
        List<String> contractCodeList = receivableList.stream().map(RentFeeChargeGetItem::getContractCode).distinct().collect(Collectors.toList());
        List<Long> chargeIdList = receivableList.stream().map(RentFeeChargeGetItem::getChargeId).distinct().collect(Collectors.toList());
        // 先校验改合同是否产生滞纳金和实收数据
        RentFeeChargeListDto chargeListDto = new RentFeeChargeListDto();
        chargeListDto.setContractCodeList(contractCodeList);
        chargeListDto.setPeriodYear(addChargeReq.getPeriodYear());
        chargeListDto.setPeriodMonth(addChargeReq.getPeriodMonth());
        List<RentFeeChargeListVo> list = rentFeeChargeProvider.list(chargeListDto);
        List<String> msg = new LinkedList<>();
        List<String> msgActualLog = new LinkedList<>();
        for (RentFeeChargeListVo rentFeeChargeListVo : list) {
            if(chargeIdList.contains(rentFeeChargeListVo.getId())){
                if (BigDecimalUtil.compareTo(rentFeeChargeListVo.getActualAmount(), BigDecimal.ZERO) > 0) {
                    msg.add(rentFeeChargeListVo.getContractCode());
                }
            }
            RentFeeActualListDto actualListDto=new RentFeeActualListDto();
            actualListDto.setChargeId(rentFeeChargeListVo.getId());
            RentFeeActualGetVo rentFeeActualGetVo = rentFeeActualProvider.get(actualListDto);

            RentFeeActualLogListDto logListDto=new RentFeeActualLogListDto();
            logListDto.setActualId(rentFeeActualGetVo.getId());
            logListDto.setPaymentStatus(0);
            int count = rentFeeActualLogProvider.count(logListDto);
            if (count > 0) {
                msgActualLog.add(rentFeeChargeListVo.getContractCode());}
        }
        if (!CollectionUtils.isEmpty(msg)) {
            throw new ServiceException("合同编号为" + String.join(",", msg) + "的应收账单更新失败，选中数据的实收总额大于0时，无法进行更新应收操作");
        }
        if (!CollectionUtils.isEmpty(msgActualLog)) {
            throw new ServiceException("合同编号为" + String.join(",", msgActualLog) + "的应收账单更新失败，选中数据的实收录入详情存在数据，无法进行更新应收操作");
        }
        //校验成功后 删除数据（费用台账+应收+实收+应收明细+实收明细）
        for (Long chargeId : chargeIdList) {
            rentFeeChargeProvider.delChargeByContractCode(chargeId);
        }
        //根据账期日期推前一个 才能生成指定账期账单。
        LocalDate currentDate = getFirstDayOfMonth(addChargeReq.getPeriodYear(), addChargeReq.getPeriodMonth());
        currentDate = currentDate.minusMonths(1);
        //参数 用于替换当前日期
        LocalDateTime nowTime = LocalDateTime.now();
        //查询当前生成合同
        for (RentFeeChargeListVo rentFeeChargeGetItem : list) {
            List<CommerceEffectiveContractVo> effectiveContractList ;
            if(rentFeeChargeGetItem.getShopType()==0||rentFeeChargeGetItem.getShopType()==1){
                effectiveContractList = commerceContractProvider.selectEffectiveContractByContractCode(contractCodeList);
            }else{
                //todo  更新应收那里也要修改 可以修改查询广告位合同用自定义SQL去写，通过as匹配出对应的VO对象，广告位查询对应合同数据。
                CommerceAdvertContractListDto dto=new CommerceAdvertContractListDto();
                dto.setContractCode(rentFeeChargeGetItem.getContractCode());
                CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(dto);
                effectiveContractList=new ArrayList<>();
                effectiveContractList.add(BeanUtils.copyProperties(commerceAdvertContractGetVo,CommerceEffectiveContractVo.class));
            }
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(effectiveContractList)) {
                CommerceEffectiveContractVo commerceEffectiveContractVo = effectiveContractList.get(0);
                if(rentFeeChargeGetItem.getShopType()==0||rentFeeChargeGetItem.getShopType()==1){
                    commerceEffectiveContractVo.setType(0);
                }else{
                    commerceEffectiveContractVo.setType(1);
                }
                //赋值项目计租面积计算服务费用
                Set<Long> tenantIdSet = effectiveContractList.stream().map(CommerceEffectiveContractVo::getTenantId).collect(Collectors.toSet());
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(tenantIdSet)) {
                    List tenantIds = new ArrayList(tenantIdSet);
                    Map<Long, EntProjectListVo> projectInfoByIds = entProjectProvider.getProjectInfoByIds(tenantIds);
                    //赋值项目计租面积
                    for (CommerceEffectiveContractVo contractVo : effectiveContractList) {
                        EntProjectListVo entProjectListVo = projectInfoByIds.get(contractVo.getTenantId());
                        if (entProjectListVo != null && entProjectListVo.getFrentalcalculationarea() != null) {
                            contractVo.setFrentalcalculationarea(new BigDecimal(entProjectListVo.getFrentalcalculationarea()));
                        }
                    }
                }
                LocalDate finalCurrentDate = currentDate;
                    //校验日期是否在合同范围内
                    List<LocalDate> periodList =  DateUtil.getFirstDayOfEachMonth(commerceEffectiveContractVo.getRentStartDate(), commerceEffectiveContractVo.getRentEndDate());
                    if(!periodList.contains(LocalDate.of(addChargeReq.getPeriodYear(), addChargeReq.getPeriodMonth(),1))){
                        log.info(currentDate+"日期不在合同范围内，跳过生成费用");
                        continue;
                    }
                //校验计费截止日期的年月是否小于当前日期的年月
                if(commerceEffectiveContractVo.getActRetriveDate()!=null){
                    YearMonth currentYearMonth = YearMonth.from(currentDate);
                    YearMonth endYearMonth = YearMonth.from(commerceEffectiveContractVo.getActRetriveDate());
                    if(endYearMonth.isBefore(currentYearMonth)) {
                        log.info(currentDate+"计费截止日期小于当前日期，跳过生成费用");
                        continue;
                    }
                }
                //查询所有合同应用的标准
                RentChargingStandardStoreListDto dto = new RentChargingStandardStoreListDto();
                List<String> contractIds = effectiveContractList.stream().map(CommerceEffectiveContractVo::getContractCode).collect(Collectors.toList());
                dto.setContractCodeList(contractIds);
                List<RentChargingBaseListVo> rentChargingBaseList = chargingStandardStoreProvider.getRentChargingBaseList(dto);
                Map<String, List<RentChargingBaseListVo>> rentChargingBaseListByContractCode = rentChargingBaseList.stream().collect(Collectors.groupingBy(RentChargingBaseListVo::getBindContractCode));
                List<Long> chargeIds = createRentCharge(effectiveContractList, rentChargingBaseListByContractCode, currentDate, nowTime, false, receivableList);
                // 滞纳金重新生成
                // reGenerateArrears(chargeIds);
            }
        }

    }

    @Override
    public void createChargeList(List<RentFeeReceivableAddChargeListReq> addChargeListReqs) {
        for (RentFeeReceivableAddChargeListReq addChargeListReq : addChargeListReqs) {
            List<LocalDate> firstDayOfEachMonth = DateUtil.getFirstDayOfEachMonth(addChargeListReq.getPeriodStartDate(), addChargeListReq.getPeriodEndDate());
            List<String> contractCodeList =Arrays.asList(addChargeListReq.getContractCode());
            List<CommerceEffectiveContractVo> effectiveContractList;
            if(addChargeListReq.getType()==0){
                effectiveContractList = commerceContractProvider.selectEffectiveContractByContractCode(contractCodeList);
            }else{
                //todo  更新应收那里也要修改 可以修改查询广告位合同用自定义SQL去写，通过as匹配出对应的VO对象，广告位查询对应合同数据。
                CommerceAdvertContractListDto dto=new CommerceAdvertContractListDto();
                dto.setContractCode(addChargeListReq.getContractCode());
                CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(dto);
                effectiveContractList=new ArrayList<>();
                effectiveContractList.add(BeanUtils.copyProperties(commerceAdvertContractGetVo,CommerceEffectiveContractVo.class));
            }
            //赋值项目计租面积计算服务费用
            Set<Long> tenantIdSet = effectiveContractList.stream().map(CommerceEffectiveContractVo::getTenantId).collect(Collectors.toSet());
            if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(tenantIdSet)) {
                List tenantIds = new ArrayList(tenantIdSet);
                Map<Long, EntProjectListVo> projectInfoByIds = entProjectProvider.getProjectInfoByIds(tenantIds);
                //赋值项目计租面积
                for (CommerceEffectiveContractVo contractVo : effectiveContractList) {
                    EntProjectListVo entProjectListVo = projectInfoByIds.get(contractVo.getTenantId());
                    if (entProjectListVo != null && entProjectListVo.getFrentalcalculationarea() != null) {
                        contractVo.setFrentalcalculationarea(new BigDecimal(entProjectListVo.getFrentalcalculationarea()));
                    }
                }
            }
            for (LocalDate currentDate : firstDayOfEachMonth) {
                //参数 用于替换当前日期
                LocalDateTime nowTime = LocalDateTime.now();
                //查询当前生成合同
                if (com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(effectiveContractList)) {
                    //校验日期是否在合同范围内
                    CommerceEffectiveContractVo commerceEffectiveContractVo = effectiveContractList.get(0);
                    commerceEffectiveContractVo.setType(addChargeListReq.getType());
                    List<LocalDate> periodList =  DateUtil.getFirstDayOfEachMonth(commerceEffectiveContractVo.getRentStartDate(), commerceEffectiveContractVo.getRentEndDate());
                    if(!periodList.contains(currentDate)){
                        log.info(currentDate+"日期不在合同范围内，跳过生成费用");
                        continue;
                    }
                    //校验计费截止日期的年月是否小于当前日期的年月
                    if(commerceEffectiveContractVo.getActRetriveDate()!=null){
                        YearMonth currentYearMonth = YearMonth.from(currentDate);
                        YearMonth endYearMonth = YearMonth.from(commerceEffectiveContractVo.getActRetriveDate());
                        if(endYearMonth.isBefore(currentYearMonth)) {
                            log.info(currentDate+"计费截止日期小于当前日期，跳过生成费用");
                            continue;
                        }
                    }

                    //往前推出一个月满足之前的业务逻辑。
                    currentDate=currentDate.minusMonths(1);
                    //查询所有合同应用的标准
                    RentChargingStandardStoreListDto dto = new RentChargingStandardStoreListDto();
                    List<String> contractIds = effectiveContractList.stream().map(CommerceEffectiveContractVo::getContractCode).collect(Collectors.toList());
                    dto.setContractCodeList(contractIds);
                    List<RentChargingBaseListVo> rentChargingBaseList = chargingStandardStoreProvider.getRentChargingBaseList(dto);
                    Map<String, List<RentChargingBaseListVo>> rentChargingBaseListByContractCode = rentChargingBaseList.stream().collect(Collectors.groupingBy(RentChargingBaseListVo::getBindContractCode));
                    List<Long> chargeIds = createRentCharge(effectiveContractList, rentChargingBaseListByContractCode, currentDate, nowTime, false, null);
                }
            }
        }
    }

    private void reGenerateArrears(List<Long> chargeIds ) {
        for (Long chargeId :chargeIds) {
            RentFeeReceivableListDto recDto = new RentFeeReceivableListDto();
            recDto.setChargeId(chargeId);
            RentFeeReceivableGetVo receivableGetVo = rentFeeReceivableProvider.get(recDto);

            // 滞纳金重新生成
            RentFeeReceivableEditReq editReq = new RentFeeReceivableEditReq();
            editReq.setId(receivableGetVo.getId());
            editReq.setReceivableDamagesDate(receivableGetVo.getReceivableDamagesDate());
            reGenerateArrearsAmount(editReq);
        }
    }

    public List<Long> createRentCharge(List<CommerceEffectiveContractVo> contractList, Map<String, List<RentChargingBaseListVo>> rentChargingBaseListByContractCode, LocalDate currentDate, LocalDateTime nowTime, boolean isFirstPeriod,List<RentFeeChargeGetItem> receivableList) {
        List<Long> chargeIds = new ArrayList<>();
        //查询该合同对应的应用标准收费项目。
        //先循环一次生成首次的。
        for (CommerceEffectiveContractVo contract : contractList) {
            //生成账单
            //对应合同的应用标准。
            List<RentChargingBaseListVo> rentChargingBaseListVos = rentChargingBaseListByContractCode.get(contract.getContractCode());
            //获取这个合同下面的标准存在几个合同主体。  修改为核算组织。
            //   多个核算组织的时候怎么配置滞纳金比例。
            if(com.alibaba.nacos.common.utils.CollectionUtils.isEmpty(rentChargingBaseListVos)){
                log.info(contract.getContractCode()+"该合同号没绑定收费项目");
                continue;
            }
            // 实收应收 费用 主表中的核算组织信息。
            Map<Long, String> voucherMap = rentChargingBaseListVos.stream().filter(t -> t.getItemVoucherTenantInfoId() != null).collect(Collectors.toMap(RentChargingBaseListVo::getItemVoucherTenantInfoId, RentChargingBaseListVo::getItemAccountingOrgName, (k1, k2) -> k1));
            if(voucherMap==null){
                log.info(contract.getContractCode()+"该合同号没绑定核算组织");
                continue;
            }
            for (Long voucherTenantInfoId : voucherMap.keySet()) {
                if(receivableList!=null){
                    boolean isExist=receivableList.stream().anyMatch(item -> contract.getContractCode().equals(item.getContractCode()) && voucherTenantInfoId.equals(item.getVoucherTenantInfoId()));
                    //不存在的该合同对应核算组织数据更新
                    if(!isExist){
                        continue;
                    }
                }
                String accountingOrgName = voucherMap.get(voucherTenantInfoId);
                contract.setVoucherTenantInfoId(voucherTenantInfoId);
                contract.setAccountingOrgName(accountingOrgName);
                List<RentChargingBaseListVo> rentChargingBaseList = rentChargingBaseListVos.stream().filter(t -> voucherTenantInfoId.equals(t.getItemVoucherTenantInfoId()) || t.getType() == 2).collect(Collectors.toList());
                Long chargeId = createChargeByContractCodeAndVoucher(contract, rentChargingBaseList, currentDate, nowTime,false);
                chargeIds.add(chargeId);
            }
        }
        return chargeIds;
    }

    private Long createChargeByContractCodeAndVoucher(CommerceEffectiveContractVo contract, List<RentChargingBaseListVo> rentChargingBaseListVos, LocalDate currentDate, LocalDateTime nowTime,  boolean isFirstPeriod) {
        // 这次算首次 入参isFirst是true;
        LocalDate periodDate = contract.getRentStartDate();
        // 如果不是首次账期，periodDate取currentDate
        RentFeeChargeAddDto rentChargeDto = null;
        RentFeeReceivableAddDto rentReceivableDto = null;
        //判断是否是首次 因为是手动生成。 用账期日期和租赁开始时间比较 相同年月算为首月。
        boolean  isFirst=areYearMonthEqual(periodDate, currentDate.plusMonths(1));
        if (isFirst) {
            rentChargeDto = createRentCharge(contract, currentDate, nowTime, true);
            if (rentChargeDto == null) return null;
            rentReceivableDto = createRentReceivable(rentChargeDto, currentDate, nowTime, true);
        } else {
            rentChargeDto = createRentCharge(contract, currentDate, nowTime, false);
            if (rentChargeDto == null) return null;
            rentReceivableDto = createRentReceivable(rentChargeDto, currentDate, nowTime, false);
            // 如果不是首次账期，periodDate取账期年月的第一天
            periodDate = LocalDate.of(rentReceivableDto.getPeriodYear(), rentReceivableDto.getPeriodMonth(), 1);
        }

        //计算每个应用的应收金额
        int latePaymentStartDate = 0;
        //滞纳金税率
        BigDecimal itemTaxRate = BigDecimal.ZERO;
        for (RentChargingBaseListVo rentChargingBaseListVo : rentChargingBaseListVos) {
            //获取应收账单中的滞纳金结算日期 默认赋值的是5.
            if (rentChargingBaseListVo.getType() == 2) {
                latePaymentStartDate = rentChargingBaseListVo.getLatePaymentStartDate();
                rentReceivableDto.setReceivableDamagesDate(
                        LocalDate.of(rentReceivableDto.getPeriodYear(),
                                rentReceivableDto.getPeriodMonth(), latePaymentStartDate));
                itemTaxRate = rentChargingBaseListVo.getItemTaxRate();
                rentReceivableDto.setReceivableDamagesScale(rentChargingBaseListVo.getLatePaymentRate());
            }
        }
        //取出有效的计费周期 例：月度周期 或者  当前日期是符合季度规则的。
        List<RentChargingBaseListVo> effectiveStandards = getEffectiveStandards(rentChargingBaseListVos, periodDate, contract.getRentStartDate());
        //计算该合同每个收费项目的应收金额。
        RentFeeEffectiveContractDto rentFeeEffectiveContractDto = BeanUtils.copyProperties(contract, RentFeeEffectiveContractDto.class);
        //系统内置的标准 计算金额。需要通过计算的标准应用
        List<RentChargingBaseListVo> calcChargingBaseList = effectiveStandards.stream().filter((t) -> t.getCalType() == 1).collect(Collectors.toList());
        List<RentFeeEffectiveContractVo> rentFeeEffectiveContractVos =new LinkedList<>();
        if(com.alibaba.nacos.common.utils.CollectionUtils.isNotEmpty(calcChargingBaseList)){
            // 赋值属期时间范围：
            LocalDate periodStart = getFirstDayOfMonth(rentChargeDto.getPeriodYear(), rentChargeDto.getPeriodMonth());
            LocalDate periodEnd = getLastDayOfMonth(rentChargeDto.getPeriodYear(), rentChargeDto.getPeriodMonth());
            if (isFirst) {
                // 属期 首期默认给空。
                rentFeeEffectiveContractDto.setPeriodStart(null);
                rentFeeEffectiveContractDto.setPeriodEnd(null);
            } else {
                //判断账期和租赁结束日期是否是相同年月。 相同年月 属期结束日期=租赁结束日期。
                if (DateUtil.isSameYearMonth(contract.getRentEndDate(),LocalDate.of(rentChargeDto.getPeriodYear(),rentChargeDto.getPeriodMonth(),1))) {
                    periodEnd = contract.getRentEndDate();
                }
                rentFeeEffectiveContractDto.setPeriodStart(periodStart);
                rentFeeEffectiveContractDto.setPeriodEnd(periodEnd);
            }
            //赋值对应的标准应用对应的数据（包含月度和季度）
            for (RentChargingBaseListVo baseListVo : calcChargingBaseList) {
                if(isFirst){
                    baseListVo.setPeriodStart(null);
                    baseListVo.setPeriodEnd(null);
                }else{
                    //月度
                    if(baseListVo.getBillingPeriodUnit()==1){
                        baseListVo.setPeriodStart(periodStart);
                        baseListVo.setPeriodEnd(periodEnd);
                    }
                    //季度
                    if(baseListVo.getBillingPeriodUnit()==2){
                        baseListVo.setPeriodStart(periodStart);
                        LocalDate quarterEndDate = getQuarterEndDate(periodStart);
                        baseListVo.setPeriodEnd(quarterEndDate);
                        //判断季度的属期和租赁结束日期是否是相同年月。 相同年月 属期结束日期=租赁结束日期。
                        if (DateUtil.isSameYearMonth(contract.getRentEndDate(),quarterEndDate)) {
                            baseListVo.setPeriodEnd(contract.getRentEndDate());
                        }
                    }
                }
            }
            rentFeeEffectiveContractDto.setStandardCodeList(calcChargingBaseList);
            log.info("contract合同信息查询对应的费用数据：==========>"+JSON.toJSONString(rentFeeEffectiveContractDto));
            log.info("contract合同信息查询对应的费用数据入参属期开始时间：==========>"+JSON.toJSONString(rentFeeEffectiveContractDto.getPeriodStart()));
            log.info("contract合同信息查询对应的费用数据入参属期结束时间：==========>"+JSON.toJSONString(rentFeeEffectiveContractDto.getPeriodEnd()));
            log.info("contract合同信息查询对应的费用数据入参标准应用：==========>"+ JSON.toJSONString(effectiveStandards));
            //每个收费标准的结果。
            rentFeeEffectiveContractVos = calculateRuleProvider.unifyCalculate(rentFeeEffectiveContractDto);
            log.info("每个收费标准的结果数据：==========>"+JSON.toJSONString(rentFeeEffectiveContractVos));
        }
        //抽出固定金额塞到应该生成标准数据
        List<RentChargingBaseListVo> standardCodeListByFixedAmount = effectiveStandards.stream().filter((t) -> t.getCalType() == 2).collect(Collectors.toList());
        for (RentChargingBaseListVo item : standardCodeListByFixedAmount) {
            RentFeeEffectiveContractVo vo = new RentFeeEffectiveContractVo();
            vo.setStandardCode(item.getStandardCode());
            vo.setAmount(item.getManuallyAmount());
            rentFeeEffectiveContractVos.add(vo);
        }
        if(CollectionUtils.isEmpty(rentFeeEffectiveContractVos)){
            log.info("合同不存在标准应用：==========>" + rentFeeEffectiveContractDto);
            return null;
        }
        //  组装明细数据。
        List<RentFeeReceivableDetailAddDto> rentReceivableDetails = createRentReceivableDetails(rentReceivableDto, rentFeeEffectiveContractVos, effectiveStandards, isFirst);
        //给费用台账添加应收数据。
        BigDecimal receivableAmount = rentReceivableDetails.stream()
                .map(RentFeeReceivableDetailAddDto::getAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        rentChargeDto.setReceivableAmount(receivableAmount);
        rentChargeDto.setArrearsAmount(receivableAmount);
        //给应收台账添加应收数据。
        rentReceivableDto.setReceivableAmount(receivableAmount);

        //  获取实收台账、实收台账明细数据。 生成数据add数据。
        //实收直接从应收中获取。
        RentFeeActualAddDto rentFeeActualAddDto = BeanUtils.copyProperties(rentReceivableDto, RentFeeActualAddDto.class);
        List<RentFeeActualDetailAddDto> rentActualDetails = createRentActualDetails(rentFeeActualAddDto, rentFeeEffectiveContractVos, effectiveStandards, itemTaxRate);
        //生成数据并绑上ID。
        RentFeeChargeAddVo chargeVo = rentFeeChargeProvider.add(rentChargeDto);
        rentReceivableDto.setChargeId(chargeVo.getId());
        RentFeeReceivableAddVo receivableVo = rentFeeReceivableProvider.add(rentReceivableDto);
        Map<Long, Long> ChargingItemIdAndreceivableDetailIdMap = new HashMap<>();
        for (RentFeeReceivableDetailAddDto receivableDetail : rentReceivableDetails) {
            receivableDetail.setReceivableId(receivableVo.getId());
            receivableDetail.setCreateTime(nowTime);
            RentFeeReceivableDetailAddVo add = rentFeeReceivableDetailProvider.add(receivableDetail);
            ChargingItemIdAndreceivableDetailIdMap.put(receivableDetail.getChargingItemId(), add.getId());
        }
        rentFeeActualAddDto.setChargeId(chargeVo.getId());
        RentFeeActualAddVo actualAddVo = rentFeeActualProvider.add(rentFeeActualAddDto);
        for (RentFeeActualDetailAddDto actualDetail : rentActualDetails) {
            actualDetail.setActualId(actualAddVo.getId());
            actualDetail.setReceivableDetailId(ChargingItemIdAndreceivableDetailIdMap.get(actualDetail.getChargingItemId()));
            rentFeeActualDetailProvider.add(actualDetail);
        }
        //记录生成数据的日志。
        String logStr = rentChargeDto.getPeriodYear() + "/" + String.format("%02d", rentChargeDto.getPeriodMonth()) + "账期合同" + contract.getContractCode() + "数据生成完成";
        log.info("生成费用台账完成：==========>" + JSON.toJSONString(logStr));
        return chargeVo.getId();
    }

    /**
     * @param contract
     * @param currentDate
     * @param nowTime
     * @param isFirst     判断是否是首次生成的数据
     * @return
     */
    private RentFeeChargeAddDto createRentCharge(CommerceEffectiveContractVo contract, LocalDate currentDate, LocalDateTime nowTime, boolean isFirst) {
        //生成费用台账
        int monthValue = currentDate.getMonthValue();
        int year = currentDate.getYear();
        //生成下个月份
        monthValue = monthValue + 1;
        if (monthValue > 12) {
            monthValue = 1;
            year++;
        }
        //查询该合同下该账期是否存在费用台账 如果存在返回null  跳过创建。
        RentFeeChargeListDto query = new RentFeeChargeListDto();
        query.setTenantId(contract.getTenantId());
        query.setContractCode(contract.getContractCode());
        query.setPeriodYear(year);
        query.setPeriodMonth(monthValue);
        query.setVoucherTenantInfoId(contract.getVoucherTenantInfoId());
        RentFeeChargeGetVo rentFeeChargeGetVo = rentFeeChargeProvider.get(query);
        log.info("查询费用台账数据参数：========>"+query);
        log.info("查询费用台账数据：========>"+rentFeeChargeGetVo);
        if(rentFeeChargeGetVo!=null){
            log.info("该合同已存在该账期费用信息：========>核算组织："+contract.getAccountingOrgName()+",合同编号："+contract.getContractCode()+" 账期："+year+"/"+String.format("%02d",  monthValue));
            return null;
        }
        RentFeeChargeAddDto dto = new RentFeeChargeAddDto();
        if(contract.getType()==0){
            RoomsGetVo roomsGetVo = roomsProvider.get(contract.getRoomId());
            log.info("获取合同对应的铺位信息表铺位ID：========>"+contract.getRoomId());
            log.info("获取合同对应的铺位信息表合同编号：========>"+contract.getContractCode());
            log.info("获取合同对应的铺位信息表：========>",roomsGetVo);
            dto=BeanUtils.copyProperties(roomsGetVo, RentFeeChargeAddDto.class);
            if (dto == null) {
                log.info("获取铺位信息失败：==========>不生成账期数据"+year+"/"+String.format("%02d",  monthValue));
                return null;
            }
            dto.setRoomName(roomsGetVo.getName());
            dto.setStoreId(contract.getStoreId());
            dto.setShopType(contract.getShopType());
        }else{
            CommerceAdvertContractGetVo advertContract =  advertContractProvider.get(contract.getId());
            dto=BeanUtils.copyProperties(advertContract, RentFeeChargeAddDto.class);
            if (dto == null) {
                log.info("获取铺位信息失败：==========>不生成账期数据"+year+"/"+String.format("%02d",  monthValue));
                return null;
            }
            dto.setStoreId(advertContract.getAdvertName());
            dto.setRoomName(advertContract.getAdvertName());
            dto.setShopType(advertContract.getAdvertContractType()+2);
        }
        dto.setCreateTime(nowTime);
        dto.setCreateBy(null);
        dto.setCreateUser(null);
        dto.setCreateUserName(null);
        //获取铺位的基本信息
        dto.setRoomId(contract.getRoomId());
        dto.setBrandId(contract.getBrandId());
        dto.setBrandName(contract.getBrandName());
        dto.setContractCode(contract.getContractCode());
        dto.setFeeType(Objects.isNull(contract.getRentType()) ? null : RentTypeEnum.getName(contract.getRentType()).getName());
        dto.setPeriodYear(year);
        dto.setPeriodMonth(monthValue);
        dto.setVoucherTenantInfoId(contract.getVoucherTenantInfoId());
        dto.setAccountingOrgName(contract.getAccountingOrgName());
        dto.setPowerStatus(0);
        dto.setArrearsStatus(0);
        dto.setCreateTime(nowTime);
        dto.setRentEndDate(contract.getRentEndDate());
        return dto;

    }

    /**
     * @param rentFeeChargeAddDto 费用管理数据
     * @param currentDate         当前日期
     * @param nowTime             当前日期（年月日时分秒）
     * @param isFirst             是否第一次生成数据（新合同）
     * @return
     */
    private RentFeeReceivableAddDto createRentReceivable(RentFeeChargeAddDto rentFeeChargeAddDto, LocalDate currentDate, LocalDateTime nowTime, boolean isFirst) {
        //生成应收台账
        RentFeeReceivableAddDto dto = BeanUtils.copyProperties(rentFeeChargeAddDto, RentFeeReceivableAddDto.class);
        // 给默认值当个账期的5号先。
        dto.setReceivableDamagesDate(
                LocalDate.of(dto.getPeriodYear(),
                        dto.getPeriodMonth(), 5));
        dto.setReceivableDamagesScale(new BigDecimal(1));
        return dto;

    }

    /**
     * @param rentFeeReceivableAddDto     应收管理数据
     * @param rentFeeEffectiveContractVos 计算后的收费标准对应金额表
     * @param effectiveStandards   该合同下生效的收费项目数据。
     * @return
     */
    private List<RentFeeReceivableDetailAddDto> createRentReceivableDetails(RentFeeReceivableAddDto rentFeeReceivableAddDto, List<RentFeeEffectiveContractVo> rentFeeEffectiveContractVos, List<RentChargingBaseListVo>effectiveStandards,boolean isFirst) {
        //生成应收台账明细
        List<RentFeeReceivableDetailAddDto> createRentReceivableDetails = new ArrayList<>();
        // 赋值属期时间范围：
        LocalDate periodStart = getFirstDayOfMonth(rentFeeReceivableAddDto.getPeriodYear(), rentFeeReceivableAddDto.getPeriodMonth());
        LocalDate periodEnd = getLastDayOfMonth(rentFeeReceivableAddDto.getPeriodYear(), rentFeeReceivableAddDto.getPeriodMonth());
        //判断账期和租赁结束日期是否是相同年月。 相同年月 属期结束日期=租赁结束日期。
        if (DateUtil.isSameYearMonth(rentFeeReceivableAddDto.getRentEndDate(),LocalDate.of(rentFeeReceivableAddDto.getPeriodYear(),rentFeeReceivableAddDto.getPeriodMonth(),1))) {
            periodEnd=rentFeeReceivableAddDto.getRentEndDate();
        }
        //通过标准ID获取金额
        Map<String, BigDecimal> effectiveStandardMapByKey =  rentFeeEffectiveContractVos.stream().collect(Collectors.toMap(RentFeeEffectiveContractVo::getStandardCode, RentFeeEffectiveContractVo::getAmount, (k1, k2) -> k1));
        for (RentChargingBaseListVo rentChargingBaseListVo : effectiveStandards) {
            RentFeeReceivableDetailAddDto dto = BeanUtils.copyProperties(rentFeeReceivableAddDto, RentFeeReceivableDetailAddDto.class);
            //月度
            if(isFirst){
                dto.setPeriodStart(null);
                dto.setPeriodEnd(null);
            }else{
                if(rentChargingBaseListVo.getBillingPeriodUnit()==1){
                    dto.setPeriodStart(periodStart);
                    dto.setPeriodEnd(periodEnd);
                }
                //季度
                if(rentChargingBaseListVo.getBillingPeriodUnit()==2){
                    dto.setPeriodStart(periodStart);
                    LocalDate quarterEndDate = getQuarterEndDate(periodStart);
                    dto.setPeriodEnd(quarterEndDate);
                    if (DateUtil.isSameYearMonth(rentFeeReceivableAddDto.getRentEndDate(),quarterEndDate)) {
                        dto.setPeriodEnd(rentFeeReceivableAddDto.getRentEndDate());
                    }
                }
            }
            dto.setChargingItemId(rentChargingBaseListVo.getChargingItemId());
            dto.setChargingItemName(rentChargingBaseListVo.getItemFeeName());
            //赋值应收本金。
            dto.setAmount(effectiveStandardMapByKey.get(rentChargingBaseListVo.getStandardCode()));
            //  赋值合计金额 暂时不包含滞纳金和往期欠费。
            dto.setTotalAmount(effectiveStandardMapByKey.get(rentChargingBaseListVo.getStandardCode()));
            //  查询该合同该收费项目的历史欠费金额和欠费滞纳金
            log.info("合同"+rentFeeReceivableAddDto.getContractCode()+"账期月"+periodStart+"应收明细标准应用：==========>"+ JSON.toJSONString(rentChargingBaseListVo));
            log.info("合同"+rentFeeReceivableAddDto.getContractCode()+"账期月"+periodStart+"应收明细：==========>"+ JSON.toJSONString(dto));
            createRentReceivableDetails.add(dto);
        }
        return createRentReceivableDetails;

    }

    /**
     * @param rentFeeActualAddDto
     * @param rentFeeEffectiveContractVos
     * @param effectiveStandards
     * @param itemTaxRate                 滞纳金税率
     * @return
     */
    private List<RentFeeActualDetailAddDto> createRentActualDetails(RentFeeActualAddDto rentFeeActualAddDto, List<RentFeeEffectiveContractVo> rentFeeEffectiveContractVos, List<RentChargingBaseListVo>effectiveStandards, BigDecimal itemTaxRate) {
//生成应收台账明细
        List<RentFeeActualDetailAddDto> createRentActualDetails = new ArrayList<>();
        //通过标准ID获取金额
        Map<String, BigDecimal> effectiveStandardMapByKey =  rentFeeEffectiveContractVos.stream().collect(Collectors.toMap(RentFeeEffectiveContractVo::getStandardCode, RentFeeEffectiveContractVo::getAmount, (k1, k2) -> k1));
        for (RentChargingBaseListVo rentChargingBaseListVo : effectiveStandards) {
            RentFeeActualDetailAddDto dto = BeanUtils.copyProperties(rentFeeActualAddDto, RentFeeActualDetailAddDto.class);
            dto.setChargingItemId(rentChargingBaseListVo.getChargingItemId());
            dto.setChargingItemName(rentChargingBaseListVo.getItemFeeName());
            //赋值应收本金。
            dto.setReceivableAmount(effectiveStandardMapByKey.get(rentChargingBaseListVo.getStandardCode()));
            dto.setActualAmountTaxRate(rentChargingBaseListVo.getItemTaxRate());
//            dto.setArrearsAmountTaxRate(itemTaxRate);
            dto.setArrearsAmountTaxRate(rentChargingBaseListVo.getItemTaxRate());
            createRentActualDetails.add(dto);
        }
        return createRentActualDetails;

    }
    private List<RentChargingBaseListVo> getEffectiveStandards(List<RentChargingBaseListVo> rentChargingBaseListVos, LocalDate periodDate,LocalDate rentStartDate) {
        return rentChargingBaseListVos.stream().filter(
                (t) -> t.getType() == 1
                        //  当前日期是符合季度规则的。(t.getBillingPeriodUnit()==2&&checkDate(rentStartDate,periodDate))
                        && (t.getBillingPeriodUnit() == 1||(t.getBillingPeriodUnit()==2&&checkDate(rentStartDate,periodDate)))
                        && isWithinRange(t.getBindApplyPaymentTermSrart(), t.getBindApplyPaymentTermEnd(), periodDate)).collect(Collectors.toList());

    }

    public static boolean isWithinRange(LocalDate startDate, LocalDate endDate, LocalDate currentDate) {
        // 判断给定日期是否在起始日期和结束日期之间
        return !currentDate.isBefore(startDate) && !currentDate.isAfter(endDate);
    }

    public static LocalDate getFirstDayOfMonth(int year, int month) {
        // 获取指定年月的第一天
        return YearMonth.of(year, month).atDay(1);
    }

    public static LocalDate getLastDayOfMonth(int year, int month) {
        // 获取指定年月的最后一天，自动处理不同月份的天数以及闰年的二月
        YearMonth yearMonth = YearMonth.of(year, month);
        return yearMonth.atEndOfMonth();
    }

    /**
     * 判断check日期是否是按照符合季度生成规则的日期。
     * @param startDate
     * @param checkDate
     * @return
     */
    private static boolean checkDate(LocalDate startDate,LocalDate checkDate) {
        long month = calculateMonthsBetweenDates(startDate, checkDate);
        //第一次的季度
        if(month==0){
            return true;
        }
        //第二次的季度
        if(month==3){
            return true;
        }
        //第三次 如果是季度初 就+6
        if(startDate.getMonthValue()==1||startDate.getMonthValue()==4||startDate.getMonthValue()==7||startDate.getMonthValue()==10){
            if(month==6){
                return true;
            }
        }
        //第三次 如果是季度中 就+5
        if(startDate.getMonthValue()==2||startDate.getMonthValue()==5||startDate.getMonthValue()==8||startDate.getMonthValue()==11){
            if(month==5){
                return true;
            }
        }
        //第三次 如果是季度末 就+4
        if(startDate.getMonthValue()==3||startDate.getMonthValue()==6||startDate.getMonthValue()==9||startDate.getMonthValue()==12){
            if(month==4){
                return true;
            }
        }
        //第三次以后
        if(startDate.getMonthValue()==1||startDate.getMonthValue()==4||startDate.getMonthValue()==7||startDate.getMonthValue()==10){
            if((month-6)>0&&(month-6)%3==0){
                return true;
            }
        }
        if(startDate.getMonthValue()==2||startDate.getMonthValue()==5||startDate.getMonthValue()==8||startDate.getMonthValue()==11){

            if((month-5)>0&&(month-5)%3==0){
                return true;
            }
        }
        if(startDate.getMonthValue()==3||startDate.getMonthValue()==6||startDate.getMonthValue()==9||startDate.getMonthValue()==12){
            if((month-4)>0&&(month-4)%3==0){
                return true;
            }
        }
        return false;
    }

    public static long calculateMonthsBetweenDates(LocalDate startDate, LocalDate endDate) {
        YearMonth startYearMonth = YearMonth.from(startDate);
        YearMonth endYearMonth = YearMonth.from(endDate);

        return ChronoUnit.MONTHS.between(startYearMonth, endYearMonth);
    }
    public static LocalDate getQuarterEndDate(LocalDate date) {
        int month = date.getMonthValue();

        // 根据月份判断季度末的最后日期
        if (month >= 1 && month <= 3) { // Q1
            return LocalDate.of(date.getYear(), Month.MARCH, 31);
        } else if (month >= 4 && month <= 6) { // Q2
            return LocalDate.of(date.getYear(), Month.JUNE, 30);
        } else if (month >= 7 && month <= 9) { // Q3
            return LocalDate.of(date.getYear(), Month.SEPTEMBER, 30);
        } else { // Q4
            return LocalDate.of(date.getYear(), Month.DECEMBER, 31);
        }
    }
    public static boolean areYearMonthEqual(LocalDate date1, LocalDate date2) {
        YearMonth yearMonth1 = YearMonth.from(date1);
        YearMonth yearMonth2 = YearMonth.from(date2);

        return yearMonth1.equals(yearMonth2);
    }
}
