<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.commerce.mapper.CommerceStoreManageMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.commerce.entity.CommerceStoreManage">
        <id column="id" property="id" />
        <result column="tenant_id" property="tenantId" />
        <result column="tenant_name" property="tenantName" />
        <result column="ent_id" property="entId" />
        <result column="org_fid" property="orgFid" />
        <result column="org_fname" property="orgFname" />
        <result column="room_id" property="roomId" />
        <result column="refer_open_time" property="referOpenTime" />
        <result column="refer_close_time" property="referCloseTime" />
        <result column="refer_open_length" property="referOpenLength" />
        <result column="refer_value" property="referValue" />
        <result column="refer_flow_open_time" property="referFlowOpenTime" />
        <result column="refer_flow_close_time" property="referFlowCloseTime" />
        <result column="refer_flow_open_length" property="referFlowOpenLength" />
        <result column="refer_type" property="referType" />
        <result column="last_power_time" property="lastPowerTime" />
        <result column="last_power_value" property="lastPowerValue" />
        <result column="electricity_open_time" property="electricityOpenTime" />
        <result column="flow_open_time" property="flowOpenTime" />
        <result column="store_status" property="storeStatus" />
        <result column="message_notify" property="messageNotify" />
        <result column="patrol_show" property="patrolShow" />
        <result column="time_period" property="timePeriod" />
        <result column="create_by" property="createBy" />
        <result column="create_user" property="createUser" />
        <result column="create_user_name" property="createUserName" />
        <result column="create_time" property="createTime" />
        <result column="update_by" property="updateBy" />
        <result column="update_user" property="updateUser" />
        <result column="update_user_name" property="updateUserName" />
        <result column="update_time" property="updateTime" />
        <result column="del_status" property="delStatus" />
        <result column="version" property="version" />
    </resultMap>

    <update id="updateLastValueAndTime">
        update som_commerce_store_manage set last_power_value = #{dto.lastPowerValue}, last_power_time = #{dto.lastPowerTime} where del_status = 0 and room_id = #{dto.roomId}
    </update>

    <update id="updateOpenTime">
        update som_commerce_store_manage set electricity_open_time = #{dto.electricityOpenTime}, store_status = #{dto.storeStatus} where del_status = 0 and room_id = #{dto.roomId}
    </update>

    <update id="emptyTimeAndStatus">
        update som_commerce_store_manage set electricity_open_time = null, flow_open_time = null, store_status = 2 where tenant_id = #{id}
    </update>

</mapper>
