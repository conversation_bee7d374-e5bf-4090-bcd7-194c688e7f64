package com.seewin.som.rent.vo.req;

import com.seewin.consumer.data.ApiPageReq;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

/**
 * <p>
 * 收费标准关联门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Getter
@Setter
public class RentChargingStandardStoreRoomViewPageReq extends ApiPageReq {

    private static final long serialVersionUID = 1L;

    /**
     * 铺位号列表
     */
    @Schema(description = "铺位号列表")
    private List<String> roomNameList;

    /**
     * 绑定的门店id模糊搜索
     */
    @Schema(description = "绑定的门店id模糊搜索")
    private String storeIdLike;

//    /**
//     * 应用账期开始账期，传月首日期，例如2023-03-01
//     */
//    @Schema(description = "应用账期开始账期，传月首日期，例如2023-03-01")
//    private LocalDate applyPaymentTermSrart;
//
//    /**
//     * 应用账期结束账期，传月末日期，例如2023-06-30'
//     */
//    @Schema(description = "应用账期结束账期，传月末日期，例如2023-06-30")
//    private LocalDate applyPaymentTermEnd;

    /**
     * 品牌名称，输入框，模糊搜索
     */
    @Schema(description = "品牌名称")
    private String brandNameLike;

    /**
     * 供应商名称，输入框，模糊搜索
     */
    @Schema(description = "供应商名称")
    private String supplierNameLike;

    /**
     * 合同编号，输入框，模糊搜索
     */
    @Schema(description = "合同编号")
    private String contractCodeLike;

    /**
     * 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
     */
    @Schema(description = "合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位")
    private Integer mergeType;

    /**
     * 广告多经场地编号
     */
    @Schema(description = "广告多经场地编号")
    @Size(max=255,message = "广告多经场地编号最大长度不能超过255")
    private String advertName;

     /**
     * 计费规则（收费项目），输入框，模糊搜索
     */
    @Schema(description = "计费规则（收费项目）")
    private String principalChargingItemNameLike;


}
