package com.seewin.som.report.service.impl;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.metadata.style.WriteFont;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson2.JSON;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.enums.RoomStatusEnum;
import com.seewin.som.commerce.req.CommerceEntexitListDto;
import com.seewin.som.commerce.resp.CommerceEntexitListVo;
import com.seewin.som.report.service.ReportBrandClusterResultsService;
import com.seewin.som.report.vo.req.*;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.apache.poi.ss.usermodel.VerticalAlignment;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.report.provider.ReportBrandClusterResultsProvider;
import com.seewin.som.report.req.ReportBrandClusterResultsAddDto;
import com.seewin.som.report.req.ReportBrandClusterResultsEditDto;
import com.seewin.som.report.req.ReportBrandClusterResultsListDto;
import com.seewin.som.report.resp.ReportBrandClusterResultsAddVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsGetVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsListVo;

import com.seewin.som.report.vo.resp.ReportBrandClusterResultsListItem;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsGetResp;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsAddResp;

import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 品牌聚类结果信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Service
public class ReportBrandClusterResultsServiceImpl implements ReportBrandClusterResultsService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-report-mgt")
	private ReportBrandClusterResultsProvider reportBrandClusterResultsProvider;
	
	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<ReportBrandClusterResultsListItem> page(ReportBrandClusterResultsListReq listReq) {
        PageResp<ReportBrandClusterResultsListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);
		
        ReportBrandClusterResultsListDto queryDto = BeanUtils.copyProperties(listReq, ReportBrandClusterResultsListDto.class);
        
        PageQuery<ReportBrandClusterResultsListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<ReportBrandClusterResultsListVo> pageResult = reportBrandClusterResultsProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), ReportBrandClusterResultsListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public ReportBrandClusterResultsGetResp get(ReportBrandClusterResultsGetReq getReq) {
        ReportBrandClusterResultsGetVo getVo = reportBrandClusterResultsProvider.get(getReq.getId());

        ReportBrandClusterResultsGetResp getResp = BeanUtils.copyProperties(getVo, ReportBrandClusterResultsGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public ReportBrandClusterResultsAddResp add(ReportBrandClusterResultsAddReq addReq) {
        ReportBrandClusterResultsAddDto dto = BeanUtils.copyProperties(addReq, ReportBrandClusterResultsAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		
        ReportBrandClusterResultsAddVo addVo = reportBrandClusterResultsProvider.add(dto);

        ReportBrandClusterResultsAddResp addResp = BeanUtils.copyProperties(addVo, ReportBrandClusterResultsAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(ReportBrandClusterResultsEditReq editReq) {
        ReportBrandClusterResultsEditDto dto = BeanUtils.copyProperties(editReq, ReportBrandClusterResultsEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        reportBrandClusterResultsProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(ReportBrandClusterResultsDelReq delReq) {
        reportBrandClusterResultsProvider.delete(delReq.getId());
    }

    @Override
    public List<ReportBrandClusterResultsListItem> list(ReportBrandClusterResultsListReq listReq) {
        ReportBrandClusterResultsListDto dto = BeanUtils.copyProperties(listReq, ReportBrandClusterResultsListDto.class);
        return BeanUtils.copyProperties(reportBrandClusterResultsProvider.list(dto), ReportBrandClusterResultsListItem.class);
    }
}
