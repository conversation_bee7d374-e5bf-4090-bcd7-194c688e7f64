package com.seewin.som.commerce.service;

import com.seewin.som.commerce.entity.CommerceContract;
import com.baomidou.mybatisplus.extension.service.IService;
import com.seewin.som.commerce.req.CommerceChargeContractListDto;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.req.CommerceContractRentFeeExportDto;
import com.seewin.som.commerce.req.CommerceContractUpdateUserInputDto;
import com.seewin.som.commerce.resp.*;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface CommerceContractService extends IService<CommerceContract> {

    CommerceContract getLastContract(Long roomId);

    List<CommerceContract> historyList(CommerceContractListDto listDto);

    String getMaxShopId(Long roomId);

    Long getShopIdCount(Long roomId);

    CommerceContract selectByRoomIdAndApproveStatus(Long roomId, Integer approveStatus);

    /**
     * TODO
     * <AUTHOR>
     * @date 2024/5/16 17:49
     * @param billMonth
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractBillMonthVo>
     */
    List<CommerceContractBillMonthVo> selectContractByBillMonth(String billMonth);

    List<CommerceContractArchiveVo> findRentStartContract(LocalDate date);

    List<CommerceContract> findContractApproveOvertime(LocalDate date);

    /**
     * 获取每月账单的需要的合同
     * <AUTHOR>
     * @date 2024/6/26 15:16
     * @param type
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractMonthFeeVo>
     */
    List<CommerceContractMonthFeeVo> selectContractMonthFee(String type);

    /**
     * 本次合同的前一份合同信息
     * @param id
     * @return
     */
    CommerceContract getLastOneContract(Long id);

    List<CommerceEffectiveContractVo> selectEffectiveContract(LocalDate currentDate);

    List<CommerceEffectiveContractVo> selectEffectiveContractByContractCode(List<String> contractCodeList);

    CommerceEffectiveContractVo getRentFeeCalculateInfo(String contractCode);

    List<CommerceContractRentFeeExportVo> getContractFeeList(CommerceContractRentFeeExportDto exportDto);

    CommerceContractArchiveVo findSignContract(Long roomId);

    List<CommerceChargeContractListVo> listByReceivable(CommerceChargeContractListDto dto);

    void updateUserInput(CommerceContractUpdateUserInputDto dto);
}
