package com.seewin.som.commerce.vo.resp;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 开闭店管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
@HeadRowHeight(20)
@ContentRowHeight(25)
@ColumnWidth(20)
public class CommerceStoreManageDetailExp implements Serializable {

    private static final long serialVersionUID = 1L;

    @ColumnWidth(20)
    @ExcelProperty({"基础信息","项目名称"})
    private String tenantName;

    @ColumnWidth(20)
    @ExcelProperty({"基础信息","日期"})
    private String dataStr;

    @ColumnWidth(20)
    @ExcelProperty({"基础信息","门店ID"})
    private String storeId;

    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    @ColumnWidth(20)
    @ExcelProperty({"基础信息","门店状态"})
    private String roomStatus;


    @ColumnWidth(20)
    @ExcelProperty({"电表","参考开店时间"})
    private String referOpenTime;

    @ColumnWidth(20)
    @ExcelProperty({"电表","参考闭店时间"})
    private String referCloseTime;

    @ColumnWidth(20)
    @ExcelProperty({"电表","参考开店时长（小时）"})
    private String referOpenLength;

    @ColumnWidth(20)
    @ExcelProperty({"电表","开店时间"})
    private String electricityOpenTime;

    @ColumnWidth(20)
    @ExcelProperty({"电表","闭店时间"})
    private String electricityCloseTime;

    @ColumnWidth(20)
    @ExcelProperty({"电表","开店时长(小时)"})
    private String electricityOpenLength;


    @ColumnWidth(20)
    @ExcelProperty({"客流仪","参考开店时间"})
    private String referFlowOpenTime;

    @ColumnWidth(20)
    @ExcelProperty({"客流仪","参考闭店时间"})
    private String referFlowCloseTime;

    @ColumnWidth(20)
    @ExcelProperty({"客流仪","参考开店时长（小时）"})
    private String referFlowOpenLength;

    @ColumnWidth(20)
    @ExcelProperty({"客流仪","开店时间"})
    private String flowOpenTime;

    @ColumnWidth(20)
    @ExcelProperty({"客流仪","闭店时间"})
    private String flowCloseTime;

    @ColumnWidth(20)
    @ExcelProperty({"客流仪","开店时长(小时)"})
    private String flowOpenLength;


    // 0：正常； 1：异常 2：无状态-
    @ColumnWidth(20)
    @ExcelProperty({"开闭店状态","开闭店状态"})
    private String storeStatus;

}
