package com.seewin.som.commerce.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.seewin.som.commerce.entity.CommerceContract;
import com.seewin.som.commerce.mapper.CommerceContractMapper;
import com.seewin.som.commerce.req.CommerceChargeContractListDto;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.req.CommerceContractRentFeeExportDto;
import com.seewin.som.commerce.req.CommerceContractUpdateUserInputDto;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.commerce.service.CommerceContractService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Service
public class CommerceContractServiceImpl extends ServiceImpl<CommerceContractMapper, CommerceContract> implements CommerceContractService {

    @Override
    public CommerceContract getLastContract(Long roomId) {
        return this.baseMapper.getLastContract(roomId);
    }

    @Override
    public List<CommerceContract> historyList(CommerceContractListDto listDto) {
        return this.baseMapper.historyList(listDto);
    }

    @Override
    public String getMaxShopId(Long roomId) {
        return this.baseMapper.getMaxShopId(roomId);
    }

    @Override
    public Long getShopIdCount(Long roomId) {
        return this.baseMapper.getShopIdCount(roomId);
    }

    @Override
    public CommerceContract selectByRoomIdAndApproveStatus(Long roomId, Integer approveStatus) {
        return baseMapper.selectByRoomIdAndApproveStatus(roomId,approveStatus);
    }

    @Override
    public List<CommerceContractBillMonthVo> selectContractByBillMonth(String billMonth) {
        return baseMapper.selectContractByBillMonth(billMonth);
    }

    @Override
    public List<CommerceContractArchiveVo> findRentStartContract(LocalDate date) {
        return baseMapper.selectRentStartContract(date);
    }

    @Override
    public List<CommerceContract> findContractApproveOvertime(LocalDate date) {
        return baseMapper.selectContractApproveOvertime(date);
    }

    @Override
    public List<CommerceContractMonthFeeVo> selectContractMonthFee(String type) {
        return baseMapper.selectContractMonthFee(type);
    }

    @Override
    public CommerceContract getLastOneContract(Long id) {
        return baseMapper.getLastOneContract(id);
    }

    @Override
    public List<CommerceEffectiveContractVo> selectEffectiveContract(LocalDate currentDate) {
        return baseMapper.selectEffectiveContract(currentDate);
    }
    @Override
    public List<CommerceEffectiveContractVo> selectEffectiveContractByContractCode(List<String> contractCodeList) {
        return baseMapper.selectEffectiveContractByContractCode(contractCodeList);
    }

    @Override
    public CommerceEffectiveContractVo getRentFeeCalculateInfo(String contractCode) {
        return baseMapper.getRentFeeCalculateInfo(contractCode);
    }

    @Override
    public List<CommerceContractRentFeeExportVo> getContractFeeList(CommerceContractRentFeeExportDto exportDto) {
        return baseMapper.getContractFeeList(exportDto);
    }

    @Override
    public CommerceContractArchiveVo findSignContract(Long roomId) {
        return baseMapper.findSignContract(roomId);
    }

    @Override
    public List<CommerceChargeContractListVo> listByReceivable(CommerceChargeContractListDto dto) {
        return this.baseMapper.listByReceivable(dto);
    }

    @Override
    public void updateUserInput(CommerceContractUpdateUserInputDto dto) {
        UpdateWrapper<CommerceContract> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("contract_code", dto.getContractCode());
        updateWrapper.set("user_input",dto.getUserInput());
        this.baseMapper.update(null,updateWrapper);
    }
}
