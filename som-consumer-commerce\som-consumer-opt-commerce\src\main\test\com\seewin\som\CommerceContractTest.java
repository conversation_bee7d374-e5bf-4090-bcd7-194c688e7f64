package com.seewin.som;

import com.alibaba.fastjson2.JSONObject;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.som.commerce.service.CommerceContracEsignService;
import com.seewin.som.commerce.service.CommerceContractTemplateFieldService;
import com.seewin.som.commerce.service.CommerceContractTemplateService;
import com.seewin.som.commerce.utils.Esign.exception.EsignDemoException;
import com.seewin.som.commerce.utils.PinYinUtils;
import com.seewin.som.commerce.utils.WordUtils;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintOaSealResp;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintReceiptSealResp;
import com.seewin.som.commerce.vo.resp.ContractFieldTreeItem;
import io.undertow.servlet.spec.HttpServletResponseImpl;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.checkerframework.checker.units.qual.A;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;

import static org.mockito.Mockito.when;

@SpringBootTest
@RunWith(SpringRunner.class)
public class CommerceContractTest {
    @Mock
    private HttpServletRequest request;
    @Mock
    HttpServletResponse response;
    @Autowired
    private CommerceContractTemplateFieldService commerceContractTemplateFieldService;
    @Autowired
    private CommerceContractTemplateService commerceContractTemplateService;
    @Autowired
    private CommerceContracEsignService commerceContracEsignService;

    private static final String authUser="bab8ebce-de3a-4614-9306-3db5d1386d5b";
    @Test
    public void printPdf() {
        String templatePath = "D:\\A-WXX\\seewin\\code\\som-consumer-commerce\\som-consumer-opt-commerce\\src\\main\\resources\\word\\【固定租金】商铺租赁合同 (单主体）-替换了变量.docx";
        // 打印PDF
        Map<String, Object> map = new HashMap();
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            FileInputStream inputStream = new FileInputStream(templatePath);

            WordUtils.fillWord(response,inputStream, map, "预留协议",new ArrayList<>());
            System.out.println("PDF创建成功");
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    @Test
    public void tree(){
//        List<ContractFieldTreeItem> treeItems= commerceContractTemplateFieldService.tree();
//        System.out.println(JSONObject.toJSONString(treeItems));
        System.out.println(PinYinUtils.getFirstPinYin("朝阳广场"));;
    }
    @Test
    public void commerceContractTemplatePage(){
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateListReq listReq = new CommerceContractTemplateListReq();
        listReq.setPageNum(1);
        listReq.setPageSize(20);
        System.out.println(JSONObject.toJSONString(commerceContractTemplateService.page(listReq)));
    }
    @Test
    public void commerceContractTemplateGet(){
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateGetReq getReq=new CommerceContractTemplateGetReq();
        getReq.setId(1855797748306145282l);
        System.out.println(JSONObject.toJSONString(commerceContractTemplateService.get(getReq)));
    }
    @Test
    public void commerceContractTemplateAdd() {
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateAddReq addReq = new CommerceContractTemplateAddReq();
        String json = "{\"id\":\"1856573591345184770\",\"templateName\":\"1113测试\",\"templateFileId\":\"1856573582394605569\",\"templateType\":0,\"contractAttr\":0,\"contractType\":\"0\",\"contractTypeName\":null,\"contractAttrName\":null,\"remark\":null,\"templateFile\":{\"fileName\":\"新建 DOCX 文档.docx\",\"fileId\":\"1856573582394605569\"},\"variableFieldList\":[{\"fieldId\":\"9\",\"titleName\":\"基本信息\",\"fieldCode\":\"contractNo\",\"fieldName\":\"合同号\",\"editFlag\":0},{\"fieldId\":\"10\",\"titleName\":\"基本信息\",\"fieldCode\":\"registerNo\",\"fieldName\":\"合同登记（备案）号\",\"editFlag\":0},{\"fieldId\":\"11\",\"titleName\":\"基本信息\",\"fieldCode\":\"brandName\",\"fieldName\":\"品牌名称\",\"editFlag\":0},{\"fieldId\":\"12\",\"titleName\":\"基本信息\",\"fieldCode\":\"floorName\",\"fieldName\":\"楼层\",\"editFlag\":0},{\"fieldId\":\"13\",\"titleName\":\"基本信息\",\"fieldCode\":\"roomName\",\"fieldName\":\"铺位编号\",\"editFlag\":0},{\"fieldId\":\"14\",\"titleName\":\"基本信息\",\"fieldCode\":\"roomBuildArea\",\"fieldName\":\"建筑面积\",\"editFlag\":0}]}";
        BeanUtils.copyProperties(JSONObject.parseObject(json, CommerceContractTemplateAddReq.class), addReq);
        System.out.println(JSONObject.toJSONString(commerceContractTemplateService.add(addReq)));
    }
    @Test
    public void commerceContractTemplateEdit() {
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateEditReq editReq = new CommerceContractTemplateEditReq();
        String json = "{\"id\":\"1856573591345184770\",\"templateName\":\"1113测试\",\"templateFileId\":\"1856573582394605569\",\"templateType\":0,\"contractAttr\":0,\"contractType\":\"0\",\"contractTypeName\":null,\"contractAttrName\":null,\"remark\":null,\"templateFile\":{\"fileName\":\"新建 DOCX 文档.docx\",\"fileId\":\"1856573582394605569\"},\"variableFieldList\":[{\"fieldId\":\"9\",\"titleName\":\"基本信息\",\"fieldCode\":\"contractNo\",\"fieldName\":\"合同号\",\"editFlag\":0},{\"fieldId\":\"10\",\"titleName\":\"基本信息\",\"fieldCode\":\"registerNo\",\"fieldName\":\"合同登记（备案）号\",\"editFlag\":0},{\"fieldId\":\"11\",\"titleName\":\"基本信息\",\"fieldCode\":\"brandName\",\"fieldName\":\"品牌名称\",\"editFlag\":0},{\"fieldId\":\"12\",\"titleName\":\"基本信息\",\"fieldCode\":\"floorName\",\"fieldName\":\"楼层\",\"editFlag\":0},{\"fieldId\":\"13\",\"titleName\":\"基本信息\",\"fieldCode\":\"roomName\",\"fieldName\":\"铺位编号\",\"editFlag\":0},{\"fieldId\":\"14\",\"titleName\":\"基本信息\",\"fieldCode\":\"roomBuildArea\",\"fieldName\":\"建筑面积\",\"editFlag\":0}]}";
        BeanUtils.copyProperties(JSONObject.parseObject(json, CommerceContractTemplateAddReq.class), editReq);
        commerceContractTemplateService.edit(editReq);
    }
    @Test
    public void commerceContractTemplateGetFieldById() {
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateGetReq getReq = new CommerceContractTemplateGetReq();
        getReq.setId(1855797748306145282l);
     System.out.println(JSONObject.toJSONString(commerceContractTemplateService.getFieldById(getReq)));
    }
    @Test
    public void commerceContractTemplateList(){
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateListToReq listReq = new CommerceContractTemplateListToReq();
        System.out.println(JSONObject.toJSONString(commerceContractTemplateService.list(listReq)));
    }
    @Test
    public void commerceContractTemplateUpdateStatus(){
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractTemplateStatusReq editReq  = new CommerceContractTemplateStatusReq();
        editReq.setStatus(1);
        editReq.setIds(Arrays.asList(1855797748306145282l));
        commerceContractTemplateService.updateStatus(editReq);
    }

    @Test
    public void oaUploadFileTest() throws IOException, InterruptedException, EsignDemoException {
        HttpServletResponse response = new MockHttpServletResponse();

        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        CommerceContractPrintOaUploadFileReq uploadFileReq = new CommerceContractPrintOaUploadFileReq();
		uploadFileReq = new CommerceContractPrintOaUploadFileReq();
		uploadFileReq.setSignCompanyName("深圳市瑞维控股有限公司");
		uploadFileReq.setSealTypeName("公章");

		String templatePath = "C:\\Users\\<USER>\\Desktop\\【固定租金】商铺租赁合同（双主体通用） - 副本.docx";
		byte[] fileBytes = Files.readAllBytes(Paths.get(templatePath));
		CommerceContractPrintOaUploadFileReq.FileItem fileItem = new CommerceContractPrintOaUploadFileReq.FileItem();
		fileItem.setFileMapId("111111188888866666");
		fileItem.setFileName("【固定租金】商铺租赁合同（双主体通用） - 副本.docx");
		fileItem.setFileByte(fileBytes);

		String templatePath1 = "C:\\Users\\<USER>\\Desktop\\场地租赁广告位使用协议合同（验证后）.docx";
		byte[] fileBytes1 = Files.readAllBytes(Paths.get(templatePath1));

		CommerceContractPrintOaUploadFileReq.FileItem fileItem1 = new CommerceContractPrintOaUploadFileReq.FileItem();
		fileItem1.setFileMapId("111111188888888888");
		fileItem1.setFileName("场地租赁广告位使用协议合同（验证后）.docx");
		fileItem1.setFileByte(fileBytes1);

		List<CommerceContractPrintOaUploadFileReq.FileItem> fileList = new ArrayList<>();
		fileList.add(fileItem);
		fileList.add(fileItem1);
		uploadFileReq.setFileList(fileList);

        CommerceContractPrintOaSealResp sealResp = commerceContracEsignService.oaUploadFile(response, uploadFileReq);
    }

    @Test
    public void receiptUploadFileTest() throws IOException, InterruptedException, EsignDemoException {
        HttpServletResponse response = new MockHttpServletResponse();

        CommerceContractPrintReceiptUploadFileReq uploadFileReq = new CommerceContractPrintReceiptUploadFileReq();
        // 设置核算组织名称
		uploadFileReq.setAccountingOrgName("深圳市瑞维控股有限公司");
        uploadFileReq.setPageNum(1);
		// 本地文件路径列表（替换成你自己的路径）
		List<String> filePaths = new ArrayList<>();
		filePaths.add("C:\\Users\\<USER>\\Desktop\\收款收据_1747984167994（前端传来的）.pdf");

		List<MultipartFile> fileList = new ArrayList<>();

		for (String path : filePaths) {
			File file = new File(path);
			if (!file.exists()) {
				throw new RuntimeException("文件不存在: " + path);
			}

			try (FileInputStream fileInputStream = new FileInputStream(file)) {
				byte[] fileContent = new byte[(int) file.length()];
				fileInputStream.read(fileContent);
				MockMultipartFile multipartFile = new MockMultipartFile(
						"file",
						file.getName(),
						"application/octet-stream",
						fileContent
				);
				fileList.add(multipartFile);
			}
		}

		// 设置到请求参数中
        CommerceContractPrintReceiptSealResp sealResp = commerceContracEsignService.receiptUploadFile( response,uploadFileReq,fileList);
    }

    @Test
    public void receiptUploadFileInlineViewTest() throws IOException, InterruptedException, EsignDemoException {
        HttpServletResponse response = new MockHttpServletResponse();
        String downloadUrl = "https://esignoss.esign.cn/7439057380/eb791b32-5ed8-4fb6-989a-ceca9688e79d/temp_file_15101694342625838488.pdf?Expires=1748231667&OSSAccessKeyId=LTAI4G23YViiKnxTC28ygQzF&Signature=VyjppEd7NLY1eBQw5Ss57abfObY%3D";

        // 设置响应头，通知浏览器以内联方式预览 PDF
        response.setHeader("Content-Disposition", "inline;filename=\"file.pdf\"");
        response.setHeader("Content-Type", "application/pdf");

        // 获取 PDF 文件内容
        URL url = new URL(downloadUrl);
        InputStream inputStream = url.openStream();
        OutputStream outputStream = response.getOutputStream();

        // 将文件内容写入到响应输出流
        byte[] buffer = new byte[1024];
        int bytesRead;
        while ((bytesRead = inputStream.read(buffer)) != -1) {
            outputStream.write(buffer, 0, bytesRead);
        }

        inputStream.close();
        outputStream.flush();
    }


    public void downloadToFile( HttpServletResponse response) throws IOException {
        // 创建 ByteArrayOutputStream 用于生成响应内容
        ByteArrayOutputStream baos = new ByteArrayOutputStream();

        // 模拟生成响应内容
        String content = "Hello, World!";
        baos.write(content.getBytes());

        // 获取 ServletOutputStream
        ServletOutputStream servletOutputStream = response.getOutputStream();

        // 将 ByteArrayOutputStream 的内容写入 ServletOutputStream
        baos.writeTo(servletOutputStream);

        // 将 ByteArrayOutputStream 的内容写入本地文件
        File file = new File("D:\\系统\\桌面\\新建文件夹\\a.pdf"); // 替换为你的文件路径
        FileOutputStream fos = new FileOutputStream(file);
        baos.writeTo(fos);

        // 关闭流
        baos.close();
        servletOutputStream.close();
        fos.close();

        System.out.println("文件已成功保存到 " + file.getAbsolutePath());
    }

    }


