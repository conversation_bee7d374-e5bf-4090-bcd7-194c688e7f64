package com.seewin.som.iot.enums;


import lombok.Getter;

/**
 * 设备类型
 */
@Getter
public enum DeviceTypeEnum {

    SALE_DEVICE("sale", "xpdyj", "销售设备"),
    SALE_DEVICE_SECOND("sale", "shishu_xp", "二代销售设备"),
    SALE_DEVICE_LKLPAY("sale", "lkl", "销售设备"),
    FIRE_DEVICE("fire", "dhlr", "动火离人设备"),
    FLOW_DEVICE("flow", "passenger", "客流设备"),
    JYKL_DEVICE("flow", "jykl", "客流设备"),
    KSKL_DEVICE("flow", "ksws", "客流设备"),
    ;

    private final String deviceType;
    private final String iotDeviceType;
    private final String deviceName;

    DeviceTypeEnum(String deviceType, String iotDeviceType, String deviceName) {
        this.deviceType = deviceType;
        this.iotDeviceType = iotDeviceType;
        this.deviceName = deviceName;
    }
}
