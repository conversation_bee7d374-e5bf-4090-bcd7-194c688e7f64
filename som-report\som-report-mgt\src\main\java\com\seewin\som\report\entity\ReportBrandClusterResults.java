package com.seewin.som.report.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 品牌聚类结果信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Data
@TableName("som_report_brand_cluster_results")
public class ReportBrandClusterResults implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 簇序号
     */
    @TableField("cluster")
    private Integer cluster;

    /**
     * 业态
     */
    @TableField("commercial_type_name")
    private String commercialTypeName;

    /**
     * 平均销售坪效
     */
    @TableField("sales_per_square_foot_mean")
    private BigDecimal salesPerSquareFootMean;

    /**
     * 平均实用面积
     */
    @TableField("actual_area_mean")
    private BigDecimal actualAreaMean;

    /**
     * 平均销售笔数
     */
    @TableField("total_order_mean")
    private BigDecimal totalOrderMean;

    /**
     * 平均客单价
     */
    @TableField("average_order_value_mean")
    private BigDecimal averageOrderValueMean;

    /**
     * 平均租金坪效
     */
    @TableField("rent_per_square_foot_mean")
    private BigDecimal rentPerSquareFootMean;

    /**
     * 平均租售比
     */
    @TableField("rent_to_sales_ratio_mean")
    private BigDecimal rentToSalesRatioMean;

    /**
     * 平均进店转化率
     */
    @TableField("enter_conversion_rate_mean")
    private BigDecimal enterConversionRateMean;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;


}
