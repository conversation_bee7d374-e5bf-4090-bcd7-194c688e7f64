{"appName":"seewin-gateway","time":"2025-05-29 10:49:19","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-05-29 10:49:32","level":"INFO","class":"com.seewin.GateWayApp","method":"logStarted","line":"56","message":"Started GateWayApp in 17.78 seconds (process running for 22.756)"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:01","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:01","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:65443|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:07","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:07","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:65443|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:26","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:26","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:65443|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 10:55:31","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 10:55:31","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:49612|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:20:14","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:20:14","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:54561|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:21:05","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:21:05","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:54561|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:23:30","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:23:30","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:54561|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:33:05","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:33:05","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58085|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:34:46","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:34:46","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58085|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-29 11:36:24","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-29 11:36:24","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58085|Real-IP:0:0:0:0:0:0:0:1"}
