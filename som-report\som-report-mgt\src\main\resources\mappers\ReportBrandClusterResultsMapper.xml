<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.seewin.som.report.mapper.ReportBrandClusterResultsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="com.seewin.som.report.entity.ReportBrandClusterResults">
        <id column="id" property="id" />
        <result column="cluster" property="cluster" />
        <result column="commercial_type_name" property="commercialTypeName" />
        <result column="sales_per_square_foot_mean" property="salesPerSquareFootMean" />
        <result column="actual_area_mean" property="actualAreaMean" />
        <result column="total_order_mean" property="totalOrderMean" />
        <result column="average_order_value_mean" property="averageOrderValueMean" />
        <result column="rent_per_square_foot_mean" property="rentPerSquareFootMean" />
        <result column="rent_to_sales_ratio_mean" property="rentToSalesRatioMean" />
        <result column="enter_conversion_rate_mean" property="enterConversionRateMean" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="version" property="version" />
        <result column="del_status" property="delStatus" />
    </resultMap>

</mapper>
