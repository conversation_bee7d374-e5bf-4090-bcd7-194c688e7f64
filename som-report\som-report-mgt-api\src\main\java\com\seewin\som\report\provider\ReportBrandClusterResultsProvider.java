package com.seewin.som.report.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.util.exception.ServiceException;

import com.seewin.som.report.req.ReportBrandClusterResultsAddDto;
import com.seewin.som.report.req.ReportBrandClusterResultsEditDto;
import com.seewin.som.report.req.ReportBrandClusterResultsListDto;
import com.seewin.som.report.resp.ReportBrandClusterResultsAddVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsGetVo;
import com.seewin.som.report.resp.ReportBrandClusterResultsListVo;

import java.util.List;

/**
 * <p>
 * 品牌聚类结果信息表 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
public interface ReportBrandClusterResultsProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<ReportBrandClusterResultsListVo> page(PageQuery<ReportBrandClusterResultsListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<ReportBrandClusterResultsListVo> list(ReportBrandClusterResultsListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(ReportBrandClusterResultsListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportBrandClusterResultsGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    ReportBrandClusterResultsGetVo get(ReportBrandClusterResultsListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    ReportBrandClusterResultsAddVo add(ReportBrandClusterResultsAddDto dto) throws ServiceException;


    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    void edit(ReportBrandClusterResultsEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(ReportBrandClusterResultsListDto dto) throws ServiceException;
}
