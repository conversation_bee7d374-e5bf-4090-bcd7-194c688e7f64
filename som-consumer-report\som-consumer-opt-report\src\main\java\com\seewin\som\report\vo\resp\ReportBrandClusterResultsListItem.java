package com.seewin.som.report.vo.resp;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 品牌聚类结果信息表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class ReportBrandClusterResultsListItem implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 簇序号
     */
    @Schema(description = "簇序号")
    private Integer cluster;
    /**
     * 业态
     */
    @Schema(description = "业态")
    private String commercialTypeName;
    /**
     * 平均销售坪效
     */
    @Schema(description = "平均销售坪效")
    private BigDecimal salesPerSquareFootMean;
    /**
     * 平均实用面积
     */
    @Schema(description = "平均实用面积")
    private BigDecimal actualAreaMean;
    /**
     * 平均销售笔数
     */
    @Schema(description = "平均销售笔数")
    private BigDecimal totalOrderMean;
    /**
     * 平均客单价
     */
    @Schema(description = "平均客单价")
    private BigDecimal averageOrderValueMean;
    /**
     * 平均租金坪效
     */
    @Schema(description = "平均租金坪效")
    private BigDecimal rentPerSquareFootMean;
    /**
     * 平均租售比
     */
    @Schema(description = "平均租售比")
    private BigDecimal rentToSalesRatioMean;
    /**
     * 平均进店转化率
     */
    @Schema(description = "平均进店转化率")
    private BigDecimal enterConversionRateMean;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 更新时间
     */
    @Schema(description = "更新时间")
    private LocalDateTime updateTime;


}
