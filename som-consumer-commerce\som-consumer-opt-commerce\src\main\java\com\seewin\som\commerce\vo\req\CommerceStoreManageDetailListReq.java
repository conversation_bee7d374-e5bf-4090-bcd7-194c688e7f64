package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import com.seewin.consumer.data.ApiPageReq;

/**
 * <p>
 * 开闭店管理明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageDetailListReq extends ApiPageReq {

    /**
     * 开闭店管理id
     */
    @Schema(description = "开闭店管理id")
    private Long storeManageId;

    /**
     * 日期
     */
    @Schema(description = "日期/创建时间")
    private LocalDateTime createTime;

    /**
     * 创建时间开始
     */
    @Schema(description = "创建时间开始，格式yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    @Schema(description = "创建时间结束，格式yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

    /**
     * 开闭店状态： 0：正常； 1：异常  2：无状态-
     */
    @Schema(description = "开闭店状态： 0：正常； 1：异常  2：无状态-")
    private Integer storeStatus;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private String storeId;

    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    @Schema(description = "门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）")
    private String roomStatus;

    /**
     * 品牌id
     */
    @Schema(description = "品牌id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    /**
     * 参考开店时间
     */
    @Schema(description = "参考开店时间")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @Schema(description = "参考闭店时间")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @Schema(description = "参考开店时长(小时)")
    private BigDecimal referOpenLength;

    /**
     * 参考客流开店时间
     */
    @Schema(description = "参考客流开店时间")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @Schema(description = "参考客流闭店时间")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @Schema(description = "参考客流开店时长(小时)")
    private BigDecimal referFlowOpenLength;

    /**
     * 电表开店时间
     */
    @Schema(description = "电表开店时间")
    private String electricityOpenTime;

    /**
     * 电表闭店时间
     */
    @Schema(description = "电表闭店时间")
    private String electricityCloseTime;

    /**
     * 电表开店时长(小时)
     */
    @Schema(description = "电表开店时长(小时)")
    private BigDecimal electricityOpenLength;

    /**
     * 客流仪开店时间
     */
    @Schema(description = "客流仪开店时间")
    private String flowOpenTime;

    /**
     * 客流仪闭店时间
     */
    @Schema(description = "客流仪闭店时间")
    private String flowCloseTime;

    /**
     * 客流仪开店时长(小时)
     */
    @Schema(description = "客流仪开店时长(小时)")
    private BigDecimal flowOpenLength;


}
