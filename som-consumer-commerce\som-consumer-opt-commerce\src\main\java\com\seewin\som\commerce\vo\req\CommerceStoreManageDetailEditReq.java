package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 开闭店管理明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageDetailEditReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 开闭店管理id
     */
    @Schema(description = "开闭店管理id")
    @NotNull(message = "开闭店管理id不能为空")
    private Long storeManageId;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    @Size(max=255,message = "门店id最大长度不能超过255")
    private String storeId;

    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    @Schema(description = "门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）")
    @Size(max=255,message = "门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）最大长度不能超过255")
    private String roomStatus;

    /**
     * 品牌id
     */
    @Schema(description = "品牌id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    @Size(max=255,message = "品牌名称最大长度不能超过255")
    private String brandName;

    /**
     * 参考开店时间
     */
    @Schema(description = "参考开店时间")
    @Size(max=16,message = "参考开店时间最大长度不能超过16")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @Schema(description = "参考闭店时间")
    @Size(max=16,message = "参考闭店时间最大长度不能超过16")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @Schema(description = "参考开店时长(小时)")
    private BigDecimal referOpenLength;

    /**
     * 参考客流开店时间
     */
    @Schema(description = "参考客流开店时间")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @Schema(description = "参考客流闭店时间")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @Schema(description = "参考客流开店时长(小时)")
    private BigDecimal referFlowOpenLength;

    /**
     * 电表开店时间
     */
    @Schema(description = "电表开店时间")
    @Size(max=16,message = "电表开店时间最大长度不能超过16")
    private String electricityOpenTime;

    /**
     * 电表闭店时间
     */
    @Schema(description = "电表闭店时间")
    @Size(max=16,message = "电表闭店时间最大长度不能超过16")
    private String electricityCloseTime;

    /**
     * 电表开店时长(小时)
     */
    @Schema(description = "电表开店时长(小时)")
    private BigDecimal electricityOpenLength;

    /**
     * 客流仪开店时间
     */
    @Schema(description = "客流仪开店时间")
    @Size(max=16,message = "客流仪开店时间最大长度不能超过16")
    private String flowOpenTime;

    /**
     * 客流仪闭店时间
     */
    @Schema(description = "客流仪闭店时间")
    @Size(max=16,message = "客流仪闭店时间最大长度不能超过16")
    private String flowCloseTime;

    /**
     * 客流仪开店时长(小时)
     */
    @Schema(description = "客流仪开店时长(小时)")
    private BigDecimal flowOpenLength;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    @Schema(description = "开闭店状态： 0：正常； 1：异常")
    private Integer storeStatus;


}
