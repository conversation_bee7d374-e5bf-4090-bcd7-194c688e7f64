{"appName":"seewin-gateway","time":"2025-05-28 09:20:40","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：404 NOT_FOUND \"No static resource api/doc.html.\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:20:49","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：404 NOT_FOUND \"No static resource api/doc.html.\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:33","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：503 SERVICE_UNAVAILABLE \"Unable to find instance for som-consumer-adm-system\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:36","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：503 SERVICE_UNAVAILABLE \"Unable to find instance for som-consumer-adm-ent\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:24","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"153","message":"凭证过期:{\"accessType\":11,\"exp\":1748400085,\"jwtId\":\"4c4f8041-5f8b-461d-a222-7a5cfb9b45ec\",\"userId\":1780070065322266626,\"userType\":2}","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:49","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"153","message":"凭证过期:{\"accessType\":11,\"exp\":1748397338,\"jwtId\":\"1d210f1d-244b-4d4a-9def-cf5171686914\",\"userId\":1780070065322266626,\"userType\":2}","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-28 09:36:06","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"153","message":"凭证过期:{\"accessType\":11,\"exp\":1748404570,\"jwtId\":\"236c4f47-fe7d-4317-807b-cf5f779ac580\",\"userId\":1913038466292097025,\"userType\":2}","statck_trace":""}
