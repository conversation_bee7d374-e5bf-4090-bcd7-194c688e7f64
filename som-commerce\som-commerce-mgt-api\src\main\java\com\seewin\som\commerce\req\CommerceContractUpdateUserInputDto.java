package com.seewin.som.commerce.req;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * <p>
 * 合同传签-更新用户输入
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Getter
@Setter
public class CommerceContractUpdateUserInputDto implements Serializable {
    private static final long serialVersionUID = 1L;
    /**
     * 合同编号
     */
    private String contractCode;
    /**
     * 用户输入
     */
    private String userInput;
}
