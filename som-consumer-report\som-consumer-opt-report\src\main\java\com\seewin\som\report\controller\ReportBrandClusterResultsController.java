package com.seewin.som.report.controller;

import com.seewin.som.report.vo.req.*;
import com.seewin.som.report.vo.resp.ReportOperationReportListItem;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import com.seewin.som.report.service.ReportBrandClusterResultsService;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;

import com.seewin.som.report.vo.resp.ReportBrandClusterResultsListItem;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsGetResp;
import com.seewin.som.report.vo.resp.ReportBrandClusterResultsAddResp;


import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

import java.util.List;

/**
 * <p>
 * 品牌聚类结果信息表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-05-21
 */
@Tag(name = "品牌聚类结果信息表")
@RestController
@RequestMapping("reportBrandClusterResults")
public class ReportBrandClusterResultsController {

	@Autowired
	private ReportBrandClusterResultsService reportBrandClusterResultsService;
	
	@Operation(summary = "品牌聚类结果信息表查询", description = "权限码：opt:report:reportbrandclusterresults:page")
	@PostMapping(ApiMethod.PAGE)
	public ApiResponse<PageResp<ReportBrandClusterResultsListItem>> page(@RequestBody @Valid ReportBrandClusterResultsListReq listReq) {
		ApiResponse<PageResp<ReportBrandClusterResultsListItem>> result = new ApiResponse<>();

		PageResp<ReportBrandClusterResultsListItem> pageResp = reportBrandClusterResultsService.page(listReq);

		result.setData(pageResp);

		return result;
	}

	@Operation(summary = "品牌聚类结果信息表详情", description = "权限码：opt:report:reportbrandclusterresults:get")
	@GetMapping(ApiMethod.GET)
	public ApiResponse<ReportBrandClusterResultsGetResp> get(@Valid ReportBrandClusterResultsGetReq getReq) {
		ApiResponse<ReportBrandClusterResultsGetResp> result = new ApiResponse<>();

		ReportBrandClusterResultsGetResp getResp = reportBrandClusterResultsService.get(getReq);

		result.setData(getResp);

		return result;
	}

	@Operation(summary = "品牌聚类结果信息表新增", description = "权限码：opt:report:reportbrandclusterresults:add")
	@PostMapping(ApiMethod.ADD)
	public ApiResponse<ReportBrandClusterResultsAddResp> add(@RequestBody @Valid ReportBrandClusterResultsAddReq addReq) {
		ApiResponse<ReportBrandClusterResultsAddResp> result = new ApiResponse<>();

		ReportBrandClusterResultsAddResp addResp = reportBrandClusterResultsService.add(addReq);

		result.setData(addResp);

		return result;
	}

    @Operation(summary = "品牌聚类结果信息表编辑",description = "权限码：opt:report:reportbrandclusterresults:edit")
    @PostMapping(ApiMethod.EDIT)
    public ApiResponse edit(@RequestBody @Valid ReportBrandClusterResultsEditReq editReq) {
        ApiResponse result = new ApiResponse<>();

        reportBrandClusterResultsService.edit(editReq);

        return result;
    }
    
    @Operation(summary = "品牌聚类结果信息表删除",description = "权限码：opt:report:reportbrandclusterresults:del")
    @PostMapping(ApiMethod.DEL)
    public ApiResponse del(@RequestBody @Valid ReportBrandClusterResultsDelReq delReq) {
        ApiResponse result = new ApiResponse<>();
        reportBrandClusterResultsService.del(delReq);

        return result;
    }

	@Operation(summary = "品牌聚类结果信息表查询-不分页", description = "权限码：opt:report:reportbrandclusterresults:list")
	@PostMapping(ApiMethod.LIST)
	public ApiResponse<List<ReportBrandClusterResultsListItem>> list(@RequestBody @Valid ReportBrandClusterResultsListReq listReq) {
		ApiResponse<List<ReportBrandClusterResultsListItem>> result = new ApiResponse<>();
		List<ReportBrandClusterResultsListItem> listResp = reportBrandClusterResultsService.list(listReq);
		result.setData(listResp);
		return result;
	}
}
