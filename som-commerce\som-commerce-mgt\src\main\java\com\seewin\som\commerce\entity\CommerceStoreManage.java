package com.seewin.som.commerce.entity;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableField;
import java.io.Serializable;
import lombok.Data;

/**
 * <p>
 * 开闭店管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Data
@TableName("som_commerce_store_manage")
public class CommerceStoreManage implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id(项目id)
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称(项目名称)
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 铺位id
     */
    @TableField("room_id")
    private Long roomId;

    /**
     * 参考开店时间
     */
    @TableField("refer_open_time")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @TableField("refer_close_time")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @TableField("refer_open_length")
    private BigDecimal referOpenLength;

    /**
     * 参考值
     */
    @TableField("refer_value")
    private BigDecimal referValue;

    /**
     * 参考客流开店时间
     */
    @TableField("refer_flow_open_time")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @TableField("refer_flow_close_time")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @TableField("refer_flow_open_length")
    private BigDecimal referFlowOpenLength;

    /**
     * 参考类型 0-电表 1-客流
     */
    @TableField("refer_type")
    private Integer referType;

    /**
     * 最后时间
     */
    @TableField("last_power_time")
    private String lastPowerTime;

    /**
     * 最后数值
     */
    @TableField("last_power_value")
    private BigDecimal lastPowerValue;

    /**
     * 电表开店时间
     */
    @TableField("electricity_open_time")
    private String electricityOpenTime;

    /**
     * 客流仪开店时间
     */
    @TableField("flow_open_time")
    private String flowOpenTime;

    /**
     * 开闭店状态： 0：正常； 1：异常 2-无状态-
     */
    @TableField("store_status")
    private Integer storeStatus;

    /**
     * 消息通知开关： 0：关； 1：开
     */
    @TableField("message_notify")
    private Integer messageNotify;

    /**
     * 巡查显示开关： 0：关； 1：开
     */
    @TableField("patrol_show")
    private Integer patrolShow;

    /**
     * 异常时间间隔(分钟)
     */
    @TableField("time_period")
    private Integer timePeriod;

    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;


}
