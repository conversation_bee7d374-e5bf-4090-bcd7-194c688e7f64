package com.seewin.som.rent.vo.resp;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 收费标准关联门店表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Getter
@Setter
public class RentChargingStandardStoreRoomViewPageResp implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;
    /**
     * 所属收费标准id
     */
    @Schema(description = "所属收费标准id")
    private Long chargingStandardId;
        /**
     * 本金标准名称
     */
    @Schema(description = "本金标准名称")
    private String chargingStandardName;
     /**
     * 计费规则（收费项目）ID
     */
    @Schema(description = "计费规则（收费项目）ID")
    private Long chargingItemId;
    /**
     * 计费规则（收费项目）名称
     */
    @Schema(description = "计费规则（收费项目）名称")
    private String chargingItemName;
    /**
     * 公式描述
     */
    @Schema(description = "公式描述")
    private String expressionDesc;
    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    private String tenantName;
    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;
    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    private String orgFid;
    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    private String orgFname;
    /**
     * 空间ID路径
     */
    @Schema(description = "空间ID路径")
    private String fid;
    /**
     * 空间完整编码
     */
    @Schema(description = "空间完整编码")
    private String fcode;
    /**
     * 空间完整名称
     */
    @Schema(description = "空间完整名称")
    private String fname;
    /**
     * 房间ID
     */
    @Schema(description = "房间ID")
    private Long roomId;
    /**
     * 房间名称
     */
    @Schema(description = "房间名称")
    private String roomName;
    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private String storeId;
    /**
     * 合同编号
     */
    @Schema(description = "合同编号")
    private String contractCode;

    /**
     * 合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位
     */
    @Schema(description = "合并空间类型：0：正铺；1：正铺临促 2：广告位 3：多经点位")
    private Integer mergeType;

    /**
     * 广告多经场地编号
     */
    @Schema(description = "广告多经场地编号")
    @Size(max=255,message = "广告多经场地编号最大长度不能超过255")
    private String advertName;

    /**
     * 租赁起始时间
     */
    @Schema(description = "租赁起始时间")
    private LocalDate rentStartDate;

    /**
     * 租赁结束时间
     */
    @Schema(description = "租赁结束时间")
    private LocalDate rentEndDate;

    /**
     * 应用账期开始账期，传月首日期，例如2023-03-01
     */
    @Schema(description = "应用账期开始账期，传月首日期，例如2023-03-01")
    private LocalDate applyPaymentTermSrart;

    /**
     * 应用账期结束账期，传月末日期，例如2023-06-30'
     */
    @Schema(description = "应用账期结束账期，传月末日期，例如2023-06-30")
    private LocalDate applyPaymentTermEnd;

     /**
     * 品牌Id
     */
    @Schema(description = "品牌Id")
    private Long brandId;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "供应商id")
    private Long supplierId;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    private String supplierName;

    /**
     * 创建人id
     */
    @Schema(description = "创建人id")
    private Long createBy;
    /**
     * 创建人账号/手机号
     */
    @Schema(description = "创建人账号/手机号")
    private String createUser;
    /**
     * 创建人姓名/昵称
     */
    @Schema(description = "创建人姓名/昵称")
    private String createUserName;
    /**
     * 创建时间
     */
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
    /**
     * 修改人id
     */
    @Schema(description = "修改人id")
    private Long updateBy;
    /**
     * 修改人账号/手机号
     */
    @Schema(description = "修改人账号/手机号")
    private String updateUser;
    /**
     * 修改人姓名/昵称
     */
    @Schema(description = "修改人姓名/昵称")
    private String updateUserName;
    /**
     * 修改时间
     */
    @Schema(description = "修改时间")
    private LocalDateTime updateTime;


}
