package com.seewin.som.commerce.service.impl;

import com.seewin.som.commerce.entity.CommerceContractPrint;
import com.seewin.som.commerce.mapper.CommerceContractPrintMapper;
import com.seewin.som.commerce.resp.CommerceContractPrintGetVo;
import com.seewin.som.commerce.resp.CommerceContractPrintListVo;
import com.seewin.som.commerce.service.CommerceContractPrintService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

/**
 * <p>
 * 招商合同打印表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Service
public class CommerceContractPrintServiceImpl extends ServiceImpl<CommerceContractPrintMapper, CommerceContractPrint> implements CommerceContractPrintService {

    @Override
    public CommerceContractPrintGetVo getBasicInfoByContractCode(String contractCode) {
        return baseMapper.getBasicInfoByContractCode(contractCode);
    }

    @Override
    public CommerceContractPrintGetVo selectSignInfoBySignFlowId(String signFlowId) {
        return baseMapper.selectSignInfoBySignFlowId(signFlowId);
    }


    @Override
    public void updateSignStatusBySignFlowId(String signFlowId, Integer signStatus) {
        baseMapper.updateSignStatusBySignFlowId(signFlowId,signStatus);
    }


    @Override
    public String selectSealId(Integer signCompanyCode,Integer sealTypeCode) {
        return baseMapper.selectSealId(signCompanyCode,sealTypeCode);
    }

    @Override
    public Map<String, Object> getSignInfoBySignCompanyCode(Integer signCompanyCode) {
        return baseMapper.getSignInfoBySignCompanyCode(signCompanyCode);
    }

    @Override
    public Map<String, Object> selectSealIdByOaUploadReq(String signCompanyName,String sealTypeName) {
        return baseMapper.selectSealIdByOaUploadReq(signCompanyName,sealTypeName);
    }


    @Override
    public Integer selectCompanyCodeByLessor(String lessor) {
        return baseMapper.selectCompanyCodeByLessor(lessor);
    }


    @Override
    public List<CommerceContractPrintListVo> getOtherFileById(Long id) {
        return baseMapper.getOtherFileById(id);
    }


    @Override
    public List<CommerceContractPrintGetVo> getAllFileBySameContractId(String sameContractId) {
        return baseMapper.getAllFileBySameContractId(sameContractId);
    }

    @Override
    public void batchUpdateTemplateId(Long newTemplateId, Long templateId) {
         baseMapper.batchUpdateTemplateId(newTemplateId,  templateId);
    }
}
