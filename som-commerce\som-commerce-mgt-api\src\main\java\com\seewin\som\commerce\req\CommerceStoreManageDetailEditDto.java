package com.seewin.som.commerce.req;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * <p>
 * 开闭店管理明细表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageDetailEditDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 开闭店管理id
     */
    private Long storeManageId;

    /**
     * 门店id
     */
    private String storeId;

    /**
     * 门店状态（字典：未交付 no_delivered  未开业 no_open  已开	业 open  待续约 be_renewed  待撤场 awaited_withdrawal 已撤场 withdrawal）
     */
    private String roomStatus;

    /**
     * 品牌id
     */
    private Long brandId;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 参考开店时间
     */
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    private BigDecimal referOpenLength;

    /**
     * 参考客流开店时间
     */
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    private BigDecimal referFlowOpenLength;

    /**
     * 电表开店时间
     */
    private String electricityOpenTime;

    /**
     * 电表闭店时间
     */
    private String electricityCloseTime;

    /**
     * 电表开店时长(小时)
     */
    private BigDecimal electricityOpenLength;

    /**
     * 客流仪开店时间
     */
    private String flowOpenTime;

    /**
     * 客流仪闭店时间
     */
    private String flowCloseTime;

    /**
     * 客流仪开店时长(小时)
     */
    private BigDecimal flowOpenLength;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    private Integer storeStatus;

    /**
     * 修改人id
     */
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private LocalDateTime updateTime;


    /**
     * 创建时间开始
     */
    private LocalDateTime createTimeStart;

    /**
     * 创建时间结束
     */
    private LocalDateTime createTimeEnd;

}
