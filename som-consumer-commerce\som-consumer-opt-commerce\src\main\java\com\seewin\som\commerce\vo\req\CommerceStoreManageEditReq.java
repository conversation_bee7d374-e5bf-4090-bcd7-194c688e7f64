package com.seewin.som.commerce.vo.req;

import java.math.BigDecimal;
import java.io.Serializable;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 开闭店管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Getter
@Setter
public class CommerceStoreManageEditReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    private Long id;

    @Schema(description = "主键集合")
    private List<Long> idList;

    /**
     * 租户名称(项目名称)
     */
    @Schema(description = "租户名称(项目名称)")
    @Size(max=255,message = "租户名称(项目名称)最大长度不能超过255")
    private String tenantName;

    /**
     * 企业ID
     */
    @Schema(description = "企业ID")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @Schema(description = "所属组织ID路径")
    @Size(max=255,message = "所属组织ID路径最大长度不能超过255")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @Schema(description = "所属组织名称路径")
    @Size(max=255,message = "所属组织名称路径最大长度不能超过255")
    private String orgFname;

    /**
     * 铺位id
     */
    @Schema(description = "铺位id")
    private Long roomId;

    /**
     * 参考开店时间
     */
    @Schema(description = "参考开店时间")
    @Size(max=16,message = "参考开店时间最大长度不能超过16")
    private String referOpenTime;

    /**
     * 参考闭店时间
     */
    @Schema(description = "参考闭店时间")
    @Size(max=16,message = "参考闭店时间最大长度不能超过16")
    private String referCloseTime;

    /**
     * 参考开店时长(小时)
     */
    @Schema(description = "参考开店时长(小时)")
    private BigDecimal referOpenLength;

    /**
     * 参考值
     */
    @Schema(description = "参考值")
    private BigDecimal referValue;

    /**
     * 参考客流开店时间
     */
    @Schema(description = "参考客流开店时间")
    private String referFlowOpenTime;

    /**
     * 参考客流闭店时间
     */
    @Schema(description = "参考客流闭店时间")
    private String referFlowCloseTime;

    /**
     * 参考客流开店时长(小时)
     */
    @Schema(description = "参考客流开店时长(小时)")
    private BigDecimal referFlowOpenLength;

    /**
     * 参考类型 0-电表 1-客流
     */
    @Schema(description = "参考类型 0-电表 1-客流")
    private Integer referType;

    /**
     * 最后时间
     */
    @Schema(description = "最后时间")
    @Size(max=16,message = "最后时间最大长度不能超过16")
    private String lastPowerTime;

    /**
     * 最后数值
     */
    @Schema(description = "最后数值")
    private BigDecimal lastPowerValue;

    /**
     * 电表开店时间
     */
    @Schema(description = "电表开店时间")
    @Size(max=16,message = "电表开店时间最大长度不能超过16")
    private String electricityOpenTime;

    /**
     * 客流仪开店时间
     */
    @Schema(description = "客流仪开店时间")
    @Size(max=16,message = "客流仪开店时间最大长度不能超过16")
    private String flowOpenTime;

    /**
     * 开闭店状态： 0：正常； 1：异常
     */
    @Schema(description = "开闭店状态： 0：正常； 1：异常")
    private Integer storeStatus;

    /**
     * 消息通知开关： 0：关； 1：开
     */
    @Schema(description = "消息通知开关： 0：关； 1：开")
    private Integer messageNotify;

    /**
     * 巡查显示开关： 0：关； 1：开
     */
    @Schema(description = "巡查显示开关： 0：关； 1：开")
    private Integer patrolShow;

    /**
     * 异常时间间隔(分钟)
     */
    @Schema(description = "异常时间间隔(分钟)")
    private Integer timePeriod;


}
