package com.seewin.som.commerce.service;


import com.seewin.som.commerce.utils.Esign.exception.EsignDemoException;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintOaSealResp;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintReceiptSealResp;
import com.seewin.som.commerce.vo.resp.CommerceContractPrintSealResp;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;


/**
 * <p>
 * 招商合同打印表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
public interface CommerceContracEsignService {

    // 上传文件接口
    CommerceContractPrintSealResp uploadFile(HttpServletResponse response, CommerceContractPrintUploadFileReq addReq) throws InterruptedException, EsignDemoException, IOException;


    void esignCallBack(HttpServletResponse response, CommerceContractPrintEsignCallBackReq esignCallBackReq);


    CommerceContractPrintOaSealResp oaUploadFile(HttpServletResponse response, CommerceContractPrintOaUploadFileReq addReq) throws InterruptedException, EsignDemoException, IOException;


    CommerceContractPrintReceiptSealResp receiptUploadFile(HttpServletResponse response, CommerceContractPrintReceiptUploadFileReq addReq, List<MultipartFile> fileList) throws InterruptedException, EsignDemoException, IOException;

}
