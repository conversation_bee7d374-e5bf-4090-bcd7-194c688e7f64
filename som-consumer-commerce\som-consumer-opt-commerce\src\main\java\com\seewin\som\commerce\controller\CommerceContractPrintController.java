package com.seewin.som.commerce.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.seewin.som.commerce.service.CommerceContracEsignService;
import com.seewin.som.commerce.utils.Esign.exception.EsignDemoException;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.*;
import com.seewin.util.bean.BeanUtils;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.bind.annotation.*;

import com.seewin.som.commerce.service.CommerceContractPrintService;
import com.seewin.consumer.data.ApiMethod;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.consumer.vo.PageResp;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.net.URL;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 招商合同打印表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Tag(name = "招商合同打印表")
@RestController
@RequestMapping("commerceContractPrint")
public class CommerceContractPrintController {

	@Autowired
	private CommerceContractPrintService commerceContractPrintService;

	@Autowired
	private CommerceContracEsignService commerceContracEsignService;

	@Operation(summary = "招商合同打印表查询", description = "权限码：opt:commerce:commercecontractprint:page")
	@PostMapping(ApiMethod.PAGE)
	public ApiResponse<PageResp<CommerceContractPrintListItem>> page(@RequestBody @Valid CommerceContractPrintListReq listReq) {
		ApiResponse<PageResp<CommerceContractPrintListItem>> result = new ApiResponse<>();

		PageResp<CommerceContractPrintListItem> pageResp = commerceContractPrintService.page(listReq);

		result.setData(pageResp);

		return result;
	}

	@Operation(summary = "招商合同打印表详情", description = "权限码：opt:commerce:commercecontractprint:get")
	@GetMapping(ApiMethod.GET)
	public ApiResponse<CommerceContractPrintGetResp> get(@Valid CommerceContractPrintGetReq getReq) {
		ApiResponse<CommerceContractPrintGetResp> result = new ApiResponse<>();

		CommerceContractPrintGetResp getResp = commerceContractPrintService.get(getReq);

		result.setData(getResp);

		return result;
	}

	@Operation(summary = "招商合同打印表新增", description = "权限码：opt:commerce:commercecontractprint:add")
	@PostMapping(ApiMethod.ADD)
	public ApiResponse<CommerceContractPrintAddResp> add(@RequestBody @Valid CommerceContractPrintAddReq addReq) {
		ApiResponse<CommerceContractPrintAddResp> result = new ApiResponse<>();
		if (CollectionUtil.isEmpty(addReq.getContractCodeList())){
			throw new ServiceException("请选择合同");
		}
		if (CollectionUtil.isEmpty(addReq.getContractTemplateIdList())){
			throw new ServiceException("请选择模版");
		}
		CommerceContractPrintAddResp addResp = commerceContractPrintService.add(addReq);

		result.setData(addResp);

		return result;
	}

	@Operation(summary = "招商合同打印表编辑",description = "权限码：opt:commerce:commercecontractprint:edit")
	@PostMapping(ApiMethod.EDIT)
	public ApiResponse edit(@RequestBody @Valid CommerceContractPrintEditReq editReq) {
		ApiResponse result = new ApiResponse<>();

		commerceContractPrintService.edit(editReq);

		return result;
	}

	@Operation(summary = "招商合同打印表删除",description = "权限码：opt:commerce:commercecontractprint:del")
	@PostMapping(ApiMethod.DEL)
	public ApiResponse del(@RequestBody @Valid CommerceContractPrintDelReq delReq) {
		ApiResponse result = new ApiResponse<>();
		commerceContractPrintService.del(delReq);

		return result;
	}
	@Operation(summary = "招商合同打印表-导出合同PDF",description = "权限码：opt:commerce:commercecontractprint:exp")
	@PostMapping("exp")
	public CommerceContractPrintUrlResp exp(HttpServletResponse response, @RequestBody @Valid CommerceContractPrintGetReq getReq)throws EsignDemoException{
		ApiResponse<Boolean> result = new ApiResponse<>();
		CommerceContractPrintUrlResp urlResp = commerceContractPrintService.exp(response, getReq);
		return urlResp;
	}
	@Operation(summary = "招商合同打印表新增", description = "权限码：opt:commerce:commercecontractprint:addAdvert")
	@PostMapping("addAdvert")
	public ApiResponse<CommerceContractPrintAddResp> addAdvert(@RequestBody @Valid CommerceContractPrintAddReq addReq) {
		ApiResponse<CommerceContractPrintAddResp> result = new ApiResponse<>();
		if (CollectionUtil.isEmpty(addReq.getContractCodeList())){
			throw new ServiceException("请选择合同");
		}
		if (CollectionUtil.isEmpty(addReq.getContractTemplateIdList())){
			throw new ServiceException("请选择模版");
		}
		CommerceContractPrintAddResp addResp = commerceContractPrintService.addAdvert(addReq);

		result.setData(addResp);

		return result;
	}



	@Operation(summary = "合同文件上传至E签宝", description = "权限码：opt:commerce:commercecontractprint:uploadFile")
	@PostMapping("uploadFile")
	public ApiResponse<CommerceContractPrintSealResp> uploadFile(HttpServletResponse response,@RequestBody @Valid CommerceContractPrintUploadFileReq uploadFileReq) throws InterruptedException, EsignDemoException, IOException {
		ApiResponse result = new ApiResponse<>();
		CommerceContractPrintSealResp sealResp = commerceContracEsignService.uploadFile( response,uploadFileReq);
		result.setCode(sealResp.getCode());
		result.setMsg(sealResp.getMsg());
		result.setData(sealResp.getData());
		return result;
	}



	@Operation(summary = "发起签署后提供给E签宝的回调接口", description = "权限码：opt:commerce:commercecontractprint:esignCallBack")
	@PostMapping("esignCallBack")
	public ApiResponse esignCallBack(HttpServletResponse response,@RequestBody @Valid CommerceContractPrintEsignCallBackReq esignCallBackReq) throws InterruptedException, EsignDemoException, IOException {
		ApiResponse result = new ApiResponse<>();
		commerceContracEsignService.esignCallBack( response,esignCallBackReq);
		return result;
	}


	@Operation(summary = "提供给OA进行签章的接口", description = "权限码：opt:commerce:commercecontractprint:oaUploadFile")
	@PostMapping("oaUploadFile")
	public ApiResponse<CommerceContractPrintOaSealResp> oaUploadFile(HttpServletResponse response,@RequestBody @Valid CommerceContractPrintOaUploadFileReq uploadFileReq) throws InterruptedException, EsignDemoException, IOException {
		ApiResponse result = new ApiResponse<>();
		CommerceContractPrintOaSealResp sealResp = commerceContracEsignService.oaUploadFile( response,uploadFileReq);
		result.setCode(sealResp.getCode());
		result.setMsg(sealResp.getMsg());
		result.setData(sealResp.getData());
		return result;
	}


	@Operation(summary = "收据打印签章接口", description = "权限码：opt:commerce:commercecontractprint:receiptUploadFile")
	@PostMapping(path = "receiptUploadFile", consumes = MediaType.MULTIPART_FORM_DATA_VALUE)
	public ApiResponse<CommerceContractPrintReceiptSealResp> receiptUploadFile(HttpServletResponse response, @RequestPart("params") CommerceContractPrintReceiptUploadFileReq uploadFileReq, // 其他参数
																			   @RequestPart("fileList") List<MultipartFile> fileList) throws InterruptedException, EsignDemoException, IOException {
		ApiResponse result = new ApiResponse<>();
		CommerceContractPrintReceiptSealResp sealResp = commerceContracEsignService.receiptUploadFile( response,uploadFileReq,fileList);
		result.setCode(sealResp.getCode());
		result.setMsg(sealResp.getMsg());
		result.setData(sealResp.getData());
		return result;
	}


	@Operation(summary = "收据打印预览接口", description = "权限码：opt:commerce:commercecontractprint:receiptPreview")
	@PostMapping("receiptPreview")
	public void oaUploadFile(HttpServletResponse response,@RequestBody @Valid CommerceContractPrintEsignReceiptPreviewReq receiptPreviewReq) throws  IOException {
		// 设置响应头，通知浏览器以内联方式预览 PDF
		response.setHeader("Content-Disposition", "inline;filename=\"receipt.pdf\"");
		response.setHeader("Content-Type", "application/pdf");

		String downloadUrl = receiptPreviewReq.getDownloadUrl();
		URL url = new URL(downloadUrl);
		InputStream inputStream = url.openStream();
		OutputStream outputStream = response.getOutputStream();

		byte[] buffer = new byte[1024];
		int bytesRead;
		while ((bytesRead = inputStream.read(buffer)) != -1) {
			outputStream.write(buffer, 0, bytesRead);
		}
		inputStream.close();
		outputStream.flush();
	}
}
