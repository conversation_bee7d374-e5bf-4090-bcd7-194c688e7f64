package com.seewin.som.rent.listerner;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSON;
import com.seewin.ems.energy.provider.ElectricityBindObjectProvider;
import com.seewin.ems.energy.resp.ElectricityListVo;
import com.seewin.event.message.EventMessage;
import com.seewin.event.service.EventSubscribeListener;
import com.seewin.som.iot.provider.IotDeviceProvider;
import com.seewin.som.rent.enums.ApproveStatusEnum;
import com.seewin.som.rent.enums.PowerStatusEnum;
import com.seewin.som.rent.provider.*;
import com.seewin.som.rent.req.RentFeeActualDetailEditDto;
import com.seewin.som.rent.req.RentFeeChargeEditDto;
import com.seewin.som.rent.req.RentFeeExemptionDetailListDto;
import com.seewin.som.rent.req.RentFeeExemptionEditDto;
import com.seewin.som.rent.resp.RentFeeActualDetailGetVo;
import com.seewin.som.rent.resp.RentFeeChargeGetVo;
import com.seewin.som.rent.resp.RentFeeExemptionDetailListVo;
import com.seewin.som.workflow.provider.ProcessStatusEvent;
import io.seata.spring.annotation.GlobalTransactional;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.common.utils.JsonUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.ArrayList;
import java.util.List;

/**
 * <pre>
 * Description
 *
 * &#64;author pengweirong
 * @since 1.0
 *
 * Change History
 * Date  Modifier  DESC.
 * 2023/3/13  pengweirong  Initial version.
 * </pre>
 */
@Component
@Slf4j
public class WorkflowEventListener implements EventSubscribeListener {

    private String OUTAGE_APPROVE = "outage_approve";
    private String CHARGE_APPROVE = "charge_approve";
    private String EXEMPTION_APPROVE = "exemption_approve";

    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeChargeProvider rentFeeChargeProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualProvider rentFeeActualProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualDetailProvider rentFeeActualDetailProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableDetailProvider rentFeeReceivableDetailProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableProvider rentFeeReceivableProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeExemptionProvider rentFeeExemptionProvider;

    @DubboReference(providedBy = "ems-energy-mgt")
    private ElectricityBindObjectProvider electricityBindObjectProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotDeviceProvider iotDeviceProvider;

    public WorkflowEventListener() {
    }

    /**
     * 固定 workflowSubscriberService
     */
    @Override
    public String getSubscriberBeanName() {
        return "workflowSubscriberService";
    }

    /**
     * 标签，具体是业务流程分类的编码
     */
    @Override
    public String getTagExpression() {
        return "outage_approve || charge_approve || exemption_approve";
    }

    /**
     * 流程引起服务，主题固定是workflow_event
     */
    @Override
    public String getTopicName() {
        return "workflow_event";
    }

    /**
     * 根据tag具体处理
     */
    @Override
    public void subscribe(EventMessage eventMessage) {
        log.info("进入费用管理审批回调");
        try {
            ProcessStatusEvent event = JSON.parseObject(eventMessage.getMessageBody(), ProcessStatusEvent.class);
            if (OUTAGE_APPROVE.equals(eventMessage.getTags())) {
                // 断电审批流程
                outageApprove(event);
            } else if (CHARGE_APPROVE.equals(eventMessage.getTags())) {
                // 供电审批流程
                chargeApprove(event);
            }
            else if (EXEMPTION_APPROVE.equals(eventMessage.getTags())) {
                // 优惠减免审批流程
                exemption(event);
            }
        } catch (Exception e) {
            log.error(eventMessage.getTopicName() + ":" + eventMessage.getTags() + ":" + e.getMessage(), e);
        }
    }

    /**
     * 断电审批流程
     */
    void outageApprove(ProcessStatusEvent event) {
        log.info("断电审批流程回调...");
        Long bizId = event.getBizId();
        log.info("bizId:{}", bizId);
        if (event.getStatus().equals(1)) {
            //审批通过
            RentFeeChargeEditDto dto = new RentFeeChargeEditDto();
            dto.setId(bizId);
            dto.setOutageApproveStatus(ApproveStatusEnum.approve.getCode());
            dto.setPowerStatus(PowerStatusEnum.outage.getCode());
            rentFeeChargeProvider.edit(dto);
            // 发起智能断电
            RentFeeChargeGetVo chargeGetVo = rentFeeChargeProvider.get(bizId);
            if (chargeGetVo!=null && chargeGetVo.getRoomId()!=null){
                Long roomId = chargeGetVo.getRoomId();
                log.info("断电的铺位id:{}, 铺位号:{}", roomId, chargeGetVo.getRoomName());
                List<ElectricityListVo> electricityDeviceList = electricityBindObjectProvider.getElectricityListByRoomId(roomId);
                log.info("electricityDeviceList size:{}", electricityDeviceList.size());
                if (CollectionUtil.isNotEmpty(electricityDeviceList)){
                    for (ElectricityListVo electricityVo : electricityDeviceList) {
                        log.info("deviceSn:{}", electricityVo.getDeviceSn());
                        iotDeviceProvider.switchElectricity(electricityVo.getDeviceSn(), Boolean.FALSE);
                        log.info("断电成功...");
                    }
                }
            }
        } else if (event.getStatus().equals(2)) {
            //驳回
            RentFeeChargeEditDto dto = new RentFeeChargeEditDto();
            dto.setId(bizId);
            dto.setPowerStatus(PowerStatusEnum.charge.getCode());
            dto.setOutageApproveStatus(ApproveStatusEnum.rejected.getCode());
            rentFeeChargeProvider.edit(dto);
        }
    }

    /**
     * 供电审批流程
     */
    void chargeApprove(ProcessStatusEvent event) {
        log.info("供电审批流程回调...");
        Long bizId = event.getBizId();
        if (event.getStatus().equals(1)) {
            //审批通过
            RentFeeChargeEditDto dto = new RentFeeChargeEditDto();
            dto.setId(bizId);
            dto.setChargeApproveStatus(ApproveStatusEnum.approve.getCode());
            dto.setPowerStatus(PowerStatusEnum.charge.getCode());
            rentFeeChargeProvider.edit(dto);
            // 恢复供电
            RentFeeChargeGetVo chargeGetVo = rentFeeChargeProvider.get(bizId);
            if (chargeGetVo!=null && chargeGetVo.getRoomId()!=null){
                Long roomId = chargeGetVo.getRoomId();
                log.info("恢复供电的铺位id:{}, 铺位号:{}", roomId, chargeGetVo.getRoomName());
                List<ElectricityListVo> electricityDeviceList = electricityBindObjectProvider.getElectricityListByRoomId(roomId);
                log.info("electricityDeviceList size:{}", electricityDeviceList.size());
                if (CollectionUtil.isNotEmpty(electricityDeviceList)){
                    for (ElectricityListVo electricityVo : electricityDeviceList) {
                        log.info("deviceSn:{}", electricityVo.getDeviceSn());
                        iotDeviceProvider.switchElectricity(electricityVo.getDeviceSn(), Boolean.TRUE);
                        log.info("恢复供电成功...");
                    }
                }
            }
        } else if (event.getStatus().equals(2)) {
            //驳回
            RentFeeChargeEditDto dto = new RentFeeChargeEditDto();
            dto.setId(bizId);
            dto.setPowerStatus(PowerStatusEnum.outage.getCode());
            dto.setChargeApproveStatus(ApproveStatusEnum.rejected.getCode());
            rentFeeChargeProvider.edit(dto);
        }
    }

    /**
     * 费用减免流程
     * @param event
     */
    @GlobalTransactional(rollbackFor = Exception.class)
    void exemption(ProcessStatusEvent event) {
        log.info("费用减免流程回调...");
        LocalDate now = LocalDate.now();
        LocalDateTime nowTime =  LocalDateTime.now();
        Long bizId = event.getBizId();
        RentFeeExemptionEditDto dto = new RentFeeExemptionEditDto();
        dto.setId(bizId);
        dto.setApproveDate(nowTime);
        if (event.getStatus().equals(1)) {
            dto.setApproveStatus(ApproveStatusEnum.approve.getCode());
            //自动优惠减免转换成手动。
/*            List<RentFeeExemptionDetailListVo> exemptionDetailList = rentFeeExemptionProvider.getRentFeeExemptionDetailListById(bizId);
            // 计算次月的年月
            YearMonth nextMonth = YearMonth.from(now).plusMonths(1);
            // 提取次月的年份和月份
            int nextYear = nextMonth.getYear();
            int nextMonthValue = nextMonth.getMonthValue();
            for (RentFeeExemptionDetailListVo detailListVo : exemptionDetailList) {
                //先修改实收里面的数据。然后执行SQL修改其他关联表数据
                //判断账期是否是小于等于当前月的次月  不用考虑未来账期的减免。由定时任务执行。
                int year = detailListVo.getPeriodYear();
                int month = detailListVo.getPeriodMonth();
                if ((year < nextYear) || (year == nextYear && month <= nextMonthValue)) {
                    //查询条件
                    RentFeeExemptionDetailListDto editDto = new RentFeeExemptionDetailListDto();
                    editDto.setContractCode(detailListVo.getContractCode());
                    editDto.setPeriodYear(detailListVo.getPeriodYear());
                    editDto.setPeriodMonth(detailListVo.getPeriodMonth());
                    editDto.setChargingItemId(detailListVo.getChargingItemId());
                    //查询实收明细ID再修改
                    RentFeeActualDetailGetVo actualDetail = rentFeeExemptionProvider.getActualDetailByContractCode(editDto);
                    //修改实收明细数据。并获取涉及到修改的合同对应的账期
                    RentFeeActualDetailEditDto actualDetailEditDto=new RentFeeActualDetailEditDto();
                    actualDetailEditDto.setReceivableAmount(detailListVo.getReceivableAmount());
                    actualDetailEditDto.setArrearsAmount(detailListVo.getArrearsAmount());
                    actualDetailEditDto.setId(actualDetail.getId());
                    rentFeeActualDetailProvider.edit(actualDetailEditDto);
                    //修改数据
                    editDto.setVoucherTenantInfoId(actualDetail.getVoucherTenantInfoId());
                    rentFeeExemptionProvider.updateChargeByContractCode(editDto);
                    rentFeeExemptionProvider.updateActualByContractCode(editDto);
                    rentFeeExemptionProvider.updateReceivableByContractCode(editDto);
                    rentFeeExemptionProvider.updateReceivableDetailByContractCode(editDto);
                }
            }*/
            rentFeeExemptionProvider.edit(dto);
        } else if (event.getStatus().equals(2)) {
            //驳回
            dto.setApproveStatus(ApproveStatusEnum.rejected.getCode());
            rentFeeExemptionProvider.edit(dto);
        }
    }
}
