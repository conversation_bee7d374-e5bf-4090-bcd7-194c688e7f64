package com.seewin.som.commerce.vo.req;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Getter;
import lombok.Setter;

import java.io.InputStream;
import java.io.Serializable;
import java.util.List;

/**
 * <p>
 * 招商合同打印表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-11
 */
@Getter
@Setter
public class CommerceContractPrintOaUploadFileReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用章公司编码
     */
    @Schema(description = "用章公司")
    @NotNull(message = "用章公司不能为空")
    private String signCompanyName;

    /**
     * 用章类型编码
     */
    @Schema(description = "用章类型")
    @NotNull(message = "用章类型不能为空")
    private String sealTypeName;

    /**
     * 文件对象数组，包含文件输出流和文件ID
     */
    @Schema(description = "文件对象数组")
    @NotNull(message = "文件对象数组")
    private List<FileItem> fileList;

    /**
     * 文件项
     */
    @Getter
    @Setter
    public static class FileItem {
        /**
         * 文件ID
         */
        @Schema(description = "文件ID")
        @NotNull(message = "文件ID不能为空")
        private String fileMapId;

        /**
         * 文件ID
         */
        @Schema(description = "文件名")
        @NotNull(message = "文件名不能为空")
        private String fileName;

        /**
         * 文件字节数组
         */
        @Schema(description = "文件字节数组")
        @NotNull(message = "文件字节数组")
        private byte[] fileByte;


    }



}
