package com.seewin.som.commerce.enums;

// ReviewStatus状态定义
public enum ReviewStatus {
    CANCELLED(0, "预决策已取消"),
    FAILED(1, "预决策比对未通过，请检查"),
    PASSED(2, "预决策比对通过");

    private final int code;
    private final String name;

    ReviewStatus(int code, String name) {
        this.code = code;
        this.name = name;
    }
    public int getCode() { return code; }
    public String getName() { return name; }
}
