package com.seewin.som.iot.listener;


import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.seewin.ems.energy.provider.ElectricityProvider;
import com.seewin.ems.energy.provider.WaterMeterDataProvider;
import com.seewin.ems.energy.provider.WaterMeterProvider;
import com.seewin.ems.energy.req.*;
import com.seewin.ems.energy.resp.ElectricityListVo;
import com.seewin.ems.energy.resp.WaterMeterDataGetVo;
import com.seewin.ems.energy.resp.WaterMeterGetVo;
import com.seewin.ems.energy.resp.WaterMeterListVo;
import com.seewin.event.message.EventMessage;
import com.seewin.event.service.EventSubscribeListener;
import com.seewin.redis.util.RedisUtil;
import com.seewin.som.commerce.provider.CommerceBrandProvider;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.req.CommerceContractListDto;
import com.seewin.som.commerce.resp.CommerceBrandGetVo;
import com.seewin.som.commerce.resp.CommerceContractGetVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.iot.listener.vo.electricity.YmdbSwitchResult;
import com.seewin.som.iot.listener.vo.fire.FireAlarmDataItem;
import com.seewin.som.iot.listener.vo.kl.*;
import com.seewin.som.iot.listener.vo.lkl.PayDataItem;
import com.seewin.som.iot.listener.vo.lkl.PayDetail;
import com.seewin.som.iot.listener.vo.water.*;
import com.seewin.som.iot.listener.vo.xp.DateItem;
import com.seewin.som.iot.listener.vo.xp.ParseData;
import com.seewin.som.iot.listener.vo.xp.XpDataItem;
import com.seewin.som.iot.listener.vo.xp.XpStatus;
import com.seewin.som.iot.provider.*;
import com.seewin.som.iot.req.*;

import com.seewin.som.iot.resp.*;
import com.seewin.som.report.provider.ReportMasterSaleDataProvider;
import com.seewin.som.report.req.ReportMasterSaleDataAddDto;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.req.SysFileAddDto;
import com.seewin.som.storage.resp.SysFileAddVo;
import com.seewin.som.wx.provider.OcrProvider;
import com.seewin.system.service.FileService;
import com.seewin.system.vo.FileInfo;
import com.seewin.util.bean.BeanUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.net.URL;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.seewin.som.iot.enums.DeviceTypeEnum.JYKL_DEVICE;
import static com.seewin.som.iot.enums.DeviceTypeEnum.KSKL_DEVICE;

@Component
@Slf4j
public class IotDeviceEventListener implements EventSubscribeListener {
    private static final Logger XP_LOG = LoggerFactory.getLogger("xpLog");

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFlowDataProvider iotFlowDataProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleDataProvider iotSaleDataProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleBindProvider iotSaleBindProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFlowBindProvider iotFlowBindProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotDeviceProvider iotDeviceProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleTemplateProvider iotSaleTemplateProvider;

    @DubboReference(providedBy = "som-report-mgt")
    private ReportMasterSaleDataProvider reportMasterSaleDataProvider;
    @DubboReference(providedBy = "som-integrate-wx")
    private OcrProvider ocrProvider;
    @DubboReference(providedBy = "som-iot-mgt")
    private IotFireBindProvider iotFireBindProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotFireAlarmProvider iotFireAlarmProvider;

    @DubboReference(providedBy = "som-iot-mgt")
    private IotSaleFailMsgProvider iotSaleFailMsgProvider;

    @DubboReference(providedBy = "som-storage-mgt")
    private SysFileProvider sysFileProvider;
    @DubboReference(providedBy = "som-iot-mgt")
    private IotDevicePointProvider iotDevicePointProvider;

    @DubboReference(providedBy = "ems-energy-mgt")
    private ElectricityProvider electricityProvider;

    @DubboReference(providedBy = "ems-energy-mgt")
    private WaterMeterProvider waterMeterProvider;

    @DubboReference(providedBy = "ems-energy-mgt")
    private WaterMeterDataProvider waterMeterDataProvider;

    @Autowired
    private FileService fileService;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceBrandProvider commerceBrandProvider;
    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;
    @DubboReference(providedBy = "som-iot-mgt")
    private IotDeviceOnlineProvider iotDeviceOnlineProvider;

    @Override
    public String getSubscriberBeanName() {
        return "iotDeviceSubscriberService";
    }

    @Override
    public String getTagExpression() {
        return "xp || shishu_data || kl || xp_status || shishu_status || kl_status || jykl || jykl_status || jykl_setting_query || dhlr_status || dhlr || lkl_pay_notify || lkl_status || kskl || kskl_setting || kskl_pic || kskl_status || ymdb_switch_result || ymdb_status || ymsb_data || ymsb_mqtt_data || ymsb_status || ymsb_mqtt_status";
    }

    @Override
    public String getTopicName() {
        return "iot";
    }

    private static final DateTimeFormatter IOT_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-M-d'T'HH:mm:ss");

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void subscribe(EventMessage eventMessage) {
        try {
            // 一代小票机或者二代小票机
            if (eventMessage.getTags().equals("xp") ||  eventMessage.getTags().equals("shishu_data")) {
                handlerXPData(eventMessage);
            } else if (eventMessage.getTags().equals("kl")) {
                handlerKlData(eventMessage);
                // 一代小票机状态或者二代小票机状态
            } else if (eventMessage.getTags().equals("xp_status") || eventMessage.getTags().equals("shishu_status")) {
                handlerXPStatus(eventMessage);
            } else if (eventMessage.getTags().equals("kl_status")) {
                handlerKLStatus(eventMessage);
            } else if (eventMessage.getTags().equals("jykl")) {
                handlerAreaFlowData(eventMessage);
            } else if (eventMessage.getTags().equals("jykl_status")) {//处理同普通客流设备
                handlerKLStatus(eventMessage);
            } else if (eventMessage.getTags().equals("jykl_setting_query")) {
                handlerJyklSetting(eventMessage);
            } else if (eventMessage.getTags().equals("dhlr_status")) {
                handlerFireStatus(eventMessage);
            } else if (eventMessage.getTags().equals("dhlr")) {
                handlerFireData(eventMessage);
            } else if (eventMessage.getTags().equals("lkl_status")) {
                handlerLklStatus(eventMessage);
            } else if (eventMessage.getTags().equals("lkl_pay_notify")) {
                handlerPayData(eventMessage);
            } else if (eventMessage.getTags().equals("kskl")) {
                handlerKsklData(eventMessage);
            } else if (eventMessage.getTags().equals("kskl_status")) {//处理同普通客流设备
                handlerKLStatus(eventMessage);
            } else if (eventMessage.getTags().equals("kskl_setting")) {
                handlerKsSetting(eventMessage);
            } else if (eventMessage.getTags().equals("kskl_pic")) {
                handlerKsPic(eventMessage);
            } else if (eventMessage.getTags().equals("ymdb_switch_result")) {
                handlerYmdbSwitchResult(eventMessage);
            } else if (eventMessage.getTags().equals("ymdb_status")) {
                handlerYMDBStatus(eventMessage);
            } else if (eventMessage.getTags().equals("ymsb_data")) {
                handlerYMSBDate(eventMessage);
            } else if (eventMessage.getTags().equals("ymsb_mqtt_data")) {
                handlerYMSBMQTTData(eventMessage);
            } else if (eventMessage.getTags().equals("ymsb_status")) {
                handlerYMSBStatus(eventMessage);
            } else if (eventMessage.getTags().equals("ymsb_mqtt_status")) {
                handlerYMSBStatus(eventMessage);
            }
        } catch (Exception e) {
            log.error("mq报文处理异常:【{}】,【{}】", eventMessage, e.getMessage(), e);
        }
    }

    private void handlerYMSBStatus(EventMessage eventMessage) {
        SbStatus sbStatus = JSON.parseObject(eventMessage.getMessageBody(), SbStatus.class);
        /*
            {
                "deviceSn": "xx", //设备id
                "status": 1, //状态, 1在线 2离线
                "date": 1719284179776 //时间戳
            }
         */
        log.info("sb_status data: {}", sbStatus);
        if (sbStatus == null) {
            log.warn("水表在线状态:{} 在线报文错误", eventMessage.getMessageBody());
            return;
        }
        if (sbStatus.getStatus() != 1 && sbStatus.getStatus() != 2) {
            log.warn("水表在线状态:{} 在线报文在线状态错误", sbStatus.getStatus());
            return;
        }
        WaterMeterListDto dto = new WaterMeterListDto();
        dto.setDeviceSn(sbStatus.getDeviceSn());
        List<WaterMeterListVo> list = waterMeterProvider.list(dto);
        if (list.isEmpty() || list.size() > 1) {
            log.warn("水表设备sn:{} 找不到对应设备或设备数大于1", sbStatus.getDeviceSn());
            return;
        }
        WaterMeterListVo vo = list.get(0);
        WaterMeterEditDto waterMeterEditDto = new WaterMeterEditDto();
        waterMeterEditDto.setId(vo.getId());
        waterMeterEditDto.setStatus(sbStatus.getStatus());
        waterMeterProvider.updateInfo(waterMeterEditDto);
    }


    /**
     * 推送间隔: 1h
     * @param eventMessage
     */
    private void handlerYMSBMQTTData(EventMessage eventMessage) {
        MQTTMeter mqttMeter = JSON.parseObject(eventMessage.getMessageBody(), MQTTMeter.class);
        log.info("MQTT水表数据：{}", mqttMeter);
        if (mqttMeter == null) {
            log.warn("MQTT水表数据推送报文错误:{}", eventMessage.getMessageBody());
            return;
        }
        List<MQTTData> mqttDataList = mqttMeter.getData();
        if (CollectionUtils.isEmpty(mqttDataList)) {
            log.warn("MQTT水表数据推送为空");
            return;
        }
        LocalDateTime now = LocalDateTime.now();
        for (MQTTData mqttData : mqttDataList) {
            String deviceId = mqttData.getDeviceId();
            double currentValue = mqttData.getValue();
            String datetimeStr = mqttData.getDatetime();
            LocalDateTime localDateTime = LocalDateTime.parse(datetimeStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDate localDate = localDateTime.toLocalDate();
            int hour = localDateTime.getHour();
            WaterMeterDataListDto waterMeterDataListDto = new WaterMeterDataListDto();
            waterMeterDataListDto.setDeviceSn(deviceId);
            waterMeterDataListDto.setReadDate(localDate);
            waterMeterDataListDto.setHours(hour);
            WaterMeterDataGetVo waterMeterDataGetVo = waterMeterDataProvider.get(waterMeterDataListDto);
            // 存在则更新，不存在则新增
            if (Objects.nonNull(waterMeterDataGetVo)) {
                if (localDateTime.isAfter(waterMeterDataGetVo.getReadDateTime()))
                    continue;
                WaterMeterDataEditDto waterMeterDataEditDto = BeanUtils.copyProperties(waterMeterDataGetVo, WaterMeterDataEditDto.class);
                waterMeterDataEditDto.setCurrentValue(currentValue);
                waterMeterDataEditDto.setReadDateTime(localDateTime);
                waterMeterDataEditDto.setUpdateTime(now);
                waterMeterDataProvider.edit(waterMeterDataEditDto);
            } else {
                WaterMeterDataAddDto waterMeterDataAddDto = new WaterMeterDataAddDto();
                waterMeterDataAddDto.setDeviceSn(deviceId);
                waterMeterDataAddDto.setReadDateTime(localDateTime);
                waterMeterDataAddDto.setReadDate(localDate);
                waterMeterDataAddDto.setHours(hour);
                waterMeterDataAddDto.setCurrentValue(currentValue);
                waterMeterDataAddDto.setCreateTime(now);
                waterMeterDataProvider.add(waterMeterDataAddDto);
            }
            // 更新water_meter表累积用水量和昨日用水量
            WaterMeterListDto waterMeterListDto = new WaterMeterListDto();
            waterMeterListDto.setDeviceSn(deviceId);
            WaterMeterGetVo waterMeterGetVo = waterMeterProvider.get(waterMeterListDto);
            if (Objects.nonNull(waterMeterGetVo)) {
                double yesterdayValue = 0;
                WaterMeterDataGetVo todayVo = waterMeterDataProvider.getByReadDate(deviceId, localDate, "eq");
                WaterMeterDataGetVo yesterdayVo = waterMeterDataProvider.getByReadDate(deviceId, localDate, "lt");
                if (Objects.nonNull(todayVo) && Objects.nonNull(yesterdayVo))
                    yesterdayValue = todayVo.getCurrentValue() - yesterdayVo.getCurrentValue();

                WaterMeterEditDto waterMeterEditDto = BeanUtils.copyProperties(waterMeterGetVo, WaterMeterEditDto.class);
                waterMeterEditDto.setCurrentValue(currentValue);
                waterMeterEditDto.setYesterdayValue(yesterdayValue);
                waterMeterEditDto.setUpdateTime(now);
                waterMeterProvider.updateInfo(waterMeterEditDto);
            }
        }
    }

    /**
     * 推送间隔: 24h
     * @param eventMessage
     */
    private void handlerYMSBDate(EventMessage eventMessage) {
        NBIotMeter nbIotMeter = JSON.parseObject(eventMessage.getMessageBody(), NBIotMeter.class);
        log.info("NB-IOT水表数据: {}", nbIotMeter);
        if (nbIotMeter == null) {
            log.warn("NB-IOT水表数据推送报文错误:{}", eventMessage.getMessageBody());
            return;
        }
        List<NBIotData> NBIotDataList = nbIotMeter.getData();
        if (CollectionUtils.isEmpty(NBIotDataList)) {
            log.warn("NB-IOT水表数据推送为空");
            return;
        }

        LocalDateTime now = LocalDateTime.now();
        for (NBIotData NBIotData : NBIotDataList) {
            String deviceId = NBIotData.getDeviceId();
            Last_1_day_hour last1DayHour = NBIotData.getLast_1_day_hour();
            if (Objects.isNull(last1DayHour) || CollectionUtils.isEmpty(last1DayHour.getItems()))
                continue;
            String dateStr = last1DayHour.getTime().substring(0, 10);
            LocalDate localDate = LocalDate.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd"));
            for (Items item : last1DayHour.getItems()) {
                int hours = item.getTime();
                double currentValue = item.getValue();
                LocalDateTime localDateTime = LocalDateTime.of(localDate.getYear(), localDate.getMonth(), localDate.getDayOfMonth(), hours, 0, 0);

                WaterMeterDataListDto waterMeterDataListDto = new WaterMeterDataListDto();
                waterMeterDataListDto.setDeviceSn(deviceId);
                waterMeterDataListDto.setReadDate(localDate);
                waterMeterDataListDto.setHours(hours);
                WaterMeterDataGetVo waterMeterDataGetVo = waterMeterDataProvider.get(waterMeterDataListDto);
                // 存在则更新，不存在则新增
                if (Objects.nonNull(waterMeterDataGetVo)) {
                    WaterMeterDataEditDto waterMeterDataEditDto = BeanUtils.copyProperties(waterMeterDataGetVo, WaterMeterDataEditDto.class);
                    waterMeterDataEditDto.setCurrentValue(currentValue);
                    waterMeterDataEditDto.setUpdateTime(now);
                    waterMeterDataProvider.edit(waterMeterDataEditDto);
                } else {
                    WaterMeterDataAddDto dataAddDto = new WaterMeterDataAddDto();
                    dataAddDto.setDeviceSn(deviceId);
                    dataAddDto.setReadDateTime(localDateTime);
                    dataAddDto.setReadDate(localDate);
                    dataAddDto.setHours(hours);
                    dataAddDto.setCurrentValue(currentValue);
                    dataAddDto.setCreateTime(now);
                    waterMeterDataProvider.add(dataAddDto);
                }
            }
            // 当前用水量
            double currentValue = NBIotData.getShuiliang();
            // 昨日用水量
            List<Last_12_days> last12Days = NBIotData.getLast_12_days();
            double yesterdayValue = last12Days.get(0).getValue() - last12Days.get(1).getValue();
            WaterMeterListDto waterMeterListDto = new WaterMeterListDto();
            waterMeterListDto.setDeviceSn(deviceId);
            WaterMeterGetVo waterMeterGetVo = waterMeterProvider.get(waterMeterListDto);
            // 更新water_meter表累积用水量和昨日用水量
            if (Objects.nonNull(waterMeterGetVo)) {
                WaterMeterEditDto waterMeterEditDto = BeanUtils.copyProperties(waterMeterGetVo, WaterMeterEditDto.class);
                waterMeterEditDto.setCurrentValue(currentValue);
                waterMeterEditDto.setYesterdayValue(yesterdayValue);
                waterMeterEditDto.setUpdateUser("更新水数据");
                waterMeterEditDto.setUpdateTime(now);
                waterMeterProvider.updateInfo(waterMeterEditDto);
            }
        }
    }

    private void handlerYmdbSwitchResult(EventMessage eventMessage) {
        YmdbSwitchResult result = JSON.parseObject(eventMessage.getMessageBody(), YmdbSwitchResult.class);
        String deviceId = result.getDeviceId();
        RedisUtil.set(deviceId + ":status", result.getSwitchStatus(), 600);
    }

    private void handlerKsPic(EventMessage eventMessage) {
        KsklPic ksklPic = JSON.parseObject(eventMessage.getMessageBody(), KsklPic.class);
        if (StringUtils.isNotEmpty(ksklPic.getPicUrl())) {
            try {
                String picUrl = ksklPic.getPicUrl();
                URL url = new URL(picUrl);
                String fileName = picUrl.substring(picUrl.lastIndexOf("/") + 1);
                FileInfo fileInfo = fileService.putObject(url.openConnection().getInputStream(), fileName, 1l);

                //新增文件上传记录
                SysFileAddDto dto = new SysFileAddDto();
                dto.setFileName(fileInfo.getOriginalFilename());
                dto.setFilePath(fileInfo.getObjectName());
                dto.setFileSize(fileInfo.getFileSize());
                dto.setMimeType(fileInfo.getContentType());
                dto.setSuffix(fileInfo.getSuffix());
                dto.setBuckets(fileInfo.getBucketName());
                dto.setStorageType(fileInfo.getStorageType());
                //业务类别
                dto.setBusType(4020 / 100);
                dto.setBusSubType(4020);
                SysFileAddVo addVo = sysFileProvider.add(dto);
                //修改设备表中的图纸ID
                iotDeviceProvider.updateDrawingById(ksklPic.getDeviceId(),addVo.getId());
                //上传后的图片先缓存在redis中。
                RedisUtil.set(ksklPic.getDeviceId(), String.valueOf(addVo.getId()), 300);
            } catch (Exception e) {
                log.error("旷世客流抓图解析失败",e);
            }
        }
    }

    private void handlerKsSetting(EventMessage eventMessage) {
        KsklSetting ksklSetting = JSON.parseObject(eventMessage.getMessageBody(), KsklSetting.class);
        List<AreaList> areaLists = ksklSetting.getConfigData();
        RedisUtil.set(KSKL_DEVICE.getIotDeviceType() + ksklSetting.getDeviceId(), JSONArray.toJSONString(areaLists), 600);
        // 更新配置信息
        if(!CollectionUtils.isEmpty(areaLists)){
            iotDevicePointProvider.updateKsPoint(ksklSetting.getDeviceId(),areaLists);
        }
    }

    private void handlerLklStatus(EventMessage eventMessage) {
        XpStatus xpStatus = JSON.parseObject(eventMessage.getMessageBody(), XpStatus.class);
        log.info("lkl_status data: {}", xpStatus);
        if (xpStatus == null) {
            log.warn("在线状态:{} 在线报文错误", eventMessage.getMessageBody());
            return;
        }
        if (xpStatus.getStatus() != 1 && xpStatus.getStatus() != 2) {
            log.warn("在线状态:{} 在线报文在线状态错误", xpStatus.getStatus());
            return;
        }
        IotDeviceListDto dto = new IotDeviceListDto();
        dto.setDeviceId(xpStatus.getDeviceSn());
        List<IotDeviceListVo> list = iotDeviceProvider.list(dto);
        if (list.isEmpty() || list.size() > 1) {
            log.warn("设备sn:{} 找不到对应设备或设备数大于1", xpStatus.getDeviceSn());
            return;
        }
        IotDeviceListVo vo = list.get(0);
        IotDeviceEditDto iotDeviceEditDto = new IotDeviceEditDto();
        iotDeviceEditDto.setId(vo.getId());

        iotDeviceEditDto.setDeviceStatus(xpStatus.getStatus() == 1 ? 0 : 1);
        iotDeviceProvider.edit(iotDeviceEditDto);
    }

    private void handlerFireStatus(EventMessage eventMessage) {
        XpStatus xpStatus = JSON.parseObject(eventMessage.getMessageBody(), XpStatus.class);
        log.info("kl_status data: {}", xpStatus);
        if (xpStatus == null) {
            log.warn("在线状态:{} 在线报文错误", eventMessage.getMessageBody());
            return;
        }
        if (xpStatus.getStatus() != 1 && xpStatus.getStatus() != 2) {
            log.warn("在线状态:{} 在线报文在线状态错误", xpStatus.getStatus());
            return;
        }
        IotDeviceListDto dto = new IotDeviceListDto();
        dto.setDeviceId(xpStatus.getDeviceSn());
        List<IotDeviceListVo> list = iotDeviceProvider.list(dto);
        if (list.isEmpty() || list.size() > 1) {
            log.warn("设备sn:{} 找不到对应设备或设备数大于1", xpStatus.getDeviceSn());
            return;
        }
        IotDeviceListVo vo = list.get(0);
        IotDeviceEditDto iotDeviceEditDto = new IotDeviceEditDto();
        iotDeviceEditDto.setId(vo.getId());

        //报警状态，不变更为在线
        if (vo.getDeviceStatus() != null && vo.getDeviceStatus() == 2 && xpStatus.getStatus() != null && xpStatus.getStatus() == 1) {
            return;
        }

        iotDeviceEditDto.setDeviceStatus(xpStatus.getStatus() == 1 ? 0 : 1);
        iotDeviceProvider.edit(iotDeviceEditDto);
    }

    private void handlerKLStatus(EventMessage eventMessage) {
        XpStatus xpStatus = JSON.parseObject(eventMessage.getMessageBody(), XpStatus.class);
        log.info("{} data: {}",eventMessage.getTags(), xpStatus);
        synchronized (xpStatus.getDeviceSn().intern()) {
            if (xpStatus == null) {
                log.warn("在线状态:{} 在线报文错误", eventMessage.getMessageBody());
                return;
            }
            if (xpStatus.getStatus() != 1 && xpStatus.getStatus() != 2) {
                log.warn("在线状态:{} 在线报文在线状态错误", xpStatus.getStatus());
                return;
            }
            IotDeviceListDto dto = new IotDeviceListDto();
            dto.setDeviceId(xpStatus.getDeviceSn());
            List<IotDeviceListVo> list = iotDeviceProvider.list(dto);
            if (list.isEmpty() || list.size() > 1) {
                log.warn("设备sn:{} 找不到对应设备或设备数大于1", xpStatus.getDeviceSn());
                return;
            }
            IotDeviceListVo vo = list.get(0);
            IotDeviceEditDto iotDeviceEditDto = new IotDeviceEditDto();
            iotDeviceEditDto.setId(vo.getId());
            iotDeviceEditDto.setDeviceStatus(xpStatus.getStatus() == 1 ? 0 : 1);
            iotDeviceProvider.edit(iotDeviceEditDto);

            //新增同步新增在离线记录
            IotDeviceOnlineAddDto addDto = BeanUtils.copyProperties(vo, IotDeviceOnlineAddDto.class);
            addDto.setDeviceStatus(iotDeviceEditDto.getDeviceStatus());
            addDto.setSyncTime(xpStatus.getDate() == null ? LocalDateTime.now() : xpStatus.getDate().toInstant()
                    .atZone(ZoneId.systemDefault()) // 指定时区（如系统默认）
                    .toLocalDateTime());
            iotDeviceOnlineProvider.add(addDto);
        }
    }

    private void handlerJyklSetting(EventMessage eventMessage) {
        JyklSetting jyklSetting = JSON.parseObject(eventMessage.getMessageBody(), JyklSetting.class);
        List<IotDevicePointAddDto> areaLists = jyklSetting.getConfigData();
        RedisUtil.set(JYKL_DEVICE.getIotDeviceType() + jyklSetting.getDeviceId(), JSONArray.toJSONString(areaLists), 600);
        // 更新配置信息
        if(!CollectionUtils.isEmpty(areaLists)){
            iotDevicePointProvider.updateJyklPoint(jyklSetting.getDeviceId(),areaLists);
        }
    }

    /**
     * 更新ems电表状态
     * @param eventMessage
     */
    private void handlerYMDBStatus(EventMessage eventMessage) {
        XpStatus xpStatus = JSON.parseObject(eventMessage.getMessageBody(), XpStatus.class);
        /*
            {
                "deviceSn": "dfsafgerrteertererer", //设备id
                "status": 1, //状态, 1在线 2离线
                "date": 1719284179776 //时间戳
             }
         */
        log.info("xp_status data: {}", xpStatus);
        if (xpStatus == null) {
            log.warn("ems电表在线状态:{} 在线报文错误", eventMessage.getMessageBody());
            return;
        }
        if (xpStatus.getStatus() != 1 && xpStatus.getStatus() != 2) {
            log.warn("ems电表在线状态:{} 在线报文在线状态错误", xpStatus.getStatus());
            return;
        }
        ElectricityListDto dto = new ElectricityListDto();
        dto.setDeviceSn(xpStatus.getDeviceSn());
        List<ElectricityListVo> list = electricityProvider.list(dto);
        if (list.isEmpty() || list.size() > 1) {
            log.warn("ems电表设备sn:{} 找不到对应设备或设备数大于1", xpStatus.getDeviceSn());
            return;
        }
        ElectricityListVo vo = list.get(0);
        ElectricityEditDto electricityEditDto = new ElectricityEditDto();
        electricityEditDto.setId(vo.getId());
        electricityEditDto.setStatus(xpStatus.getStatus());
        electricityProvider.editStatus(electricityEditDto);
    }

    /**
     * 更新设备在线状态
     */
    private void handlerXPStatus(EventMessage eventMessage) {
        XpStatus xpStatus = JSON.parseObject(eventMessage.getMessageBody(), XpStatus.class);
        log.info("xp_status data: {}", xpStatus);
        if (xpStatus == null) {
            log.warn("在线状态:{} 在线报文错误", eventMessage.getMessageBody());
            return;
        }
        if (xpStatus.getStatus() != 1 && xpStatus.getStatus() != 2) {
            log.warn("在线状态:{} 在线报文在线状态错误", xpStatus.getStatus());
            return;
        }
        IotDeviceListDto dto = new IotDeviceListDto();
        dto.setDeviceId(xpStatus.getDeviceSn());
        List<IotDeviceListVo> list = iotDeviceProvider.list(dto);
        if (list.isEmpty() || list.size() > 1) {
            log.warn("设备sn:{} 找不到对应设备或设备数大于1", xpStatus.getDeviceSn());
            return;
        }
        IotDeviceListVo vo = list.get(0);
        IotDeviceEditDto iotDeviceEditDto = new IotDeviceEditDto();
        iotDeviceEditDto.setId(vo.getId());
        iotDeviceEditDto.setDeviceStatus(xpStatus.getStatus() == 1 ? 0 : 1);
        iotDeviceProvider.edit(iotDeviceEditDto);

        //新增同步新增在离线记录
        IotDeviceOnlineAddDto addDto = BeanUtils.copyProperties(vo, IotDeviceOnlineAddDto.class);
        addDto.setDeviceStatus(iotDeviceEditDto.getDeviceStatus());
        addDto.setSyncTime(xpStatus.getDate() == null ? LocalDateTime.now() : xpStatus.getDate().toInstant()
                .atZone(ZoneId.systemDefault()) // 指定时区（如系统默认）
                .toLocalDateTime());
        iotDeviceOnlineProvider.add(addDto);
    }

    /**
     * 处理销售数据
     *
     * @param eventMessage
     */
    private void handlerXPData(EventMessage eventMessage) {
        XP_LOG.info("小票原始报文: [{}]-[{}]", eventMessage.getMessageId(), eventMessage.getMessageBody());
        XpDataItem xpDataItem = JSON.parseObject(eventMessage.getMessageBody(), XpDataItem.class);
        IotSaleFailMsgAddDto failMsgAddDto = new IotSaleFailMsgAddDto();
        failMsgAddDto.setMessageId(eventMessage.getMessageId());
        failMsgAddDto.setMessage(eventMessage.getMessageBody());
        failMsgAddDto.setDeviceId(xpDataItem.getDeviceId());
        try {
            log.info("xp data: {}", xpDataItem);
            List<DateItem> dateItems = xpDataItem.getData();
            String data = dateItems.get(0).getText();
            if (StringUtils.isEmpty(data)) {
                data = StringUtils.EMPTY;
            }
            String picData = StringUtils.EMPTY;

            //只有图片的小票单
            if (StringUtils.isNotEmpty(dateItems.get(0).getPic())) {
                JSONArray array = JSONArray.parseArray(dateItems.get(0).getPic());
                if (array.size() > 0) {
                    String url = array.getString(0);
                    String content = ocrProvider.orcRec(url);
                    XP_LOG.info("小票图片OCR: [{}]-[{}]", eventMessage.getMessageId(), content);
                    failMsgAddDto.setContent(content);
                    if (StringUtils.isNotEmpty(content)) {
                        picData = content;
                    }
                }
            }

            //过滤总单记录“日结”和“交班”两个词
            if ((StringUtils.isEmpty(data) && StringUtils.isEmpty(picData)) || data.contains("日结") || data.contains("交班") || picData.contains("日结") || picData.contains("交班")) {
                return;
            }

            IotSaleBindListDto iotSaleBindListDto = new IotSaleBindListDto();
            iotSaleBindListDto.setDeviceId(xpDataItem.getDeviceId());
            IotSaleBindGetVo iotSaleBindGetVo = iotSaleBindProvider.get(iotSaleBindListDto);
            if (iotSaleBindGetVo == null) {
                log.warn("设备sn:{} 未找到销售绑定信息", xpDataItem.getDeviceId());
                return;
            }
            IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
            iotDeviceListDto.setDeviceId(xpDataItem.getDeviceId());
            IotDeviceGetVo iotDeviceGetVo = iotDeviceProvider.get(iotDeviceListDto);
            if (iotDeviceGetVo == null) {
                log.warn("设备sn:{} 未找到设备信息", xpDataItem.getDeviceId());
                return;
            }
            failMsgAddDto.setEntId(iotDeviceGetVo.getEntId());
            failMsgAddDto.setTenantId(iotDeviceGetVo.getTenantId());
            failMsgAddDto.setTenantName(iotDeviceGetVo.getTenantName());

            RoomsListDto roomsListDto = new RoomsListDto();
            roomsListDto.setTenantId(iotSaleBindGetVo.getTenantId());
            roomsListDto.setFcode(iotSaleBindGetVo.getObjCode());
            RoomsGetVo roomsGetVo = roomProvider.get(roomsListDto);
            if (roomsGetVo == null) {
                log.warn("空间编码:{} 未找到门店数据", iotSaleBindGetVo.getObjCode());
                return;
            }
            failMsgAddDto.setShopNo(roomsGetVo.getName());

            IotSaleTemplateGetVo templateGetVo = iotSaleTemplateProvider.get(iotDeviceGetVo.getSaleTemplateId());
            if (templateGetVo == null) {
                log.warn("模版id:{}  设备sn：{}  未找到销售模板信息", iotDeviceGetVo.getSaleTemplateId(), iotDeviceGetVo.getDeviceId());
                return;
            }

            log.info("解析前数据，data = {}, templateGetVo.getSaleAmount() = {}, templateGetVo.getSaleNo() = {}, templateGetVo.getSaleTime() = {}", data, templateGetVo.getSaleAmount(), templateGetVo.getSaleNo(), templateGetVo.getSaleTime());
            ParseData parseData = parseData(data, templateGetVo.getSaleAmount(), templateGetVo.getSaleNo(), templateGetVo.getSaleTime(), templateGetVo.getTakeOut(), templateGetVo.getRetrieveAmount());
            log.info("解析后数据1，parseData: {}, {}, {}", parseData.getSaleNo(), parseData.getSaleTime(), parseData.getSaleAmount());
            if (Objects.isNull(parseData.getSaleNo()) || Objects.isNull(parseData.getSaleTime())) {
                parseData = parseData(picData, templateGetVo.getSaleAmount(), templateGetVo.getSaleNo(), templateGetVo.getSaleTime(), templateGetVo.getTakeOut(), templateGetVo.getRetrieveAmount());
                log.info("解析后数据2，parseData: {}, {}, {}", parseData.getSaleNo(), parseData.getSaleTime(), parseData.getSaleAmount());
            }
            XP_LOG.info("小票解析结果: [{}]-[{},{},{}]", eventMessage.getMessageId(), parseData.getSaleNo(), parseData.getSaleTime(), parseData.getSaleAmount());
            IotSaleDataAddDto iotSaleDataAddDto = BeanUtils.copyProperties(iotDeviceGetVo, IotSaleDataAddDto.class);
            iotSaleDataAddDto.setShopNo(roomsGetVo.getName());
            iotSaleDataAddDto.setBrandName(roomsGetVo.getBrandName());
            iotSaleDataAddDto.setObjCode(iotSaleBindGetVo.getObjCode());
            iotSaleDataAddDto.setUseObj(iotSaleBindGetVo.getUseObj());

            iotSaleDataAddDto.setOrderType(parseData.getTakeOut() ? 1 : 0);
            iotSaleDataAddDto.setOrderNo(parseData.getSaleNo());
            iotSaleDataAddDto.setOrderAmount(BigDecimal.valueOf(Double.parseDouble(parseData.getSaleAmount())));
            //如果打单没有时间，以收到报文时间为补充
            if (StringUtils.isNotEmpty(parseData.getSaleTime())) {
                iotSaleDataAddDto.setOrderTime(parseDateTime(parseData.getSaleTime()));
            }
            if (Objects.isNull(iotSaleDataAddDto.getOrderTime())) {
                iotSaleDataAddDto.setOrderTime(LocalDateTime.now());
            }
            IotSaleDataAddVo add = iotSaleDataProvider.add(iotSaleDataAddDto);
            log.info("iot表插入成功，{}", add.getId());

            //累计存销售主数据库
            IotSaleDataGetVo iotSaleDataGetVo = iotSaleDataProvider.get(add.getId());
            ReportMasterSaleDataAddDto masterSaleDataAddDto = BeanUtils.copyProperties(iotSaleDataGetVo, ReportMasterSaleDataAddDto.class);
            masterSaleDataAddDto.setSaleTime(iotSaleDataAddDto.getOrderTime().toLocalDate());
            masterSaleDataAddDto.setBrandName(iotSaleDataGetVo.getBrandName());
            masterSaleDataAddDto.setStoreId(roomsGetVo.getStoreId());
            // TODO 现在推的都是门店
            masterSaleDataAddDto.setDataSources(0);
            masterSaleDataAddDto.setDataStatus(0);
            BigDecimal saleAmount = new BigDecimal(parseData.getSaleAmount());
            masterSaleDataAddDto.setTotalAmount(saleAmount);
            masterSaleDataAddDto.setBeforeTotalAmount(saleAmount);
            masterSaleDataAddDto.setTotalOrder(parseData.getOrder());
            masterSaleDataAddDto.setBeforeTotalOrder(parseData.getOrder());
            //外卖
            if (parseData.getTakeOut()) {
                masterSaleDataAddDto.setTakeawayAmount(saleAmount);
                masterSaleDataAddDto.setBeforeTakeawayAmount(saleAmount);
                masterSaleDataAddDto.setTakeawayOrder(parseData.getOrder());
                masterSaleDataAddDto.setBeforeTakeawayOrder(parseData.getOrder());

                masterSaleDataAddDto.setStoreAmount(BigDecimal.valueOf(0));
                masterSaleDataAddDto.setBeforeStoreAmount(BigDecimal.valueOf(0));
                masterSaleDataAddDto.setStoreOrder(0);
                masterSaleDataAddDto.setBeforeStoreOrder(0);
            }
            //门店
            else {
                masterSaleDataAddDto.setStoreAmount(saleAmount);
                masterSaleDataAddDto.setBeforeStoreAmount(saleAmount);
                masterSaleDataAddDto.setStoreOrder(parseData.getOrder());
                masterSaleDataAddDto.setBeforeStoreOrder(parseData.getOrder());

                masterSaleDataAddDto.setTakeawayAmount(BigDecimal.valueOf(0));
                masterSaleDataAddDto.setBeforeTakeawayAmount(BigDecimal.valueOf(0));
                masterSaleDataAddDto.setTakeawayOrder(0);
                masterSaleDataAddDto.setBeforeTakeawayOrder(0);
            }
            // 补充业态和一级品类字段
            if (Objects.nonNull(roomsGetVo.getBrandId())) {
                CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
                commerceContractListDto.setTenantId(roomsGetVo.getTenantId());
                commerceContractListDto.setRoomId(roomsGetVo.getId());
                commerceContractListDto.setBrandId(roomsGetVo.getBrandId());
                CommerceBrandGetVo commerceBrandGetVo = commerceBrandProvider.get(roomsGetVo.getBrandId());
                CommerceContractGetVo commerceContractGetVo = commerceContractProvider.get(commerceContractListDto);
                if (Objects.nonNull(commerceContractGetVo)) {
                    masterSaleDataAddDto.setCommercialTypeCode(commerceContractGetVo.getCommercialTypeCode());
                    masterSaleDataAddDto.setCommercialTypeName(commerceContractGetVo.getCommercialTypeName());
                    masterSaleDataAddDto.setCategoryId(commerceContractGetVo.getCategoryId());
                    masterSaleDataAddDto.setCategoryName(commerceContractGetVo.getCategoryName());
                    if(commerceBrandGetVo!=null){
                        masterSaleDataAddDto.setCommercialTwoId(commerceBrandGetVo.getCommercialTwoId());
                        masterSaleDataAddDto.setCommercialTwo(commerceBrandGetVo.getCommercialTwo());
                    }
                }
            }
            EntProjectGetVo entProjectGetVo = entProjectProvider.get(roomsGetVo.getTenantId());
            if(entProjectGetVo!=null){
                masterSaleDataAddDto.setCity(entProjectGetVo.getFcity());
            }
            reportMasterSaleDataProvider.accSaleData(masterSaleDataAddDto);
            log.info("report表插入成功，{}", add.getId());
        } catch (Exception e) {
            if (StringUtils.isNotEmpty(e.getMessage()) && e.getMessage().contains("DuplicateKeyException")) {
                return;
            }
            log.error("小票解析异常：【{}】,【{}】", eventMessage, e.getMessage(), e);
            iotSaleFailMsgProvider.add(failMsgAddDto);
        }
    }

    /**
     * 处理拉卡拉支付信息
     *
     * @param eventMessage
     */
    private void handlerPayData(EventMessage eventMessage) {
        PayDataItem dataItem = JSON.parseObject(eventMessage.getMessageBody(), PayDataItem.class);
        try {
            log.info("pay data: {}", dataItem);
            List<PayDetail> dateItems = dataItem.getData();
            PayDetail detail = dateItems.get(0);

            //匹配绑定关系
            IotSaleBindListDto iotSaleBindListDto = new IotSaleBindListDto();
            iotSaleBindListDto.setDeviceId(dataItem.getDeviceId());
            IotSaleBindGetVo iotSaleBindGetVo = iotSaleBindProvider.get(iotSaleBindListDto);
            if (iotSaleBindGetVo == null) {
                log.warn("设备sn:{} 未找到销售绑定信息", dataItem.getDeviceId());
                return;
            }
            //匹配设备信息
            IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
            iotDeviceListDto.setDeviceId(dataItem.getDeviceId());
            IotDeviceGetVo iotDeviceGetVo = iotDeviceProvider.get(iotDeviceListDto);
            if (iotDeviceGetVo == null) {
                log.warn("设备sn:{} 未找到设备信息", dataItem.getDeviceId());
                return;
            }

            //匹配店铺信息
            RoomsListDto roomsListDto = new RoomsListDto();
            roomsListDto.setTenantId(iotSaleBindGetVo.getTenantId());
            roomsListDto.setFcode(iotSaleBindGetVo.getObjCode());
            RoomsGetVo roomsGetVo = roomProvider.get(roomsListDto);
            if (roomsGetVo == null) {
                log.warn("空间编码:{} 未找到门店数据", iotSaleBindGetVo.getObjCode());
                return;
            }

            Integer orderCount = 1;
            BigDecimal orderAmount = BigDecimal.valueOf(Integer.valueOf(detail.getAmount())).divide(BigDecimal.valueOf(100));
            //判断退单
            if (StringUtils.isNotEmpty(detail.getExtTransCode())) {
                switch (detail.getExtTransCode()) {
                    case "012002"://消费撤销
                    case "012018"://预授权完成撤销
                    case "012102"://积分消费撤销
                    case "012802"://分期付消费撤销
                    case "012003"://退货交易
                        orderCount = -1;
                        orderAmount = orderAmount.negate();
                }
            }

            IotSaleDataAddDto iotSaleDataAddDto = BeanUtils.copyProperties(iotDeviceGetVo, IotSaleDataAddDto.class);
            iotSaleDataAddDto.setShopNo(roomsGetVo.getName());
            iotSaleDataAddDto.setBrandName(roomsGetVo.getBrandName());
            iotSaleDataAddDto.setObjCode(iotSaleBindGetVo.getObjCode());
            iotSaleDataAddDto.setUseObj(iotSaleBindGetVo.getUseObj());

            iotSaleDataAddDto.setOrderType(0);
            iotSaleDataAddDto.setOrderNo(detail.getOrderNo());
            //报文订单金额以分为单位
            iotSaleDataAddDto.setOrderAmount(orderAmount);
            iotSaleDataAddDto.setOrderTime(parseDateTime(detail.getTransTime()));
            IotSaleDataAddVo add = iotSaleDataProvider.add(iotSaleDataAddDto);
            log.info("iot表插入成功，{}", add.getId());

            //累计存销售主数据库
            IotSaleDataGetVo iotSaleDataGetVo = iotSaleDataProvider.get(add.getId());
            ReportMasterSaleDataAddDto masterSaleDataAddDto = BeanUtils.copyProperties(iotSaleDataGetVo, ReportMasterSaleDataAddDto.class);
            masterSaleDataAddDto.setSaleTime(iotSaleDataAddDto.getOrderTime().toLocalDate());
            masterSaleDataAddDto.setBrandName(iotSaleDataGetVo.getBrandName());
            masterSaleDataAddDto.setStoreId(roomsGetVo.getStoreId());
            // TODO 现在推的都是门店
            masterSaleDataAddDto.setDataSources(0);
            masterSaleDataAddDto.setDataStatus(0);
            //总计
            masterSaleDataAddDto.setTotalAmount(orderAmount);
            masterSaleDataAddDto.setBeforeTotalAmount(orderAmount);
            masterSaleDataAddDto.setTotalOrder(orderCount);
            masterSaleDataAddDto.setBeforeTotalOrder(orderCount);
            //门店总计
            masterSaleDataAddDto.setStoreAmount(orderAmount);
            masterSaleDataAddDto.setBeforeStoreAmount(orderAmount);
            masterSaleDataAddDto.setStoreOrder(orderCount);
            masterSaleDataAddDto.setBeforeStoreOrder(orderCount);
            //外卖总计
            masterSaleDataAddDto.setTakeawayAmount(BigDecimal.valueOf(0));
            masterSaleDataAddDto.setBeforeTakeawayAmount(BigDecimal.valueOf(0));
            masterSaleDataAddDto.setTakeawayOrder(0);
            masterSaleDataAddDto.setBeforeTakeawayOrder(0);

            // 补充业态和一级品类字段
            if (Objects.nonNull(roomsGetVo.getBrandId())) {
                CommerceContractListDto commerceContractListDto = new CommerceContractListDto();
                commerceContractListDto.setBrandId(roomsGetVo.getBrandId());
                CommerceContractGetVo commerceContractGetVo = commerceContractProvider.get(commerceContractListDto);
                if (Objects.nonNull(commerceContractGetVo)) {
                    masterSaleDataAddDto.setCommercialTypeCode(commerceContractGetVo.getCommercialTypeCode());
                    masterSaleDataAddDto.setCommercialTypeName(commerceContractGetVo.getCommercialTypeName());
                    masterSaleDataAddDto.setCategoryId(commerceContractGetVo.getCategoryId());
                    masterSaleDataAddDto.setCategoryName(commerceContractGetVo.getCategoryName());
                }
            }
            reportMasterSaleDataProvider.accSaleData(masterSaleDataAddDto);
            log.info("report表插入成功，{}", add.getId());
        } catch (Exception e) {
            log.error("拉卡拉支付信息处理异常：{}", e.getMessage());
        }
    }

    private static final List<String> FULL_DATE_PATTERN = Arrays.asList(
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-ddHH:mm:ss",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/ddHH:mm:ss",
            "yyyy-MM-dd HH:mm",
            "yyyy-MM-ddHH:mm",
            "yyyy/MM/dd HH:mm",
            "yyyy/MM/ddHH:mm"
    );

    private LocalDateTime parseDateTime(String saleTime) {
        if (StringUtils.isNotEmpty(saleTime)) {
            for (String pattern : FULL_DATE_PATTERN) {
                try {
                    return LocalDateTime.parse(saleTime, DateTimeFormatter.ofPattern(pattern));
                } catch (Exception e) {
                }
            }
            String saleTime2 = LocalDate.now().getYear() + "-" + saleTime;
            for (String pattern : FULL_DATE_PATTERN) {
                try {
                    return LocalDateTime.parse(saleTime2, DateTimeFormatter.ofPattern(pattern));
                } catch (Exception e) {
                }
            }
            saleTime2 = LocalDate.now().getYear() + "/" + saleTime;
            for (String pattern : FULL_DATE_PATTERN) {
                try {
                    return LocalDateTime.parse(saleTime2, DateTimeFormatter.ofPattern(pattern));
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    private ParseData parseData(String data, String saleAmountRe, String saleNoRe, String saleTimeRe, String takeOutReg, String retrieveAmountReg) {
        ParseData parseData = new ParseData();
        String[] txtList = data.split("\\n");
        // 流水号
        //String saleNo = "(?s)(?<=消费流水：)(.*)";
        for (String item : txtList) {
            Pattern r = Pattern.compile(saleNoRe);
            Matcher m = r.matcher(item);
            if (m.find()) {
                parseData.setSaleNo(m.group("saleNo").trim());
                break;
            }
        }
        // 销售日期
        //String saleTime = "(?s)(?<=打印时间：)(.*)";
        for (String item : txtList) {
            Pattern r = Pattern.compile(saleTimeRe);
            Matcher m = r.matcher(item);
            if (m.find()) {
                parseData.setSaleTime(m.group("saleTime").trim());
                break;
            }
        }

        //退单处理匹配
        if (data.contains("退") || data.contains("作废")) {
            Pattern r = Pattern.compile(retrieveAmountReg);
            for (String item : txtList) {
                Matcher m = r.matcher(item);
                if (m.find()) {
                    parseData.setSaleAmount(m.group("saleAmount").trim());
                    break;
                }
            }
            parseData.setOrder(-1);
            if (StringUtils.isNotEmpty(parseData.getSaleAmount())) {
                parseData.setSaleAmount("-" + parseData.getSaleAmount());
            }

            //退单无单号时，设置为空
            if (StringUtils.isEmpty(parseData.getSaleNo())) {
                parseData.setSaleNo(StringUtils.EMPTY);
            }
        } else {
            // 销售金额
            //String saleAmount = "(?s)(?<=总计：)(.*)(?=支付方式)";
            for (String item : txtList) {
                Pattern r = Pattern.compile(saleAmountRe);
                Matcher m = r.matcher(item);
                if (m.find()) {
                    parseData.setSaleAmount(m.group("saleAmount").trim());
                    break;
                }
            }
        }

        //“外卖”、“配送”、“饿了么”
        if (StringUtils.isEmpty(takeOutReg)) {
            takeOutReg = "外卖|配送|饿了么";
        }
        Pattern r = Pattern.compile(takeOutReg);
        Matcher m = r.matcher(data);
        parseData.setTakeOut(m.find());
        return parseData;
    }


    /**
     * 处理客流数据   // TODO 待优化
     *
     * @param eventMessage
     */
    private void handlerKlData(EventMessage eventMessage) {
        List<IotFlowDataAddDto> dtos = new ArrayList<>();
        KlDataItem klDataItem = JSON.parseObject(eventMessage.getMessageBody(), KlDataItem.class);
        List<DataItem> data = klDataItem.getData();
        log.info("kl data: {}", klDataItem);

        IotFlowBindListDto iotFlowBindListDto = new IotFlowBindListDto();
        iotFlowBindListDto.setDeviceId(klDataItem.getDeviceId());
        List<IotFlowBindListVo> flowBindListVos = iotFlowBindProvider.list(iotFlowBindListDto);
        if (flowBindListVos == null || flowBindListVos.size() == 0) {
            log.warn("设备sn:{} 未找到客流绑定信息", klDataItem.getDeviceId());
            return;
        }
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceId(klDataItem.getDeviceId());
        IotDeviceGetVo deviceVo = iotDeviceProvider.get(iotDeviceListDto);
        if (deviceVo == null) {
            log.warn("设备sn:{} 未找到设备信息", klDataItem.getDeviceId());
            return;
        }

        for (IotFlowBindListVo bindVo : flowBindListVos) {
            IotFlowDataAddDto dto = new IotFlowDataAddDto();
            dto.setTenantId(deviceVo.getTenantId());
            dto.setDeviceId(deviceVo.getDeviceId());
            dto.setDeviceName(deviceVo.getDeviceName());
            dto.setDeviceCode(deviceVo.getDeviceCode());
            dto.setDeviceModel(deviceVo.getDeviceModel());
            dto.setDeviceName(deviceVo.getDeviceName());
            dto.setDeviceEnable(deviceVo.getDeviceEnable());
            dto.setUseObj(bindVo.getUseObj());
            dto.setObjCode(bindVo.getObjCode());
            dto.setUseType(bindVo.getUseType());
            dto.setCountDirection(bindVo.getCountDirection());

            dto.setCreateTime(LocalDateTime.now());

            if (data != null && data.size() > 0) {
                DataItem dataItem = data.get(0);
//                LocalDateTime endDateTime = ZonedDateTime.parse(dataItem.getEndTime()).toLocalDateTime();
//                ZonedDateTime zonedDateTime = endDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                dto.setCollectTime(getTime(dataItem.getEndTime()));
                dto.setEnterCount(dataItem.getInCnt());
                dto.setOutCount(dataItem.getOutCnt());
            }
            dtos.add(dto);
        }
        for (IotFlowDataAddDto dto : dtos) {
            iotFlowDataProvider.add(dto);
        }
    }

    private LocalDateTime getTime(String endTime){
        try{
            LocalDateTime endDateTime = ZonedDateTime.parse(endTime).toLocalDateTime();
            ZonedDateTime zonedDateTime = endDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
            return zonedDateTime.toLocalDateTime();
        }catch (Exception e){
            try{
                LocalDateTime endDateTime = LocalDateTime.parse(endTime);
                return endDateTime;
            }catch (Exception e1){

            }
        }
        return null;
    }

    /**
     * 旷世客流数据对接
     *
     * @param eventMessage
     */
    private void handlerKsklData(EventMessage eventMessage) {
        List<IotFlowDataAddDto> dtos = new ArrayList<>();
        KsklData klDataItem = JSON.parseObject(eventMessage.getMessageBody(), KsklData.class);
        Map<String, KsklData.KsklCountItem> data = klDataItem.getCountData();
        log.info("kl data: {}", klDataItem);

        IotFlowBindListDto iotFlowBindListDto = new IotFlowBindListDto();
        iotFlowBindListDto.setDeviceId(klDataItem.getDeviceId());
        List<IotFlowBindListVo> flowBindListVos = iotFlowBindProvider.list(iotFlowBindListDto);
        if (flowBindListVos == null || flowBindListVos.size() == 0) {
            log.warn("设备sn:{} 未找到客流绑定信息", klDataItem.getDeviceId());
            return;
        }
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceId(klDataItem.getDeviceId());
        IotDeviceGetVo deviceVo = iotDeviceProvider.get(iotDeviceListDto);
        if (deviceVo == null) {
            log.warn("设备sn:{} 未找到设备信息", klDataItem.getDeviceId());
            return;
        }

        for (IotFlowBindListVo bindVo : flowBindListVos) {
            if (!data.containsKey(bindVo.getAreaType())) {
                continue;
            }
            IotFlowDataAddDto dto = new IotFlowDataAddDto();
            dto.setTenantId(deviceVo.getTenantId());
            dto.setDeviceId(deviceVo.getDeviceId());
            dto.setDeviceName(deviceVo.getDeviceName());
            dto.setDeviceCode(deviceVo.getDeviceCode());
            dto.setDeviceModel(deviceVo.getDeviceModel());
            dto.setDeviceName(deviceVo.getDeviceName());
            dto.setDeviceEnable(deviceVo.getDeviceEnable());
            dto.setUseObj(bindVo.getUseObj());
            dto.setObjCode(bindVo.getObjCode());
            dto.setUseType(bindVo.getUseType());
            dto.setCountDirection(bindVo.getCountDirection());

            dto.setCreateTime(LocalDateTime.now());

            if (data != null && data.size() > 0) {
                KsklData.KsklCountItem dataItem = data.get(bindVo.getAreaType());
                dto.setCollectTime(klDataItem.getTime());
                dto.setEnterCount(Long.valueOf(dataItem.getEnter_count()));
                dto.setOutCount(Long.valueOf(dataItem.getExit_count()));
            }
            dtos.add(dto);
        }
        for (IotFlowDataAddDto dto : dtos) {
            iotFlowDataProvider.add(dto);
        }
    }

    /**
     * 获取区域型客流设备的区域客流信息
     *
     * @param eventMessage
     */
    private void handlerAreaFlowData(EventMessage eventMessage) {
        List<IotFlowDataAddDto> dtos = new ArrayList<>();
        AreaFlowResourceData areaFlowResourceData = JSON.parseObject(eventMessage.getMessageBody(), AreaFlowResourceData.class);
        log.info("AreaFlowData: {}", areaFlowResourceData);

        IotFlowBindListDto iotFlowBindListDto = new IotFlowBindListDto();
        iotFlowBindListDto.setDeviceId(areaFlowResourceData.getDeviceId());
        List<IotFlowBindListVo> flowBindListVos = iotFlowBindProvider.list(iotFlowBindListDto);
        if (flowBindListVos == null || flowBindListVos.size() == 0) {
            log.warn("handlerAreaFlowData:设备sn:{} 未找到客流绑定信息", areaFlowResourceData.getDeviceId());
            return;
        }
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceId(areaFlowResourceData.getDeviceId());
        IotDeviceGetVo deviceVo = iotDeviceProvider.get(iotDeviceListDto);
        if (deviceVo == null) {
            log.warn("handlerAreaFlowData:设备sn:{} 未找到设备信息", areaFlowResourceData.getDeviceId());
            return;
        }
        //数据按照对于格式先取出来。  客流设备传输的源数据格式
        List<AreaFlowResourceData.DataItem> flowResourceData = areaFlowResourceData.getData();
        List<AreaFlowDataItem> areaFlowDataItems = null;
        if (!CollectionUtils.isEmpty(flowResourceData)) {
            areaFlowDataItems = convertFlowData(flowResourceData);
            Map<String, List<AreaFlowDataItem>> dataItemMap = areaFlowDataItems.stream().collect(Collectors.groupingBy(item -> item.getDeviceId() + "&" + item.getLineID()));
            //插入客流数据（根据区域ID+设备ID）
            for (IotFlowBindListVo bindVo : flowBindListVos) {
                IotFlowDataAddDto dto = new IotFlowDataAddDto();
                dto.setTenantId(deviceVo.getTenantId());
                dto.setDeviceId(deviceVo.getDeviceId());
                dto.setDeviceName(deviceVo.getDeviceName());
                dto.setDeviceCode(deviceVo.getDeviceCode());
                dto.setDeviceModel(deviceVo.getDeviceModel());
                dto.setDeviceName(deviceVo.getDeviceName());
                dto.setDeviceEnable(deviceVo.getDeviceEnable());
                dto.setUseObj(bindVo.getUseObj());
                dto.setObjCode(bindVo.getObjCode());
                dto.setUseType(bindVo.getUseType());
                dto.setCountDirection(bindVo.getCountDirection());
                dto.setCreateTime(LocalDateTime.now());
                String key = bindVo.getDeviceId() + "&" + bindVo.getAreaType();
                List<AreaFlowDataItem> list = dataItemMap.get(key);

                //区域和该设备区域不匹配的过滤掉。
                if (CollectionUtils.isEmpty(list)) {
                    log.warn("handlerAreaFlowData:设备sn:{} 未找到设备对应的区域类型数据", JSONObject.toJSONString(dataItemMap));
                    continue;
                }
                AreaFlowDataItem areaFlowDataItem = list.get(0);
                LocalDateTime endDateTime = LocalDateTime.from(IOT_TIME_FORMATTER.parse(areaFlowDataItem.getEndCountingTime()));
                dto.setCollectTime(endDateTime);
                dto.setEnterCount(Long.valueOf(areaFlowDataItem.getEnteringNumber()));
                dto.setOutCount(Long.valueOf(areaFlowDataItem.getLeavingNumber()));
                dtos.add(dto);
            }
            for (IotFlowDataAddDto dto : dtos) {
                iotFlowDataProvider.add(dto);
            }
        }
    }

    /**
     * 根据客流设备传输的客流数据结构，转换成 区域+设备ID粒度的客流数据。
     *
     * @param dataItems
     * @return
     */
    private static List<AreaFlowDataItem> convertFlowData(List<AreaFlowResourceData.DataItem> dataItems) {
        List<AreaFlowDataItem> result = new ArrayList<>();
        for (AreaFlowResourceData.DataItem dataItem : dataItems) {
            String deviceId = dataItem.getDeviceID();
            String deviceName = dataItem.getDeviceName();
            List<AreaFlowResourceData.DataItem.Item> lineList = dataItem.getLinePassDataItems();
            //获取该设备下多个区域的对于的客流数据
            for (AreaFlowResourceData.DataItem.Item item : lineList) {
                AreaFlowDataItem areaFlowDataItem = new AreaFlowDataItem();
                areaFlowDataItem.setDeviceId(deviceId);
                areaFlowDataItem.setDeviceName(deviceName);
                areaFlowDataItem.setLineID(item.getLineID());
                areaFlowDataItem.setEnteringNumber(item.getEnteringNumber());
                areaFlowDataItem.setLeavingNumber(item.getLeavingNumber());
                areaFlowDataItem.setStartCountingTime(item.getStartCountingTime());
                areaFlowDataItem.setEndCountingTime(item.getEndCountingTime());
                result.add(areaFlowDataItem);
            }
        }
        return result;
    }

    private void handlerFireData(EventMessage eventMessage) {
        FireAlarmDataItem fireDataItem = JSON.parseObject(eventMessage.getMessageBody(), FireAlarmDataItem.class);
        List<com.seewin.som.iot.listener.vo.fire.DataItem> data = fireDataItem.getData();
        log.info("fire data: {}", fireDataItem);

//        IotFireBindListDto bindListDto = new IotFireBindListDto();
//        bindListDto.setDeviceId(fireDataItem.getDeviceId());
//        List<IotFireBindListVo> bindListVos = iotFireBindProvider.list(bindListDto);
//        if (bindListVos == null || bindListVos.size() == 0) {
//            log.warn("设备sn:{} 未找到动火离人绑定信息", fireDataItem.getDeviceId());
//            return;
//        }
        IotDeviceListDto iotDeviceListDto = new IotDeviceListDto();
        iotDeviceListDto.setDeviceId(fireDataItem.getDeviceId());
        IotDeviceGetVo deviceVo = iotDeviceProvider.get(iotDeviceListDto);
        if (deviceVo == null) {
            log.warn("设备sn:{} 未找到设备信息", fireDataItem.getDeviceId());
            return;
        }

//        for (IotFireBindListVo bindVo : bindListVos) {
        IotDeviceAlarmReceiveDto dto = new IotDeviceAlarmReceiveDto();
        dto.setDeviceId(deviceVo.getDeviceId());

        if (data != null && data.size() > 0) {
            com.seewin.som.iot.listener.vo.fire.DataItem dataItem = data.get(0);
            dto.setAlarmPic(dataItem.getImage_data());
            dto.setAlarmActive(dataItem.getAlarm_active());
            dto.setAlarmTime(dataItem.getTrigger_time());
        }
        iotFireAlarmProvider.receive(dto);
//        }
    }

    public static void main(String[] args) {
        // 原始字符串
        //String input = "2023-09-21T09:49:17Z";
        //// 解析原始字符串为 ZonedDateTime 对象
        //ZonedDateTime zdt = ZonedDateTime.parse(input);
        //
        //// 创建一个 DateTimeFormatter 对象，用于指定输出格式
        //DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        //// 使用 formatter 将 ZonedDateTime 对象格式化为指定格式的字符串
        //String formattedDateTime = zdt.format(formatter);
        //// 输出格式化后的字符串
        //System.out.println(formattedDateTime);
        //
        //LocalDateTime localTime = zdt.toLocalDateTime();
        //System.out.println(localTime);
//
//        //UTC时间
//        LocalDateTime utcDateTime = LocalDateTime.now(ZoneId.of("UTC"));
//        System.out.println(utcDateTime);
//        // 转换为东八区时间
//        ZonedDateTime beijingTime = utcDateTime.atZone(ZoneId.of("UTC")).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
//        LocalDateTime localDateTime = beijingTime.toLocalDateTime();
//        System.out.println(utcDateTime);
//        System.out.println(beijingTime);
//        System.out.println(localDateTime);

//        String message = "{\"channelId\":2830,\"data\":[{\"pic\":\"[\\\"http://116.205.245.238:9000/developer/bitmap/2024/06/06/1342fc21-0c0e-43ba-9d4b-decdaeae2846.jpg\\\"]\",\"text\":\"\\r\\r\\r\\r\"}],\"dataType\":1,\"deviceId\":\"864708066786891\",\"modelId\":\"19_xpnr\",\"productId\":151}";
//
//        XpDataItem xpDataItem = JSON.parseObject(message, XpDataItem.class);
//        log.info("xp data: {}", xpDataItem);
//        List<DateItem> dateItems = xpDataItem.getData();
//        String data = dateItems.get(0).getText();
//        System.out.println(StringUtils.isEmpty(data));
//        System.out.println(StringUtils.isEmpty(data.trim()));
//        System.out.println(dateItems.get(0).getPic());
//
//        if ((StringUtils.isEmpty(data) || StringUtils.isEmpty(data.trim())) && StringUtils.isNotEmpty(dateItems.get(0).getPic())) {
//            JSONArray array = JSONArray.parseArray(dateItems.get(0).getPic());
//            if (array.size() > 0) {
//                String url = array.getString(0);
//                System.out.println(url);
//            }
//
//        }

        KsklData klDataItem = JSON.parseObject("{\"productId\":123,\"modelId\":\"modelIdxx\",\"deviceId\":\"deviceIdxx\",\"channelId\":234,\"data\":[{\"head_count\":{\"head_count\":{\"count_list\":[{\"device_id\":0,\"end_time\":1728352783,\"gateway_list\":[{\"enter_count\":157,\"exit_count\":111,\"id\":1},{\"enter_count\":152,\"exit_count\":112,\"id\":2}],\"start_time\":1728352783}]},\"statistical_type\":\"head_count\"}}]}", KsklData.class);
        System.out.println(klDataItem);
    }
}