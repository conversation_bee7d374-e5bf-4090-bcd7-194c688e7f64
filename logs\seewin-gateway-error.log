{"appName":"seewin-gateway","time":"2025-05-29 10:54:02","level":"ERROR","class":"com.seewin.gateway.config.GlobalExceptionHandler","method":"handle","line":"46","message":"网关异常全局处理，异常信息：503 SERVICE_UNAVAILABLE \"Unable to find instance for som-consumer-adm-system\"","statck_trace":""}
{"appName":"seewin-gateway","time":"2025-05-29 10:54:27","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"198","message":"凭证过期","statck_trace":"io.jsonwebtoken.ExpiredJwtException: JWT expired at 2025-05-29T01:36:27Z. Current time: 2025-05-29T02:54:27Z, a difference of 4680066 milliseconds.  Allowed clock skew: 0 milliseconds.\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:427)\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parse(DefaultJwtParser.java:529)\r\n\tat io.jsonwebtoken.impl.DefaultJwtParser.parseClaimsJws(DefaultJwtParser.java:589)\r\n\tat io.jsonwebtoken.impl.ImmutableJwtParser.parseClaimsJws(ImmutableJwtParser.java:173)\r\n\tat com.seewin.redis.util.JwtUtil.parserToken(JwtUtil.java:42)\r\n\tat com.seewin.redis.handler.TokenHandler.parseToken(TokenHandler.java:67)\r\n\tat com.seewin.gateway.filter.OptFilter.filter(OptFilter.java:145)\r\n\tat com.seewin.gateway.factory.AuthFilterGatewayFilterFactory.lambda$apply$0(AuthFilterGatewayFilterFactory.java:91)\r\n\tat org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)\r\n\tat org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:136)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:100)\r\n\tat reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:206)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerComplete(FluxConcatMapNoPrefetch.java:274)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onComplete(FluxConcatMap.java:887)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.innerResult(MonoFilterWhen.java:216)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:332)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:290)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onSubscribe(MonoFilterWhen.java:313)\r\n\tat reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:193)\r\n\tat reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:146)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)\r\n\tat reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1169)\r\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:710)\r\n\tat reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:481)\r\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:652)\r\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:238)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\n"}
{"appName":"seewin-gateway","time":"2025-05-29 11:20:33","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"214","message":"请求异常","statck_trace":"org.springframework.data.redis.RedisSystemException: Redis exception\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)\r\n\tat org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)\r\n\tat org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:306)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1010)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$3(LettuceConnection.java:443)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:72)\r\n\tat org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:86)\r\n\tat org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:337)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$3(RedisTemplate.java:571)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)\r\n\tat org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:571)\r\n\tat com.seewin.redis.util.RedisUtil.get(RedisUtil.java:24)\r\n\tat com.seewin.redis.handler.ParamHandler.get(ParamHandler.java:13)\r\n\tat com.seewin.gateway.filter.OptFilter.filter(OptFilter.java:92)\r\n\tat com.seewin.gateway.factory.AuthFilterGatewayFilterFactory.lambda$apply$0(AuthFilterGatewayFilterFactory.java:91)\r\n\tat org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)\r\n\tat org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:136)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:100)\r\n\tat reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:206)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerComplete(FluxConcatMapNoPrefetch.java:274)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onComplete(FluxConcatMap.java:887)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.innerResult(MonoFilterWhen.java:216)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:332)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:290)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onSubscribe(MonoFilterWhen.java:313)\r\n\tat reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:193)\r\n\tat reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:146)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)\r\n\tat reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1169)\r\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:710)\r\n\tat reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:481)\r\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:652)\r\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:238)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset\r\n\tat io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)\r\n\tat io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)\r\n\tat io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1008)\r\n\t... 139 common frames omitted\r\nCaused by: java.net.SocketException: Connection reset\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)\r\n\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)\r\n\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\r\n\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\r\n\t... 8 common frames omitted\r\n"}
{"appName":"seewin-gateway","time":"2025-05-29 11:33:25","level":"ERROR","class":"com.seewin.gateway.filter.OptFilter","method":"filter","line":"214","message":"请求异常","statck_trace":"org.springframework.data.redis.RedisSystemException: Redis exception\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:72)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceExceptionConverter.convert(LettuceExceptionConverter.java:41)\r\n\tat org.springframework.data.redis.PassThroughExceptionTranslationStrategy.translate(PassThroughExceptionTranslationStrategy.java:40)\r\n\tat org.springframework.data.redis.FallbackExceptionTranslationStrategy.translate(FallbackExceptionTranslationStrategy.java:38)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.convertLettuceAccessException(LettuceConnection.java:306)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1010)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.lambda$doInvoke$3(LettuceConnection.java:443)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$Synchronizer.invoke(LettuceInvoker.java:673)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceInvoker$DefaultSingleInvocationSpec.get(LettuceInvoker.java:589)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceKeyCommands.exists(LettuceKeyCommands.java:72)\r\n\tat org.springframework.data.redis.connection.DefaultedRedisConnection.exists(DefaultedRedisConnection.java:86)\r\n\tat org.springframework.data.redis.connection.DefaultStringRedisConnection.exists(DefaultStringRedisConnection.java:337)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$hasKey$3(RedisTemplate.java:571)\r\n\tat org.springframework.data.redis.core.RedisTemplate.lambda$doWithKeys$22(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:406)\r\n\tat org.springframework.data.redis.core.RedisTemplate.execute(RedisTemplate.java:373)\r\n\tat org.springframework.data.redis.core.RedisTemplate.doWithKeys(RedisTemplate.java:785)\r\n\tat org.springframework.data.redis.core.RedisTemplate.hasKey(RedisTemplate.java:571)\r\n\tat com.seewin.redis.util.RedisUtil.get(RedisUtil.java:24)\r\n\tat com.seewin.redis.handler.ParamHandler.get(ParamHandler.java:13)\r\n\tat com.seewin.gateway.filter.OptFilter.filter(OptFilter.java:92)\r\n\tat com.seewin.gateway.factory.AuthFilterGatewayFilterFactory.lambda$apply$0(AuthFilterGatewayFilterFactory.java:91)\r\n\tat org.springframework.cloud.gateway.filter.OrderedGatewayFilter.filter(OrderedGatewayFilter.java:44)\r\n\tat org.springframework.cloud.gateway.handler.FilteringWebHandler$DefaultGatewayFilterChain.lambda$filter$0(FilteringWebHandler.java:117)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:45)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:165)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.FluxSwitchIfEmpty$SwitchIfEmptySubscriber.onNext(FluxSwitchIfEmpty.java:74)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.onNext(MonoFlatMap.java:158)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.onNext(FluxMap.java:122)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onNext(MonoNext.java:82)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerNext(FluxConcatMapNoPrefetch.java:258)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onNext(FluxConcatMap.java:863)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onNext(FluxOnErrorResume.java:79)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onNext(MonoPeekTerminal.java:180)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:136)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.FluxOnErrorResume$ResumeSubscriber.onSubscribe(FluxOnErrorResume.java:74)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onSubscribe(MonoPeekTerminal.java:152)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onSubscribe(MonoFilterWhen.java:100)\r\n\tat reactor.core.publisher.MonoJust.subscribe(MonoJust.java:55)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onNext(FluxConcatMapNoPrefetch.java:206)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:98)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.onNext(FluxDematerialize.java:44)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.slowPath(FluxIterable.java:335)\r\n\tat reactor.core.publisher.FluxIterable$IterableSubscription.request(FluxIterable.java:294)\r\n\tat reactor.core.publisher.FluxDematerialize$DematerializeSubscriber.request(FluxDematerialize.java:127)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.innerComplete(FluxConcatMapNoPrefetch.java:274)\r\n\tat reactor.core.publisher.FluxConcatMap$ConcatMapInner.onComplete(FluxConcatMap.java:887)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onComplete(Operators.java:2231)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.onComplete(MonoPeekTerminal.java:299)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.innerResult(MonoFilterWhen.java:216)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:332)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onNext(MonoFilterWhen.java:290)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$FilterWhenInner.onSubscribe(MonoFilterWhen.java:313)\r\n\tat reactor.core.publisher.FluxFlatMap.trySubscribeScalarMap(FluxFlatMap.java:193)\r\n\tat reactor.core.publisher.MonoFlatMap.subscribeOrReturn(MonoFlatMap.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:63)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.onNext(MonoFilterWhen.java:146)\r\n\tat reactor.core.publisher.Operators$ScalarSubscription.request(Operators.java:2571)\r\n\tat reactor.core.publisher.MonoFilterWhen$MonoFilterWhenMain.request(MonoFilterWhen.java:182)\r\n\tat reactor.core.publisher.MonoPeekTerminal$MonoTerminalPeekSubscriber.request(MonoPeekTerminal.java:139)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.MonoFlatMap$FlatMapMain.request(MonoFlatMap.java:194)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxMap$MapSubscriber.request(FluxMap.java:164)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.request(Operators.java:2331)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.request(FluxConcatMapNoPrefetch.java:338)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.request(MonoNext.java:108)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.set(Operators.java:2367)\r\n\tat reactor.core.publisher.Operators$MultiSubscriptionSubscriber.onSubscribe(Operators.java:2241)\r\n\tat reactor.core.publisher.MonoNext$NextSubscriber.onSubscribe(MonoNext.java:70)\r\n\tat reactor.core.publisher.FluxConcatMapNoPrefetch$FluxConcatMapNoPrefetchSubscriber.onSubscribe(FluxConcatMapNoPrefetch.java:164)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:201)\r\n\tat reactor.core.publisher.FluxIterable.subscribe(FluxIterable.java:83)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDefer.subscribe(MonoDefer.java:53)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoIgnoreThen$ThenIgnoreMain.subscribeNext(MonoIgnoreThen.java:264)\r\n\tat reactor.core.publisher.MonoIgnoreThen.subscribe(MonoIgnoreThen.java:51)\r\n\tat reactor.core.publisher.InternalMonoOperator.subscribe(InternalMonoOperator.java:76)\r\n\tat reactor.core.publisher.MonoDeferContextual.subscribe(MonoDeferContextual.java:55)\r\n\tat reactor.netty.http.server.HttpServer$HttpServerHandle.onStateChange(HttpServer.java:1169)\r\n\tat reactor.netty.ReactorNetty$CompositeConnectionObserver.onStateChange(ReactorNetty.java:710)\r\n\tat reactor.netty.transport.ServerTransport$ChildObserver.onStateChange(ServerTransport.java:481)\r\n\tat reactor.netty.http.server.HttpServerOperations.onInboundNext(HttpServerOperations.java:652)\r\n\tat reactor.netty.channel.ChannelOperationsHandler.channelRead(ChannelOperationsHandler.java:114)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:444)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat reactor.netty.http.server.HttpTrafficHandler.channelRead(HttpTrafficHandler.java:238)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler$DelegatingChannelHandlerContext.fireChannelRead(CombinedChannelDuplexHandler.java:436)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:346)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.fireChannelRead(ByteToMessageDecoder.java:333)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.callDecode(ByteToMessageDecoder.java:454)\r\n\tat io.netty.handler.codec.ByteToMessageDecoder.channelRead(ByteToMessageDecoder.java:290)\r\n\tat io.netty.channel.CombinedChannelDuplexHandler.channelRead(CombinedChannelDuplexHandler.java:251)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:442)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:412)\r\n\tat io.netty.channel.DefaultChannelPipeline$HeadContext.channelRead(DefaultChannelPipeline.java:1410)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:440)\r\n\tat io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:420)\r\n\tat io.netty.channel.DefaultChannelPipeline.fireChannelRead(DefaultChannelPipeline.java:919)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:166)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKey(NioEventLoop.java:788)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeysOptimized(NioEventLoop.java:724)\r\n\tat io.netty.channel.nio.NioEventLoop.processSelectedKeys(NioEventLoop.java:650)\r\n\tat io.netty.channel.nio.NioEventLoop.run(NioEventLoop.java:562)\r\n\tat io.netty.util.concurrent.SingleThreadEventExecutor$4.run(SingleThreadEventExecutor.java:997)\r\n\tat io.netty.util.internal.ThreadExecutorMap$2.run(ThreadExecutorMap.java:74)\r\n\tat io.netty.util.concurrent.FastThreadLocalRunnable.run(FastThreadLocalRunnable.java:30)\r\n\tat java.base/java.lang.Thread.run(Thread.java:842)\r\nCaused by: io.lettuce.core.RedisException: java.net.SocketException: Connection reset\r\n\tat io.lettuce.core.internal.Exceptions.bubble(Exceptions.java:83)\r\n\tat io.lettuce.core.internal.Futures.awaitOrCancel(Futures.java:250)\r\n\tat io.lettuce.core.LettuceFutures.awaitOrCancel(LettuceFutures.java:74)\r\n\tat org.springframework.data.redis.connection.lettuce.LettuceConnection.await(LettuceConnection.java:1008)\r\n\t... 139 common frames omitted\r\nCaused by: java.net.SocketException: Connection reset\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.throwConnectionReset(SocketChannelImpl.java:394)\r\n\tat java.base/sun.nio.ch.SocketChannelImpl.read(SocketChannelImpl.java:426)\r\n\tat io.netty.buffer.PooledByteBuf.setBytes(PooledByteBuf.java:255)\r\n\tat io.netty.buffer.AbstractByteBuf.writeBytes(AbstractByteBuf.java:1132)\r\n\tat io.netty.channel.socket.nio.NioSocketChannel.doReadBytes(NioSocketChannel.java:357)\r\n\tat io.netty.channel.nio.AbstractNioByteChannel$NioByteUnsafe.read(AbstractNioByteChannel.java:151)\r\n\t... 8 common frames omitted\r\n"}
