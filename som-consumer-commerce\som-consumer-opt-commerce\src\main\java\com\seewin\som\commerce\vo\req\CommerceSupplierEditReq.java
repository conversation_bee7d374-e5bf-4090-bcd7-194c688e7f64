package com.seewin.som.commerce.vo.req;

import java.time.LocalDateTime;
import java.io.Serializable;
import java.util.Collections;
import java.util.List;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Getter;
import lombok.Setter;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * <p>
 * 零售供应商表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Getter
@Setter
public class CommerceSupplierEditReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @Schema(description = "主键")
    @NotNull(message = "主键不能为空")
    private Long id;

    /**
     * 供应商名称
     */
    @Schema(description = "供应商名称")
    @NotBlank(message = "供应商名称不能为空")
    @Size(max = 50, message = "供应商名称最大长度不能超过50")
    private String name;

    /**
     * 1.身份证、2.营业执照
     */
    @Schema(description = "1.身份证、2.营业执照 ")
    @NotNull(message = "1.身份证、2.营业执照 不能为空")
    private Integer certificateType;

    /**
     * 零售供应商身份证地址
     */
    @Schema(description = "零售供应商身份证地址")
    @Size(max = 50, message = "零售供应商身份证地址最大长度不能超过50")
    private String cardAddress;

    /**
     * 零售供应商身份证证件号
     */
    @Schema(description = "零售供应商身份证证件号")
    @Size(max = 18, message = "零售供应商身份证证件号最大长度不能超过18")
    private String cardNumber;

    /**
     * 联系人姓名
     */
    @Schema(description = "联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    @Size(max = 20, message = "联系人姓名最大长度不能超过20")
    private String contactName;

    /**
     * 联系人岗位，数据字典：contact_position
     */
    @Schema(description = "联系人岗位，数据字典：contact_position")
    @NotBlank(message = "联系人岗位，数据字典：contact_position不能为空")
    @Size(max = 32, message = "联系人岗位，数据字典：contact_position最大长度不能超过32")
    private String contactJob;

    /**
     * 联系人方式（号码）
     */
    @Schema(description = "联系人方式（号码）")
    @NotBlank(message = "联系人方式（号码）不能为空")
    @Size(max = 32, message = "联系人方式（号码）最大长度不能超过32")
    private String contactPhon;

    /**
     * 营业执照公司名称
     */
    @Schema(description = "营业执照公司名称")
    @Size(max = 50, message = "营业执照公司名称最大长度不能超过50")
    private String companyName;

    /**
     * 营业执照统一社会信用代码
     */
    @Schema(description = "营业执照统一社会信用代码")
    @Size(max = 50, message = "营业执照统一社会信用代码最大长度不能超过50")
    private String socialCreditCode;

    /**
     * 营业执照法定代表人
     */
    @Schema(description = "营业执照法定代表人")
    @Size(max = 50, message = "营业执照法定代表人最大长度不能超过50")
    private String legalRepresentative;

    /**
     * 在营开店数
     */
    @Schema(description = "在营开店数 ")
//    @NotNull(message = "在营开店数 不能为空")
    private Integer storeNum;

    /**
     * 开店城市，、分隔
     */
    @Schema(description = "开店城市，、分隔")
//    @NotBlank(message = "开店城市，、分隔不能为空")
    @Size(max = 255, message = "开店城市，、分隔最大长度不能超过255")
    private String citys;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;
    /**
     * 税号
     */
    @Schema(description = "税号")
    private String taxNo;

    @Schema(description = "经营品牌数据")
    private List<CommerceSupplierBrandReq> brandList= Collections.emptyList();
    @Schema(description = "身份证，营业执照（busSubType=3003（身份证），3004（营业执照））,如果为商标，则需上传")
    private List<Long> cardFiles;

    @Schema(description = "操作来源  1-编辑  2-审批重新发起")
    private Integer operationSource = 1;

    @Schema(description = "报税人类型 0.一般纳税人 1.小规模纳税人 2.个人")
    private Integer taxPeopleType;
}
