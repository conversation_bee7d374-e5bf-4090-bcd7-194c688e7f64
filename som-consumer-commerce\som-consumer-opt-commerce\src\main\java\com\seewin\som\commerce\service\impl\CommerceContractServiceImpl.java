package com.seewin.som.commerce.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.cscec1b.consumer.vo.FileResp;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.enums.ComercePlanApproveStatusEnum;
import com.seewin.som.commerce.enums.FileType;
import com.seewin.som.commerce.enums.OrderExecuteStepEnum;
import com.seewin.som.commerce.enums.ReviewStatus;
import com.seewin.som.commerce.listener.CommerceContractListener;
import com.seewin.som.commerce.listener.CommerceOpenDeliveryListener;
import com.seewin.som.commerce.provider.*;
import com.seewin.som.commerce.req.*;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.commerce.service.CommerceContractService;
import com.seewin.som.commerce.utils.FileUtils;
import com.seewin.som.commerce.utils.SequenceCodeUtils;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.commerce.vo.resp.*;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.req.EntProjectListDto;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.ent.resp.EntProjectListVo;
import com.seewin.som.message.provider.MsgLogProvider;
import com.seewin.som.rent.provider.RentPolicyProvider;
import com.seewin.som.rent.req.RentPolicyListDto;
import com.seewin.som.rent.resp.RentPolicyGetVo;
import com.seewin.som.rent.utils.CalculateRuleUtils;
import com.seewin.som.space.provider.RoomsExpandProvider;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsEditDto;
import com.seewin.som.space.req.RoomsExpandListDto;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsExpandGetVo;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.som.space.resp.RoomsListVo;
import com.seewin.som.storage.provider.SysAttachmentProvider;
import com.seewin.som.storage.resp.SysAttachmentListVo;
import com.seewin.som.workflow.provider.FlwApplyProvider;
import com.seewin.som.workflow.provider.ProcessServiceProvider;
import com.seewin.som.workflow.provider.ProcessStatusEnum;
import com.seewin.som.workflow.req.FlwApplyListDto;
import com.seewin.som.workflow.req.ProcessStartDto;
import com.seewin.som.workflow.req.ReSubmitDto;
import com.seewin.som.workflow.resp.FlwApplyGetVo;
import com.seewin.som.workflow.resp.ProcessTaskVo;
import com.seewin.util.exception.ServiceException;
import io.seata.spring.annotation.GlobalTransactional;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.JsonUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 招商合同表-商铺 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
@Service
@Slf4j
public class CommerceContractServiceImpl implements CommerceContractService {
    /**
     * providedBy：兼容Mesh服务
     */
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractInfoProvider commerceContractInfoProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractRentProvider commerceContractRentProvider;
    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;
    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;
    @DubboReference(providedBy = "som-space-mgt")
    private RoomsExpandProvider roomsExpandProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceOrderProvider commerceOrderProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceSubOrderProvider commerceSubOrderProvider;
    @DubboReference(providedBy = "som-storage-mgt")
    private SysAttachmentProvider attachmentProvider;
    @DubboReference(providedBy = "som-workflow-mgt")
    private ProcessServiceProvider processServiceProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceOrderExtendProvider commerceOrderExtendProvider;
    @DubboReference(providedBy = "som-message-mgt")
    private MsgLogProvider msgLogProvider;
    @DubboReference(providedBy = "som-workflow-mgt")
    private FlwApplyProvider flwApplyProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceBrandProvider commerceBrandProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceSupplierProvider commerceSupplierProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceEntexitProvider commerceEntexitProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommercePlanDetailPresentProvider commercePlanDetailPresentProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentPolicyProvider rentPolicyProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractLogProvider commerceContractLogProvider;
    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceAdvertContractProvider advertContractProvider;

    @Value("${esign.app.difyUrl}")
    private String difyUrl;

    @Value("${esign.app.apiKey}")
    private String apiKey;

    /**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<CommerceContractListItem> page(CommerceContractListReq listReq, boolean pageAll) {
        PageResp<CommerceContractListItem> pageResp = new PageResp<>();
        OptUser curUser = ApiUtils.getUser(OptUser.class);

        CommerceContractListDto queryDto = BeanUtils.copyProperties(listReq, CommerceContractListDto.class);
        queryDto.setEntId(curUser.getEntId());
        queryDto.setTenantId(curUser.getTenantId());
        if (!pageAll) {
            queryDto.setExecuteUser(curUser.getUserName());
        }
        //如果查询租赁类型
        if (ObjectUtils.isNotEmpty(listReq.getRentType())) {
            CommerceContractInfoListDto dto = new CommerceContractInfoListDto();
            dto.setTenantId(curUser.getTenantId());
            dto.setEntId(curUser.getEntId());
            dto.setRentType(listReq.getRentType());
            List<CommerceContractInfoListVo> listVoList = commerceContractInfoProvider.list(dto);
            if (CollectionUtils.isEmpty(listVoList)) {
                pageResp.setPageNum(listReq.getPageNum());
                pageResp.setPageSize(listReq.getPageSize());
                pageResp.setPages(0);
                pageResp.setTotal(0);
                pageResp.setItems(new ArrayList<>());
                return pageResp;
            }
            queryDto.setContractCodeList(listVoList.stream().map(CommerceContractInfoListVo::getContractCode).collect(Collectors.toList()));
        }
        if (CollectionUtils.isNotEmpty(listReq.getIds())){
            queryDto.setIdList(listReq.getIds());
        }
        PageQuery<CommerceContractListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<CommerceContractListVo> pageResult = commerceContractProvider.page(pageQuery);
        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), CommerceContractListItem.class));

        return pageResp;
    }

        /**
     * <p>查询<br>
     *
     * @param listReq 查询条件VO
     * @return 查询结果
     */
    @Override
    public List<CommerceContractListItemResp> listEffective(CommerceContractListReq listReq, boolean pageAll) {
        List<CommerceContractListItemResp> respList = new ArrayList<>();
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        CommerceContractListDto queryDto = BeanUtils.copyProperties(listReq, CommerceContractListDto.class);
        queryDto.setEntId(curUser.getEntId());
        queryDto.setTenantId(curUser.getTenantId());
        if (!pageAll) {
            queryDto.setExecuteUser(curUser.getUserName());
        }
        //如果查询租赁类型
        if (ObjectUtils.isNotEmpty(listReq.getRentType())) {
            CommerceContractInfoListDto dto = new CommerceContractInfoListDto();
            dto.setTenantId(curUser.getTenantId());
            dto.setEntId(curUser.getEntId());
            dto.setRentType(listReq.getRentType());
            List<CommerceContractInfoListVo> listVoList = commerceContractInfoProvider.list(dto);
            if (CollectionUtils.isEmpty(listVoList)) {
                return respList;
            }
            queryDto.setContractCodeList(listVoList.stream().map(CommerceContractInfoListVo::getContractCode).collect(Collectors.toList()));
        }
        // 当前日期
        LocalDate currentDate = LocalDate.now();

        // 最终返回-合并
        List<CommerceContractListVo> commerceContractListVoList = new ArrayList<>();

        // 审批中合同主表数据
        queryDto.setApproveStatus(ComercePlanApproveStatusEnum.beingProcessed.getCode());
        // 合同info表
        List<CommerceContractListVo> beingProcessedCommerceContractListVoList = commerceContractProvider.list(queryDto);
        // 合同info表map
        Map<String, CommerceContractInfoListVo> beingProcessedCommerceContractInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(beingProcessedCommerceContractListVoList)) {
            // 审批中合同info表数据
            CommerceContractInfoListDto beingProcessedDto = new CommerceContractInfoListDto();
            beingProcessedDto.setTenantId(curUser.getTenantId());
            beingProcessedDto.setEntId(curUser.getEntId());
            beingProcessedDto.setContractCodeList(beingProcessedCommerceContractListVoList.stream().map(CommerceContractListVo::getContractCode).collect(Collectors.toList()));
            List<CommerceContractInfoListVo> beingProcessedInfoListVoList = commerceContractInfoProvider.list(beingProcessedDto);
            if (CollectionUtils.isNotEmpty(beingProcessedInfoListVoList)) {
                for (CommerceContractInfoListVo infoVo : beingProcessedInfoListVoList) {
                    beingProcessedCommerceContractInfoMap.put(infoVo.getContractCode(), infoVo);
                }
            }

            commerceContractListVoList.addAll(beingProcessedCommerceContractListVoList);
        }

        // 审批通过合同主表数据
        queryDto.setApproveStatus(ComercePlanApproveStatusEnum.approve.getCode());
        List<CommerceContractListVo> approveCommerceContractListVoList = commerceContractProvider.list(queryDto);
        // 主表map
        Map<String, CommerceContractListVo> approveCommerceContractMap = new HashMap<>();
        // 合同info表map
        Map<String, CommerceContractInfoListVo> approveCommerceContractInfoMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(approveCommerceContractListVoList)) {
            for (CommerceContractListVo vo : approveCommerceContractListVoList) {
                if (StringUtils.isNotEmpty(vo.getContractCode())) {
                    approveCommerceContractMap.put(vo.getContractCode(), vo);
                }
            }
            // 审批通过合同info表数据
            CommerceContractInfoListDto approveDto = new CommerceContractInfoListDto();
            approveDto.setTenantId(curUser.getTenantId());
            approveDto.setEntId(curUser.getEntId());
            approveDto.setContractCodeList(approveCommerceContractListVoList.stream().map(CommerceContractListVo::getContractCode).collect(Collectors.toList()));
            List<CommerceContractInfoListVo> approveInfoListVoList = commerceContractInfoProvider.list(approveDto);
            // 过滤审批通过 and (计费截止日期actRetriveDate为空 or (计费截止日期actRetriveDate不为空，且计费截止日期actRetriveDate大于等于当前日期的数据)）
            if (CollectionUtils.isNotEmpty(approveInfoListVoList)) {
                List<CommerceContractInfoListVo> effectiveApproveContractInfoList = approveInfoListVoList.stream()
                        .filter(contract -> (contract.getActRetriveDate() == null ||  (contract.getActRetriveDate() != null && !contract.getActRetriveDate().isBefore(currentDate))))
                        .collect(Collectors.toList());

                // 添加审批通过 and (计费截止日期actRetriveDate为空 or (计费截止日期actRetriveDate不为空，且计费截止日期actRetriveDate大于等于当前日期的数据)）
                if (CollectionUtils.isNotEmpty(effectiveApproveContractInfoList)) {
                    for (CommerceContractInfoListVo infoVo : effectiveApproveContractInfoList) {
                        CommerceContractListVo vo = approveCommerceContractMap.get(infoVo.getContractCode());
                        if (vo != null) {
                            commerceContractListVoList.add(vo);
                        }
                        if (StringUtils.isNotEmpty(infoVo.getContractCode())) {
                            approveCommerceContractInfoMap.put(infoVo.getContractCode(), infoVo);
                        }
                    }

                }
            }
        }
        if (CollectionUtils.isNotEmpty(commerceContractListVoList)) {
            for (CommerceContractListVo vo : commerceContractListVoList) {
                CommerceContractListItemResp resp = BeanUtils.copyProperties(vo, CommerceContractListItemResp.class);
                resp.setRoomShopId(vo.getRoomShopId());
                resp.setArchiveDate(vo.getArchiveDate());
                resp.setOrderStatus(vo.getOrderStatus());
                if (StringUtils.isNotEmpty(vo.getContractCode()) && beingProcessedCommerceContractInfoMap.get(vo.getContractCode()) != null) {
                    CommerceContractInfoListVo infoVo = beingProcessedCommerceContractInfoMap.get(vo.getContractCode());
                    resp.setRentEndDate(infoVo.getRentEndDate());
                    resp.setRentStartDate(infoVo.getRentStartDate());
                    resp.setActRetriveDate(infoVo.getActRetriveDate());
                    resp.setRentType(infoVo.getRentType());
                    resp.setContractType(infoVo.getContractType());
                    respList.add(resp);
                }
               if (StringUtils.isNotEmpty(vo.getContractCode()) && approveCommerceContractInfoMap.get(vo.getContractCode()) != null) {
                    CommerceContractInfoListVo infoVo = approveCommerceContractInfoMap.get(vo.getContractCode());
                    resp.setRentEndDate(infoVo.getRentEndDate());
                    resp.setRentStartDate(infoVo.getRentStartDate());
                    resp.setActRetriveDate(infoVo.getActRetriveDate());
                    resp.setRentType(infoVo.getRentType());
                    resp.setContractType(infoVo.getContractType());

                    respList.add(resp);
                }
            }

        }
        return respList;
    }

    @Override
    public CommerceContractGetResp getByContractCode(CommerceContractCodeGetReq getReq) {
        CommerceContractListDto dto = new CommerceContractListDto();
        dto.setContractCode(getReq.getContractCode());
        CommerceContractGetVo getVo = commerceContractProvider.get(dto);
        if (Objects.isNull(getVo)) {
            return null;
        }
        CommerceContractInfoGetVo infoGetVo = commerceContractInfoProvider.getByContractCode(getVo.getContractCode());
        CommerceContractRentGetVo rentGetVo = commerceContractRentProvider.getByContractCode(getVo.getContractCode());

        CommerceContractGetResp getResp = BeanUtils.copyProperties(getVo, CommerceContractGetResp.class);
        CommerceContractInfoGetResp infoGetResp = BeanUtils.copyProperties(infoGetVo, CommerceContractInfoGetResp.class);
        CommerceContractRentGetResp rentGetResp = BeanUtils.copyProperties(rentGetVo, CommerceContractRentGetResp.class);
        if (ObjectUtils.isNotEmpty(rentGetVo.getIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getIncrementalRateList(),List.class);
            rentGetResp.setIncrementalRateList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getMonthFeePercentList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getMonthFeePercentList(),List.class);
            rentGetResp.setMonthFeePercentList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getOperateIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getOperateIncrementalRateList(),List.class);
            rentGetResp.setOperateIncrementalRateList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getManageIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getManageIncrementalRateList(),List.class);
            rentGetResp.setManageIncrementalRateList(list);
        }
        getResp.setContractInfo(infoGetResp);
        // 获取租赁政策租金信息
        getRentPolicyInfo(getVo, rentGetResp);
        getResp.setContractRent(rentGetResp);

        //合同附件
//        List<SysAttachmentListVo> brandFiles = FileUtils.findFile(getVo.getId(), FileType.CONTRACT_FILE, attachmentProvider);
//        List<FileResp> files = BeanUtils.copyProperties(brandFiles, FileResp.class);
//        getResp.setFiles(files);

        getResp.setOgn(getOgn(getVo.getOrgFname(), getVo.getOrgFid()));
        getResp.setDpt(getDpt(getVo.getOrgFname(), getVo.getOrgFid()));

        return getResp;
    }

    @Override
    public List<CommerceContractHistoryItem> listHistory(CommerceContractHistoryReq historyReq) {
        //门店历史详情
        CommerceContractListDto listDto = BeanUtils.copyProperties(historyReq, CommerceContractListDto.class);
        List<CommerceContractHistoryListVo> listVos = commerceContractProvider.historyList(listDto);

        List<CommerceContractHistoryItem> itemList = BeanUtils.copyProperties(listVos, CommerceContractHistoryItem.class);
        return itemList;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public CommerceContractGetResp get(CommerceContractGetReq getReq) {
        CommerceContractGetVo getVo = commerceContractProvider.get(getReq.getId());
        CommerceContractInfoGetVo infoGetVo = commerceContractInfoProvider.getByContractCode(getVo.getContractCode());
        CommerceContractRentGetVo rentGetVo = commerceContractRentProvider.getByContractCode(getVo.getContractCode());

        CommerceContractGetResp getResp = BeanUtils.copyProperties(getVo, CommerceContractGetResp.class);
        CommerceContractInfoGetResp infoGetResp = BeanUtils.copyProperties(infoGetVo, CommerceContractInfoGetResp.class);
        CommerceContractRentGetResp rentGetResp = BeanUtils.copyProperties(rentGetVo, CommerceContractRentGetResp.class);

        getResp.setContractInfo(infoGetResp);
        // 获取租赁政策租金信息
        getRentPolicyInfo(getVo, rentGetResp);
        getResp.setContractRent(rentGetResp);

        //续签合同-原合同租赁时间
        if(Objects.nonNull(infoGetVo.getContractType()) && infoGetVo.getContractType() == 2) {
            CommerceContractGetVo lastVo = commerceContractProvider.getLastOneContract(getReq.getId());
            if (Objects.nonNull(lastVo)) {
                CommerceContractInfoGetVo lastInfoGetVo = commerceContractInfoProvider.getByContractCode(lastVo.getContractCode());
                getResp.getContractInfo().setRentStartDateLast(lastInfoGetVo.getRentStartDate());
                getResp.getContractInfo().setRentEndDateLast(lastInfoGetVo.getRentEndDate());
            }
        }

        if (ObjectUtils.isNotEmpty(rentGetVo.getIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getIncrementalRateList(),List.class);
            rentGetResp.setIncrementalRateList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getMonthFeePercentList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getMonthFeePercentList(),List.class);
            rentGetResp.setMonthFeePercentList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getOperateIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getOperateIncrementalRateList(),List.class);
            rentGetResp.setOperateIncrementalRateList(list);
        }
        if (ObjectUtils.isNotEmpty(rentGetVo.getManageIncrementalRateList())) {
            List<String> list = JSONArray.parseObject(rentGetVo.getManageIncrementalRateList(),List.class);
            rentGetResp.setManageIncrementalRateList(list);
        }
        //合同附件
        List<SysAttachmentListVo> brandFiles = FileUtils.findFile(getVo.getId(), FileType.CONTRACT_FILE, attachmentProvider);
        List<FileResp> files = BeanUtils.copyProperties(brandFiles, FileResp.class);
        getResp.setFiles(files);

        getResp.setOgn(getOgn(getVo.getOrgFname(), getVo.getOrgFid()));
        getResp.setDpt(getDpt(getVo.getOrgFname(), getVo.getOrgFid()));

        return getResp;
    }

    /**
     * 获取租赁政策租金信息
     * @param getVo
     * @param rentGetResp
     */
    private void getRentPolicyInfo(CommerceContractGetVo getVo, CommerceContractRentGetResp rentGetResp) {
        RentPolicyListDto rentPolicyDto = new RentPolicyListDto();
        rentPolicyDto.setTenantId(getVo.getTenantId());
        rentPolicyDto.setName(getVo.getName());
        RentPolicyGetVo rentPolicyGetVo = rentPolicyProvider.get(rentPolicyDto);
        if (rentPolicyGetVo!=null){
            Double rentPriceBase = rentPolicyGetVo.getRentPriceBase();
            if (rentPriceBase!=null){
                rentGetResp.setMonthPrice(BigDecimal.valueOf(rentPriceBase));
            }
            BigDecimal rent = rentPolicyGetVo.getRent();
            rentGetResp.setMonthFee(rent);
            Double rentArea = rentPolicyGetVo.getRentArea();
            BigDecimal monthPropertyFee = rentPolicyGetVo.getMonthPropertyFee();
            BigDecimal monthOperatePrice = rentPolicyGetVo.getMonthOperatePrice();
            if (rentArea!=null && monthPropertyFee!=null){
                BigDecimal monthManageFee = monthPropertyFee.multiply(BigDecimal.valueOf(rentArea)).setScale(2, RoundingMode.HALF_UP);
                rentGetResp.setMonthManageFee(monthManageFee);
            }
            if (rentArea!=null && monthOperatePrice!=null){
                BigDecimal monthOperateFee = monthOperatePrice.multiply(BigDecimal.valueOf(rentArea)).setScale(2, RoundingMode.HALF_UP);
                rentGetResp.setMonthOperateFee(monthOperateFee);
            }
        }
    }

    private String getOgn(String orgFname, String orgFid) {
        String[] fnameArr = orgFname.split("/");
        String[] fidArr = orgFid.split("/");
        for (int i = fidArr.length - 1; i >= 0; i--) {
            if (StringUtils.isNotEmpty(fidArr[i]) && fidArr[i].endsWith("ogn") && fnameArr.length >= i) {
                return fnameArr[i];
            }
        }
        return StringUtils.EMPTY;
    }

    private String getDpt(String orgFname, String orgFid) {
        String[] fnameArr = orgFname.split("/");
        String[] fidArr = orgFid.split("/");
        for (int i = fidArr.length - 1; i >= 0; i--) {
            if (StringUtils.isNotEmpty(fidArr[i]) && fidArr[i].endsWith("dpt") && fnameArr.length >= i) {
                return fnameArr[i];
            }
        }
        return StringUtils.EMPTY;
    }

    @Override
    public CommerceContractExportResultResp importContract(MultipartFile file) throws IOException{
        CommerceContractExportResultResp exportResult = new CommerceContractExportResultResp();
        exportResult.setExportResult(Boolean.TRUE);
        exportResult.setErrorList(new ArrayList<>());

        OptUser curUser = ApiUtils.getUser(OptUser.class);
        log.info("curUser:{}", JsonUtils.toJson(curUser));
        String tenantName = curUser.getTenantName();

        List<CommerceContractExportErrorResp> errorList = new ArrayList<>();

        InputStream inputStream = file.getInputStream();
        //获取正确数据
        ArrayList<CommerceContractExcelReq> importContractList = new ArrayList<>();
        EasyExcel.read(inputStream)
                .head(CommerceContractExcelReq.class)
                .registerReadListener(new CommerceContractListener(
                        importContractList::addAll))
                .sheet()
                .headRowNumber(2)
                .doRead();
        log.info("importContractList size: {}", importContractList.size());

        if (CollectionUtils.isNotEmpty(importContractList)){
            int j = 3;
            if (StringUtils.isEmpty(tenantName)){
                CommerceContractExportErrorResp errorResp = new CommerceContractExportErrorResp();
                errorResp.setLineNo(j);
                errorResp.setErrorInfo("项目不可为空");
                errorList.add(errorResp);
            }

            // 获取项目列表
            EntProjectListDto projectListDto = new EntProjectListDto();
            List<EntProjectListVo> projectList = entProjectProvider.list(projectListDto);
            log.info("projectList:{}", projectList.size());
            Map<String, EntProjectListVo> projectMap = projectList.stream().collect(Collectors.toMap(EntProjectListVo::getName, a -> a, (k1, k2) -> k1));

            // 获取项目最新的合同编码递增后缀数量
            Long contractCodeNum = 0L;
            if (StringUtils.isNotEmpty(tenantName)){
                contractCodeNum = commerceContractProvider.getContractCodeNum(tenantName);
            }
            log.info("contractCodeNum:{}", contractCodeNum);

            // 获取店铺列表
            Map<String, RoomsListVo> roomMap = new HashMap<>();
            if (StringUtils.isNotEmpty(tenantName)){
                RoomsListDto roomsListDto = new RoomsListDto();
                roomsListDto.setTenantName(tenantName);
                List<RoomsListVo> roomList = roomsProvider.list(roomsListDto);
                log.info("roomList:{}", roomList.size());
                if (CollectionUtils.isEmpty(roomList)){
                    CommerceContractExportErrorResp errorResp = new CommerceContractExportErrorResp();
                    errorResp.setLineNo(0);
                    errorResp.setErrorInfo("项目店铺列表不可为空");
                    errorList.add(errorResp);
                }else {
                    roomMap = roomList.stream().collect(Collectors.toMap(RoomsListVo::getName, a -> a, (k1, k2) -> k2));
                }
            }

            // 获取店铺扩展列表
            Map<Long, RoomsExpandListDto> roomExpandMap = new HashMap<>();
            if (StringUtils.isNotEmpty(tenantName)){
                RoomsExpandListDto roomsExpandListDto = new RoomsExpandListDto();
                roomsExpandListDto.setTenantId(projectMap.get(tenantName).getId());
                List<RoomsExpandListDto> roomExpandlist = roomsExpandProvider.list(roomsExpandListDto);
                log.info("roomExpandlist:{}", roomExpandlist.size());
                if (CollectionUtils.isEmpty(roomExpandlist)){
                    CommerceContractExportErrorResp errorResp = new CommerceContractExportErrorResp();
                    errorResp.setLineNo(0);
                    errorResp.setErrorInfo("项目店铺扩展列表不可为空");
                    errorList.add(errorResp);
                }else {
                    roomExpandMap = roomExpandlist.stream().collect(Collectors.toMap(RoomsExpandListDto::getRoomId, a -> a, (k1, k2) -> k2));
                }
            }

            // 获取品牌列表
            CommerceBrandListDto brandListDto = new CommerceBrandListDto();
            brandListDto.setApproveStatus(3);
            List<CommerceBrandListVo> brandList = commerceBrandProvider.list(brandListDto);
            log.info("brandList:{}", brandList.size());
            Map<String, CommerceBrandListVo> brandMap = brandList.stream().collect(Collectors.toMap(CommerceBrandListVo::getName, a -> a, (k1, k2) -> k1));

            // 获取供应商列表
            CommerceSupplierListDto supplierListDto = new CommerceSupplierListDto();
            supplierListDto.setApproveStatus(3);
            List<CommerceSupplierListVo> supplierList = commerceSupplierProvider.list(supplierListDto);
            log.info("supplierList:{}", supplierList.size());
            Map<String, CommerceSupplierListVo> supplierMap = supplierList.stream().collect(Collectors.toMap(CommerceSupplierListVo::getName, a -> a, (k1, k2) -> k1));

            // 获取营运工单列表
            Map<String, CommerceOrderListVo> orderMap = new HashMap<>();
            if (StringUtils.isNotEmpty(tenantName)){
                CommerceOrderListDto orderDto = new CommerceOrderListDto();
                orderDto.setTenantName(tenantName);
                orderDto.setDiliveryStatus(0);
                orderDto.setExecuteStatus(0);
                List<CommerceOrderListVo> orderList = commerceOrderProvider.list(orderDto);
                log.info("orderList:{}", orderList.size());
                if (CollectionUtils.isNotEmpty(orderList)){
                    orderMap = orderList.stream().collect(Collectors.toMap(CommerceOrderListVo::getName, a -> a, (k1, k2) -> k1));
                }
            }

            // 获取审批通过的合同工单编号
            Map<String, CommerceContractListVo> contractDbMap = new HashMap<>();
            if (StringUtils.isNotEmpty(tenantName)){
                CommerceContractListDto contractListDto = new CommerceContractListDto();
                contractListDto.setTenantName(tenantName);
                contractListDto.setApproveStatus(3);
                List<CommerceContractListVo> contractList = commerceContractProvider.list(contractListDto);
                if (CollectionUtils.isNotEmpty(contractList)){
                    contractDbMap = contractList.stream().collect(Collectors.toMap(CommerceContractListVo::getName, a -> a, (k1, k2) -> k1));
                }
            }

            if (CollectionUtils.isNotEmpty(errorList)){
                exportResult.setExportResult(Boolean.FALSE);
                exportResult.setErrorList(errorList);
                return exportResult;
            }

            LocalDate nowLocalDate = LocalDate.now();
            LocalDateTime nowLocalDateTime = LocalDateTime.now();
            Map<String, Integer> duplicateMap=new HashMap<>();
            // 遍历入库
            List<CommerceContractImportExcelDto> resultList = new ArrayList<>();
            for (int i = 0; i < importContractList.size(); i++) {
                log.info("开始处理第 "+ (i+j) + " 行数据");
                CommerceContractExportErrorResp errorResp = new CommerceContractExportErrorResp();
                errorResp.setLineNo(i+j);
                StringBuffer errorBuffer = new StringBuffer();

                CommerceContractExcelReq contractExcelReq = importContractList.get(i);
                log.info("contractExcelReq:{}", JsonUtils.toJson(contractExcelReq));

                if (StringUtils.isEmpty(tenantName)){
                    errorBuffer.append("<br/>项目不可为空");
                }

                EntProjectListVo projectListVo = null;
                if (StringUtils.isNotEmpty(tenantName)){
                    projectListVo = projectMap.get(tenantName);
                    if (projectListVo == null){
                        errorBuffer.append("<br/>项目不存在");
                    }
                }

                String shopNo = contractExcelReq.getShopNo();
                if (StringUtils.isEmpty(shopNo)){
                    errorBuffer.append("<br/>铺位号（铺位信息）为必填字段，不可为空");
                }else {
                    if (duplicateMap.get(shopNo) != null) {
                        errorBuffer.append("<br/>铺位号（铺位信息）与第" + duplicateMap.get(shopNo) + "行铺位号（铺位信息）重复");
                    }
                    duplicateMap.put(shopNo,i+j);
                }

                RoomsListVo roomsListVo = null;
                if (StringUtils.isNotEmpty(shopNo)){
                    roomsListVo = roomMap.get(shopNo);
                    if (roomsListVo == null){
                        errorBuffer.append("<br/>铺位号（铺位信息）“"+shopNo+"”不存在");
                    }
                }

                RoomsExpandListDto expandListDto  = null;
                if (roomsListVo!=null){
                    expandListDto = roomExpandMap.get(roomsListVo.getId());
                    if (expandListDto == null){
                        errorBuffer.append("<br/>铺位号对应的扩展信息不存在");
                    }
                }

                String brandName = contractExcelReq.getBrandName();
                if (StringUtils.isEmpty(brandName)){
                    errorBuffer.append("<br/>签约品牌名称为必填字段，不可为空");
                }

                CommerceBrandListVo brandListVo = null;
                if (StringUtils.isNotEmpty(brandName)){
                    brandListVo = brandMap.get(brandName);
                    if (brandListVo == null){
                        errorBuffer.append("<br/>签约品牌名称（当事人信息）“"+brandName+"”不存在");
                    }
                }

                String supplierName = contractExcelReq.getSupplierName();
                if (StringUtils.isEmpty(supplierName)){
                    errorBuffer.append("<br/>供应商名称为必填字段，不可为空");
                }

                CommerceSupplierListVo supplierListVo = null;
                if (StringUtils.isNotEmpty(supplierName)){
                    supplierListVo = supplierMap.get(supplierName);
                    if (supplierListVo == null){
                        errorBuffer.append("<br/>供应商名称（品牌及供应商信息）“"+supplierName+"”不存在");
                    }
                }

                String contractCodeExcel = null;
                CommerceContractListVo contractDbVo = contractDbMap.get(shopNo);
                if (contractDbVo!=null){
                    // 没有计费截止日期才进行修改
                    CommerceContractInfoGetVo byContractCode = commerceContractInfoProvider.getByContractCode(contractDbVo.getContractCode());
                    if (byContractCode!=null && byContractCode.getActRetriveDate()==null){
                        contractCodeExcel = contractDbVo.getContractCode();
                    }
                }

                CommerceOrderListVo orderListVo = null;
                if (StringUtils.isEmpty(contractCodeExcel)){
                    if (StringUtils.isNotEmpty(shopNo)){
                        orderListVo = orderMap.get(shopNo);
                        if (orderListVo == null){
                            errorBuffer.append("<br/>营运工单不存在");
                        }
                    }
                }

                LocalDate rentStartDate = null;
                String rentStartDateExcel = contractExcelReq.getRentStartDate();
                try {
                    if (StringUtils.isEmpty(rentStartDateExcel)){
                        errorBuffer.append("<br/>租赁起始时间为必填字段，不可为空");
                    }else {
                        rentStartDate = LocalDate.parse(rentStartDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>租赁起始时间格式填写错误");
                }

                LocalDate rentEndDate = null;
                String rentEndDateExcel = contractExcelReq.getRentEndDate();
                try {
                    if (StringUtils.isEmpty(rentEndDateExcel)){
                        errorBuffer.append("<br/>租赁截止时间为必填字段，不可为空");
                    }else {
                        rentEndDate = LocalDate.parse(rentEndDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>租赁截止时间格式填写错误");
                }

                LocalDate deliveryDate = null;
                String deliveryDateExcel = contractExcelReq.getDeliveryDate();
                try {
                    if (StringUtils.isEmpty(deliveryDateExcel)){
                        errorBuffer.append("<br/>商户交付日期为必填字段，不可为空");
                    }else {
                        deliveryDate = LocalDate.parse(deliveryDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>商户交付日期格式填写错误");
                }

                LocalDate rentFeeDate = null;
                String rentFeeDateExcel = contractExcelReq.getRentFeeDate();
                try {
                    if (StringUtils.isEmpty(rentFeeDateExcel)){
                        errorBuffer.append("<br/>商户计租日期为必填字段，不可为空");
                    }else {
                        rentFeeDate = LocalDate.parse(rentFeeDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>商户计租日期格式填写错误");
                }

                LocalDate openDate = null;
                String openDateExcel = contractExcelReq.getOpenDate();
                try {
                    if (StringUtils.isEmpty(openDateExcel)){
                        errorBuffer.append("<br/>商户开业日期为必填字段，不可为空");
                    }else {
                        openDate = LocalDate.parse(openDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>商户开业日期格式填写错误");
                }

                // 日期校验
                if (rentStartDate!=null && rentEndDate!=null){
                    if (rentStartDate.isAfter(rentEndDate) || rentStartDate.equals(rentEndDate)){
                        errorBuffer.append("<br/>租赁起始时间不可大于等于租赁截止时间");
                    }
                }
                if (deliveryDate!=null && rentStartDate!=null){
                    if (deliveryDate.isAfter(rentStartDate)){
                        errorBuffer.append("<br/>商户交付日期不可大于租赁起始时间");
                    }
                }
                if (rentFeeDate!=null && rentStartDate!=null){
                    if (rentFeeDate.isBefore(rentStartDate)){
                        errorBuffer.append("<br/>商户计租日期不可小于租赁起始时间");
                    }
                }
                if (openDate!=null && rentStartDate!=null){
                    if (openDate.isBefore(rentStartDate)){
                        errorBuffer.append("<br/>商户开业日期不可小于租赁起始时间");
                    }
                }

                CommerceContractImportExcelDto importExcelDto = new CommerceContractImportExcelDto();
                if (StringUtils.isNotEmpty(contractCodeExcel)){
                    importExcelDto.setContractCode(contractCodeExcel);
                }

                log.info("赋值招商合同表-商铺");
                CommerceContractImportDto contractDto = new CommerceContractImportDto();
                contractDto.setDataType(1);

                if (projectListVo!=null){
                    contractDto.setTenantId(projectListVo.getId());
                    contractDto.setTenantName(projectListVo.getName());
                    contractDto.setEntId(projectListVo.getEntId());
                    contractDto.setOrgFid(projectListVo.getOrgFid());
                    contractDto.setOrgFname(projectListVo.getOrgFname());
                }

                if (orderListVo!=null){
                    contractDto.setOrderCode(orderListVo.getOrderCode());
                }

                String contractCode = null;
                if (StringUtils.isEmpty(contractCodeExcel)){
                    contractCode = SequenceCodeUtils.getContractCode(projectListVo.getCode(), contractCodeNum + i);
                    log.info("合同编号:{}", contractCode);
                    contractDto.setContractCode(contractCode);

                    contractDto.setApproveStatus(3);
                    contractDto.setApplyDate(nowLocalDate);
                    contractDto.setApproveDate(nowLocalDate);

                    contractDto.setExecuteUser(curUser.getUserName());
                    contractDto.setExecuteUserName(curUser.getRealName());
                    contractDto.setExecuteUserFid(curUser.getOrgFid());
                    contractDto.setExecuteUserFname(curUser.getOrgFname());

                    contractDto.setCreateBy(curUser.getUserId());
                    contractDto.setCreateUser(curUser.getUserName());
                    contractDto.setCreateUserName(curUser.getRealName());
                    contractDto.setCreateTime(nowLocalDateTime);
                }else {
                    contractDto.setContractCode(contractCodeExcel);
                }

                if (roomsListVo!=null){
                    contractDto.setFid(roomsListVo.getFid());
                    contractDto.setFcode(roomsListVo.getFcode());
                    contractDto.setFname(roomsListVo.getFname());
                    contractDto.setRoomId(roomsListVo.getId());
                    contractDto.setName(roomsListVo.getName());
                    contractDto.setRoomStatus(roomsListVo.getStatus());
                    if (rentStartDate!=null && nowLocalDate.isAfter(rentStartDate) && StringUtils.isEmpty(contractCodeExcel)){
                        contractDto.setRoomStatus(3);
                    }
                }

                if (expandListDto!=null){
                    contractDto.setActualArea(expandListDto.getActualArea());
                    contractDto.setRentArea(expandListDto.getRentArea());
                    contractDto.setElectricity(expandListDto.getElectricity());
                    contractDto.setWaterSupply(expandListDto.getWaterSupply());
                    contractDto.setDrainage(expandListDto.getDrainage());
                    contractDto.setExhaustFumes(expandListDto.getExhaustFumes());
                }

                if (brandListVo!=null){
                    contractDto.setBrandId(brandListVo.getId());
                    contractDto.setBrandName(brandListVo.getName());
                    contractDto.setCommercialTypeCode(brandListVo.getAdaptiveFormatId());
                    contractDto.setCommercialTypeName(brandListVo.getAdaptiveFormat());
                    contractDto.setCategoryId(brandListVo.getCategoryId());
                    contractDto.setCategoryName(brandListVo.getCategory());
                }

                // 生成门店id
                if (StringUtils.isEmpty(contractCodeExcel) && StringUtils.isNotEmpty(contractDto.getName()) && contractDto.getRoomId()!=null){
                    String roomShopId = commerceContractProvider.getShopId(contractDto.getName(), contractDto.getRoomId(), true);
                    log.info("门店id:{}", roomShopId);
                    contractDto.setRoomShopId(roomShopId);
                }

                importExcelDto.setContractImportDto(contractDto);

                log.info("赋值招商合同信息表");
                CommerceContractInfoImportDto contractInfoDto = new CommerceContractInfoImportDto();
                if (projectListVo!=null){
                    contractInfoDto.setTenantId(projectListVo.getId());
                    contractInfoDto.setTenantName(projectListVo.getName());
                    contractInfoDto.setEntId(projectListVo.getEntId());
                    contractInfoDto.setOrgFid(projectListVo.getOrgFid());
                    contractInfoDto.setOrgFname(projectListVo.getOrgFname());
                }

                if (StringUtils.isEmpty(contractCodeExcel)){
                    contractInfoDto.setContractCode(contractCode);
                    contractInfoDto.setContractType(1);

                    contractInfoDto.setCreateBy(curUser.getUserId());
                    contractInfoDto.setCreateUser(curUser.getUserName());
                    contractInfoDto.setCreateUserName(curUser.getRealName());
                    contractInfoDto.setCreateTime(nowLocalDateTime);
                }else {
                    contractInfoDto.setContractCode(contractCodeExcel);
                }

                if (brandListVo!=null){
                    contractInfoDto.setBrandId(brandListVo.getId());
                    contractInfoDto.setBrandName(brandListVo.getName());
                    contractInfoDto.setBrandCommercialTypeCode(brandListVo.getAdaptiveFormatId());
                    contractInfoDto.setBrandCommercialTypeName(brandListVo.getAdaptiveFormat());
                    contractInfoDto.setBrandCategoryId(brandListVo.getCategoryId());
                    contractInfoDto.setBrandCategoryName(brandListVo.getCategory());
                }

                String shopType = contractExcelReq.getShopType();
                if (StringUtils.isEmpty(shopType)){
                    errorBuffer.append("<br/>商铺类型为必填字段，不可为空");
                }else {
                    if (!shopType.equals("正铺") && !shopType.equals("临促")){
                        errorBuffer.append("<br/>商铺类型只存在“正铺、临促”类型");
                    }else {
                        if ("临促".equals(shopType)){
                            contractInfoDto.setShopType(1);
                        }else {
                            contractInfoDto.setShopType(0);
                        }
                    }
                }

                String lessor = contractExcelReq.getLessor();
                if (StringUtils.isEmpty(lessor)){
                    errorBuffer.append("<br/>甲方为必填字段，不可为空");
                }else {
                    contractInfoDto.setLessor(lessor);
                }

                String lessorRepresentative = contractExcelReq.getLessorRepresentative();
                String lessorAddress = contractExcelReq.getLessorAddress();
                String thirdParty = contractExcelReq.getThirdParty();

                contractInfoDto.setLessorRepresentative(lessorRepresentative);
                contractInfoDto.setLessorAddress(lessorAddress);
                contractInfoDto.setThirdParty(thirdParty);

                String operateIncreaseMode = contractExcelReq.getOperateIncreaseMode();
                if (StringUtils.isEmpty(operateIncreaseMode)){
                    contractInfoDto.setOperateIncreaseMode(2);
                }else {
                    if (!operateIncreaseMode.equals("比例递增") && !operateIncreaseMode.equals("金额递增") && !operateIncreaseMode.equals("不递增")){
                        errorBuffer.append("<br/>月运营管理费递增方式只存在“比例递增、金额递增、不递增”方式");
                    }else {
                        if (operateIncreaseMode.equals("比例递增")){
                            contractInfoDto.setOperateIncreaseMode(0);
                        }else if (operateIncreaseMode.equals("金额递增")){
                            contractInfoDto.setOperateIncreaseMode(1);
                        }else {
                            contractInfoDto.setOperateIncreaseMode(2);
                        }
                    }
                }
                Integer operateIncreaseModeIng = contractInfoDto.getOperateIncreaseMode();

                String manageIncreaseMode = contractExcelReq.getManageIncreaseMode();
                if (StringUtils.isEmpty(manageIncreaseMode)){
                    contractInfoDto.setManageIncreaseMode(2);
                }else {
                    if (!manageIncreaseMode.equals("比例递增") && !manageIncreaseMode.equals("金额递增") && !manageIncreaseMode.equals("不递增")){
                        errorBuffer.append("<br/>月物业管理费递增方式只存在“比例递增、金额递增、不递增”方式");
                    }else {
                        if (manageIncreaseMode.equals("比例递增")){
                            contractInfoDto.setManageIncreaseMode(0);
                        }else if (manageIncreaseMode.equals("金额递增")){
                            contractInfoDto.setManageIncreaseMode(1);
                        }else {
                            contractInfoDto.setManageIncreaseMode(2);
                        }
                    }
                }
                Integer manageIncreaseModeIng = contractInfoDto.getManageIncreaseMode();

                String businessScope = contractExcelReq.getBusinessScope();
                if (StringUtils.isEmpty(businessScope)){
                    errorBuffer.append("<br/>签约品牌经营范围为必填字段，不可为空");
                }else {
                    contractInfoDto.setBusinessScope(businessScope);
                }

                if (supplierListVo!=null){
                    contractInfoDto.setSupplierId(supplierListVo.getId());
                    contractInfoDto.setSupplierName(supplierListVo.getName());
                    contractInfoDto.setSupplierCertificateCode(supplierListVo.getCardNumber());
                    if (supplierListVo.getCertificateType()==2){
                        contractInfoDto.setSupplierCertificateCode(supplierListVo.getSocialCreditCode());
                    }
                    contractInfoDto.setSupplierCertificateAddress(supplierListVo.getCardAddress());
                    contractInfoDto.setContactName(supplierListVo.getContactName());
                    contractInfoDto.setContactPhon(supplierListVo.getContactPhon());
                    contractInfoDto.setSupplierQuality(supplierListVo.getQualityNum());
                    contractInfoDto.setSupplierSecurity(supplierListVo.getSafeNum());
                    contractInfoDto.setSupplierViolate(supplierListVo.getBreakNum());
                }

                String signEntity = contractExcelReq.getSignEntity();
                if (StringUtils.isEmpty(signEntity)){
                    errorBuffer.append("<br/>签约主体为必填字段，不可为空");
                }else {
                    if (!signEntity.equals("公司") && !signEntity.equals("个人")){
                        errorBuffer.append("<br/>签约主体只存在“公司、个人”主体");
                    }else {
                        if ("公司".equals(signEntity)){
                            contractInfoDto.setSignEntity(1);
                        }else {
                            contractInfoDto.setSignEntity(2);
                        }
                    }
                }

                String businessType = contractExcelReq.getBusinessType();
                if (StringUtils.isEmpty(businessType)){
                    errorBuffer.append("<br/>经营模式为必填字段，不可为空");
                }else {
                    if (!businessType.equals("品牌直营") && !businessType.equals("品牌代理") && !businessType.equals("品牌加盟") && !businessType.equals("个体经营")){
                        errorBuffer.append("<br/>经营模式只存在“品牌直营、品牌代理、品牌加盟、个体经营”模式");
                    }else {
                        if ("品牌直营".equals(businessType)){
                            contractInfoDto.setBusinessType(1);
                        }else if ("品牌代理".equals(businessType)){
                            contractInfoDto.setBusinessType(2);
                        } else if ("品牌加盟".equals(businessType)){
                            contractInfoDto.setBusinessType(3);
                        } else {
                            contractInfoDto.setBusinessType(4);
                        }
                    }
                }

                String decoration = contractExcelReq.getDecoration();
                if (StringUtils.isEmpty(decoration)){
                    errorBuffer.append("<br/>是否重新装修为必填字段，不可为空");
                }else {
                    if (!decoration.equals("是") && !decoration.equals("否")){
                        errorBuffer.append("<br/>是否重新装修只存在“是、否”");
                    }else {
                        if ("是".equals(decoration)){
                            contractInfoDto.setDecoration(1);
                        }else {
                            contractInfoDto.setDecoration(0);
                        }
                    }
                }

                String rentType = contractExcelReq.getRentType();
                if (StringUtils.isEmpty(rentType)){
                    errorBuffer.append("<br/>租赁类型为必填字段，不可为空");
                }else {
                    if (!rentType.equals("纯租") && !rentType.equals("纯扣") && !rentType.equals("保底扣（租金）") && !rentType.equals("保底扣（租金+管理费）")){
                        errorBuffer.append("<br/>租赁类型只存在“纯租、纯扣、保底扣（租金）、保底扣（租金+管理费）”类型");
                    }else {
                        if ("纯租".equals(rentType)){
                            contractInfoDto.setRentType(1);
                        } else if ("纯扣".equals(rentType)) {
                            contractInfoDto.setRentType(2);
                        } else if ("保底扣（租金）".equals(rentType)) {
                            contractInfoDto.setRentType(3);
                        }else {
                            contractInfoDto.setRentType(4);
                        }
                    }
                }

                contractInfoDto.setRentStartDate(rentStartDate);
                contractInfoDto.setRentEndDate(rentEndDate);
                contractInfoDto.setDeliveryDate(deliveryDate);
                contractInfoDto.setActDeliveryDate(deliveryDate);
                contractInfoDto.setRentFeeDate(rentFeeDate);
                contractInfoDto.setOpenDate(openDate);
                contractInfoDto.setActOpenDate(openDate);

                String rentIncreaseMode = contractExcelReq.getRentIncreaseMode();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()!=2){
                    if (StringUtils.isEmpty(rentIncreaseMode)){
                        errorBuffer.append("<br/>租赁类型不为纯扣，租金递增方式为必填字段");
                    }else {
                        if (!rentIncreaseMode.equals("固定比例") && !rentIncreaseMode.equals("固定金额")){
                            errorBuffer.append("<br/>租金递增方式只存在“固定比例、固定金额”方式");
                        }else {
                            if ("固定比例".equals(rentIncreaseMode)){
                                contractInfoDto.setRentIncreaseMode(0);
                            }else {
                                contractInfoDto.setRentIncreaseMode(1);
                            }
                        }
                    }
                }

                String pointsMode = contractExcelReq.getPointsMode();
                String settlementDate = contractExcelReq.getSettlementDate();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()!=1){
                    if (StringUtils.isEmpty(pointsMode)){
                        errorBuffer.append("<br/>租赁类型不为纯租，扣点方式为必填字段");
                    }else {
                        if (!pointsMode.equals("固定比例") && !pointsMode.equals("阶梯变化") && !pointsMode.equals("年份变化")){
                            errorBuffer.append("<br/>扣点方式只存在“固定比例、阶梯变化、年份变化”方式");
                        }else {
                            if ("固定比例".equals(pointsMode)){
                                contractInfoDto.setPointsMode(0);
                            }else if ("阶梯变化".equals(pointsMode)){
                                contractInfoDto.setPointsMode(1);
                            }else {
                                contractInfoDto.setPointsMode(2);
                            }
                        }
                    }
                    if (StringUtils.isEmpty(settlementDate)){
                        errorBuffer.append("<br/>租赁类型不为纯租，结算日期为必填字段");
                    }else {
                        if (!"月末".equals(settlementDate)){
                            try {
                                Integer settlementNum = Integer.valueOf(settlementDate.substring(0, settlementDate.length() - 1));
                                contractInfoDto.setSettlementDate(settlementNum);
                            }catch (Exception e){
                                errorBuffer.append("<br/>结算日期的值不存在于下拉框可选内容");
                            }
                        }else {
                            contractInfoDto.setSettlementDate(0);
                        }
                    }
                }

                importExcelDto.setContractInfoImportDto(contractInfoDto);

                log.info("赋值招商合同租赁信息表");
                CommerceContractRentImportDto rentDto = new CommerceContractRentImportDto();
                if (projectListVo!=null){
                    rentDto.setTenantId(projectListVo.getId());
                    rentDto.setTenantName(projectListVo.getName());
                    rentDto.setEntId(projectListVo.getEntId());
                    rentDto.setOrgFid(projectListVo.getOrgFid());
                    rentDto.setOrgFname(projectListVo.getOrgFname());
                }

                if (StringUtils.isEmpty(contractCodeExcel)){
                    rentDto.setContractCode(contractCode);

                    rentDto.setCreateBy(curUser.getUserId());
                    rentDto.setCreateUser(curUser.getUserName());
                    rentDto.setCreateUserName(curUser.getRealName());
                    rentDto.setCreateTime(nowLocalDateTime);
                }else {
                    rentDto.setContractCode(contractCodeExcel);
                }

                if (rentStartDate!=null && rentEndDate!=null){
                    Period period = Period.between(rentStartDate, rentEndDate.plusDays(1));
                    int years = period.getYears();
                    int months = period.getMonths();
                    int days = period.getDays();
                    StringBuffer yearLimitContract = new StringBuffer();
                    if (years>0){
                        yearLimitContract.append(years).append("年");
                    }
                    if (months>0){
                        yearLimitContract.append(months).append("月");
                    }
                    if (days>0){
                        yearLimitContract.append(days).append("天");
                    }
                    rentDto.setYearLimitContract(yearLimitContract.toString());
                    rentDto.setYearLimitMatch(1);
                }

                String monthPriceContract = contractExcelReq.getMonthPriceContract();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()==1){
                    if (StringUtils.isEmpty(monthPriceContract)){
                        errorBuffer.append("<br/>租赁类型=纯租，“月租金单价”为必填字段");
                    }else {
                        try {
                            BigDecimal monthPrice = new BigDecimal(monthPriceContract);
                            rentDto.setMonthPrice(monthPrice);
                            rentDto.setMonthPriceContract(monthPrice);
                            if (monthPrice.compareTo(BigDecimal.ZERO)<0){
                                errorBuffer.append("<br/>月租金单价不可小于0");
                            }
                        }catch (Exception e){
                            errorBuffer.append("<br/>月租金单价格式填写错误");
                        }
                    }
                }

                String monthFeeContract = contractExcelReq.getMonthFeeContract();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()!=2){
                    if (StringUtils.isEmpty(monthFeeContract)){
                        errorBuffer.append("<br/>租赁类型不为纯扣，“月租金总额”为必填字段");
                    }else {
                        try {
                            BigDecimal monthFeeTotal = new BigDecimal(monthFeeContract);
                            rentDto.setMonthFeeContract(monthFeeTotal);
                            if (monthFeeTotal.compareTo(BigDecimal.ZERO)<0){
                                errorBuffer.append("<br/>月租金总额不可小于0");
                            }
                        }catch (Exception e){
                            errorBuffer.append("<br/>月租金总额格式填写错误");
                        }
                    }
                }

                String incrementalRateList = contractExcelReq.getIncrementalRateList();
                String incrementalMoneyList = contractExcelReq.getIncrementalMoneyList();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()!=2){
                    Integer rentIncreaseModeDto = contractInfoDto.getRentIncreaseMode();
                    if (rentIncreaseModeDto!=null){
                        if (rentIncreaseModeDto==0){
                            if (StringUtils.isEmpty(incrementalRateList)){
                                errorBuffer.append("<br/>租赁类型不为纯扣，且租金递增方式=固定比例时，“租金年递增率”为必填字段");
                            }else {
                                try {
                                    if (incrementalRateList.contains("，")) {
                                        errorBuffer.append("<br/>租金年递增率填写格式错误（请使用英文输入法下的逗号,）");
                                    }
                                    if (!incrementalRateList.contains("[") || !incrementalRateList.contains("]")) {
                                        errorBuffer.append("<br/>租金年递增率填写格式错误（需要包含[]）");
                                    }
                                    if (incrementalRateList.contains("-")) {
                                        errorBuffer.append("<br/>租金年递增率填写格式错误（递增率不可小于0）");
                                    }
                                    incrementalRateList = convertRateStr(incrementalRateList);
                                    rentDto.setIncrementalRateList(incrementalRateList);
                                }catch (Exception e){
                                    errorBuffer.append("<br/>租金年递增率填写格式错误（未按示例填写）");
                                }
                            }
                        }else{
                            if (StringUtils.isEmpty(incrementalMoneyList)){
                                errorBuffer.append("<br/>租赁类型不为纯扣，且租金递增方式=固定金额时，“租金年递增（元）”为必填字段");
                            }else {
                                try {
                                    if (incrementalMoneyList.contains("，")) {
                                        errorBuffer.append("<br/>租金年递增（元）填写格式错误（请使用英文输入法下的逗号,）");
                                    }
                                    if (!incrementalMoneyList.contains("[") || !incrementalMoneyList.contains("]")) {
                                        errorBuffer.append("<br/>租金年递增（元）填写格式错误（需要包含[]）");
                                    }
                                    if (incrementalMoneyList.contains("-")) {
                                        errorBuffer.append("<br/>租金年递增（元）填写格式错误（递增金额不可小于0）");
                                    }
                                    incrementalMoneyList = convertRateStr(incrementalMoneyList);
                                    rentDto.setIncrementalRateList(incrementalMoneyList);
                                }catch (Exception e){
                                    errorBuffer.append("<br/>租金年递增（元）填写格式错误（未按示例填写）");
                                }
                            }
                        }
                    }
                }

                String operateIncrementalRateList = contractExcelReq.getOperateIncrementalRateList();
                String operateIncrementalMoneyList = contractExcelReq.getOperateIncrementalMoneyList();
                if (operateIncreaseModeIng!=null && operateIncreaseModeIng==0){
                    if (StringUtils.isEmpty(operateIncrementalRateList)) {
                        errorBuffer.append("<br/>月运营管理费递增方式=比例递增时，月运营管理费总额年递增率为必填字段");
                    } else {
                        try {
                            if (operateIncrementalRateList.contains("，")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增率填写格式错误（请使用英文输入法下的逗号,）");
                            }
                            if (!operateIncrementalRateList.contains("[") || !operateIncrementalRateList.contains("]")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增率填写格式错误（需要包含[]）");
                            }
                            if (operateIncrementalRateList.contains("-")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增率填写格式错误（递增率不可小于0）");
                            }
                            operateIncrementalRateList = convertRateStr(operateIncrementalRateList);
                            rentDto.setOperateIncrementalRateList(operateIncrementalRateList);
                        } catch (Exception e) {
                            errorBuffer.append("<br/>月运营管理费总额年递增率填写格式错误（未按示例填写）");
                        }
                    }
                }else if (operateIncreaseModeIng!=null && operateIncreaseModeIng==1){
                    if (StringUtils.isEmpty(operateIncrementalMoneyList)) {
                        errorBuffer.append("<br/>月运营管理费递增方式=金额递增时，月运营管理费总额年递增（元）为必填字段");
                    } else {
                        try {
                            if (operateIncrementalMoneyList.contains("，")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增（元）填写格式错误（请使用英文输入法下的逗号,）");
                            }
                            if (!operateIncrementalMoneyList.contains("[") || !operateIncrementalMoneyList.contains("]")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增（元）填写格式错误（需要包含[]）");
                            }
                            if (operateIncrementalMoneyList.contains("-")) {
                                errorBuffer.append("<br/>月运营管理费总额年递增（元）填写格式错误（递增金额不可小于0）");
                            }
                            operateIncrementalMoneyList = convertRateStr(operateIncrementalMoneyList);
                            rentDto.setOperateIncrementalRateList(operateIncrementalMoneyList);
                        } catch (Exception e) {
                            errorBuffer.append("<br/>月运营管理费总额年递增（元）填写格式错误（未按示例填写）");
                        }
                    }
                }

                String manageIncrementalRateList = contractExcelReq.getManageIncrementalRateList();
                String manageIncrementalMoneyList = contractExcelReq.getManageIncrementalMoneyList();
                if (manageIncreaseModeIng!=null && manageIncreaseModeIng==0){
                    if (StringUtils.isEmpty(manageIncrementalRateList)) {
                        errorBuffer.append("<br/>月物业管理费递增方式=比例递增时，月物业管理费总额年递增率为必填字段");
                    } else {
                        try {
                            if (manageIncrementalRateList.contains("，")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增率填写格式错误（请使用英文输入法下的逗号,）");
                            }
                            if (!manageIncrementalRateList.contains("[") || !manageIncrementalRateList.contains("]")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增率填写格式错误（需要包含[]）");
                            }
                            if (manageIncrementalRateList.contains("-")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增率填写格式错误（递增率不可小于0）");
                            }
                            manageIncrementalRateList = convertRateStr(manageIncrementalRateList);
                            rentDto.setManageIncrementalRateList(manageIncrementalRateList);
                        } catch (Exception e) {
                            errorBuffer.append("<br/>月物业管理费总额年递增率填写格式错误（未按示例填写）");
                        }
                    }
                }else if (manageIncreaseModeIng!=null && manageIncreaseModeIng==1){
                    if (StringUtils.isEmpty(manageIncrementalMoneyList)) {
                        errorBuffer.append("<br/>月物业管理费递增方式=金额递增时，月物业管理费总额年递增（元）为必填字段");
                    } else {
                        try {
                            if (manageIncrementalMoneyList.contains("，")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增（元）填写格式错误（请使用英文输入法下的逗号,）");
                            }
                            if (!manageIncrementalMoneyList.contains("[") || !manageIncrementalMoneyList.contains("]")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增（元）填写格式错误（需要包含[]）");
                            }
                            if (manageIncrementalMoneyList.contains("-")) {
                                errorBuffer.append("<br/>月物业管理费总额年递增（元）填写格式错误（递增金额不可小于0）");
                            }
                            manageIncrementalMoneyList = convertRateStr(manageIncrementalMoneyList);
                            rentDto.setManageIncrementalRateList(manageIncrementalMoneyList);
                        } catch (Exception e) {
                            errorBuffer.append("<br/>月物业管理费总额年递增（元）填写格式错误（未按示例填写）");
                        }
                    }
                }

                String fixedPercentList = contractExcelReq.getFixedPercentList();
                String ladChangePercentList = contractExcelReq.getLadChangePercentList();
                String yearPercentList = contractExcelReq.getYearPercentList();
                if (contractInfoDto.getRentType()!=null && contractInfoDto.getRentType()!=1){
                    Integer points = contractInfoDto.getPointsMode();
                    if (points!=null){
                        if (points==0){
                            if (StringUtils.isEmpty(fixedPercentList)){
                                errorBuffer.append("<br/>租赁类型不为纯租，且扣点方式=固定比例时，固定扣点为必填字段");
                            }else {
                                try {
                                    if (fixedPercentList.contains("-")) {
                                        errorBuffer.append("<br/>固定扣点不可小于0");
                                    }
                                    rentDto.setMonthFeePercent(Double.valueOf(fixedPercentList));
                                    StringBuffer sf = new StringBuffer();
                                    sf.append("[");
                                    sf.append("\"");
                                    sf.append(fixedPercentList);
                                    sf.append("\"");
                                    sf.append("]");
                                    rentDto.setMonthFeePercentList(sf.toString());
                                }catch (Exception e){
                                    errorBuffer.append("<br/>固定扣点填写格式错误（未按示例填写）");
                                }
                            }
                         } else if (points==1) {
                            if (StringUtils.isEmpty(ladChangePercentList)){
                                errorBuffer.append("<br/>租赁类型不为纯租，且扣点方式=阶梯变化时，阶梯变化扣点为必填字段");
                            }else {
                                try {
                                    if (ladChangePercentList.contains("，")) {
                                        errorBuffer.append("<br/>阶梯变化扣点填写格式错误（请使用英文输入法下的逗号,）");
                                    }
                                    if (!ladChangePercentList.contains("[") || !ladChangePercentList.contains("]")) {
                                        errorBuffer.append("<br/>阶梯变化扣点填写格式错误（需要包含[]）");
                                    }
                                    if (checkLadRate(ladChangePercentList)) {
                                        errorBuffer.append("<br/>阶梯变化扣点填写格式错误（扣点不可小于0）");
                                    }
                                    ladChangePercentList = convertRateStr(ladChangePercentList);
                                    rentDto.setMonthFeePercentList(ladChangePercentList);
                                }catch (Exception e){
                                    errorBuffer.append("<br/>阶梯变化扣点填写格式错误（未按示例填写）");
                                }
                            }
                        } else if (points==2) {
                            if (StringUtils.isEmpty(yearPercentList)){
                                errorBuffer.append("<br/>租赁类型不为纯租，且扣点方式=年份变化时，年份变化扣点为必填字段");
                            }else {
                                try {
                                    if (yearPercentList.contains("，")) {
                                        errorBuffer.append("<br/>年份变化扣点填写格式错误（请使用英文输入法下的逗号,）");
                                    }
                                    if (!yearPercentList.contains("[") || !yearPercentList.contains("]")) {
                                        errorBuffer.append("<br/>年份变化扣点填写格式错误（需要包含[]）");
                                    }
                                    if (yearPercentList.contains("-")) {
                                        errorBuffer.append("<br/>年份变化扣点填写格式错误（扣点不可小于0）");
                                    }
                                    yearPercentList = convertRateStr(yearPercentList);
                                    rentDto.setMonthFeePercentList(yearPercentList);
                                }catch (Exception e){
                                    errorBuffer.append("<br/>年份变化扣点填写格式错误（未按示例填写）");
                                }
                            }
                        }
                    }
                }

                BigDecimal monthManagePriceContract = null;
                String monthManagePriceContractExcel = contractExcelReq.getMonthManagePriceContract();
                try {
                    if (StringUtils.isEmpty(monthManagePriceContractExcel)){
                        errorBuffer.append("<br/>月物业管理费单价为必填字段，不可为空");
                    }else {
                        monthManagePriceContract = new BigDecimal(monthManagePriceContractExcel);
                        if (monthManagePriceContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>月物业管理费单价不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>月物业管理费单价格式填写错误");
                }
                rentDto.setMonthManagePrice(monthManagePriceContract);
                rentDto.setMonthManagePriceContract(monthManagePriceContract);
                rentDto.setMonthManagePriceMatch(1);

                BigDecimal monthManageFeeContract = null;
                String monthManageFeeContractExcel = contractExcelReq.getMonthManageFeeContract();
                try {
                    if (StringUtils.isEmpty(monthManageFeeContractExcel)){
                        errorBuffer.append("<br/>月物业管理费总额为必填字段，不可为空");
                    }else {
                        monthManageFeeContract = new BigDecimal(monthManageFeeContractExcel);
                        if (monthManageFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>月物业管理费总额不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>月物业管理费总额格式填写错误");
                }
                rentDto.setMonthManageFeeContract(monthManageFeeContract);

                BigDecimal monthOperatePriceContract = null;
                String monthOperatePriceContractExcel = contractExcelReq.getMonthOperatePriceContract();
                try {
                    if (StringUtils.isEmpty(monthOperatePriceContractExcel)){
                        errorBuffer.append("<br/>月运营管理费单价为必填字段，不可为空");
                    }else {
                        monthOperatePriceContract = new BigDecimal(monthOperatePriceContractExcel);
                        if (monthOperatePriceContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>月运营管理费单价不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>月运营管理费单价格式填写错误");
                }
                rentDto.setMonthOperatePrice(monthOperatePriceContract);
                rentDto.setMonthOperatePriceContract(monthOperatePriceContract);
                rentDto.setMonthOperatePriceMatch(1);

                BigDecimal monthOperateFeeContract = null;
                String monthOperateFeeContractExcel = contractExcelReq.getMonthOperateFeeContract();
                try {
                    if (StringUtils.isEmpty(monthOperateFeeContractExcel)){
                        errorBuffer.append("<br/>月运营管理费总额为必填字段，不可为空");
                    }else {
                        monthOperateFeeContract = new BigDecimal(monthOperateFeeContractExcel);
                        if (monthOperateFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>月运营管理费总额不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>月运营管理费总额格式填写错误");
                }
                rentDto.setMonthOperateFeeContract(monthOperateFeeContract);

                BigDecimal otherPriceContract = null;
                String otherPriceContractExcel = contractExcelReq.getOtherPriceContract();
                try {
                    if (StringUtils.isEmpty(otherPriceContractExcel)){
                        errorBuffer.append("<br/>其他费用单价为必填字段，不可为空");
                    }else {
                        otherPriceContract = new BigDecimal(otherPriceContractExcel);
                        if (otherPriceContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>其他费用单价不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>其他费用单价格式填写错误");
                }
                rentDto.setOtherPrice(otherPriceContract);
                rentDto.setOtherPriceContract(otherPriceContract);
                rentDto.setOtherPriceMatch(1);

                BigDecimal otherFeeContract = null;
                String otherFeeContractExcel = contractExcelReq.getOtherFeeContract();
                try {
                    if (StringUtils.isEmpty(otherFeeContractExcel)){
                        errorBuffer.append("<br/>其他费用总额为必填字段，不可为空");
                    }else {
                        otherFeeContract = new BigDecimal(otherFeeContractExcel);
                        if (otherFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>其他费用总额不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>其他费用总额格式填写错误");
                }
                rentDto.setOtherFeeContract(otherFeeContract);

                BigDecimal guarantSaleContract = BigDecimal.ZERO;
                String guarantSaleContractExcel = contractExcelReq.getGuarantSaleContract();
                try {
                    if (StringUtils.isNotEmpty(guarantSaleContractExcel)){
                        guarantSaleContract = new BigDecimal(guarantSaleContractExcel);
                        if (guarantSaleContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>保底销售额不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>保底销售额格式填写错误");
                }
                rentDto.setGuarantSaleContract(guarantSaleContract);

                rentDto.setIncrementalRate(BigDecimal.ZERO);
                rentDto.setIncrementalRateContract(BigDecimal.ZERO);
                rentDto.setIncrementalStartContract(BigDecimal.ZERO);
                rentDto.setIncrementalIntervalContract(BigDecimal.ZERO);
                rentDto.setIncrementalRateMatch(1);

                BigDecimal feeFreeContract = null;
                String feeFreeContractExcel = contractExcelReq.getFeeFreeContract();
                try {
                    if (StringUtils.isEmpty(feeFreeContractExcel)){
                        errorBuffer.append("<br/>装修免租期为必填字段，不可为空");
                    }else {
                        feeFreeContract = new BigDecimal(feeFreeContractExcel);
                        rentDto.setFeeFree(feeFreeContract.intValue());
                        rentDto.setFeeFreeContract(feeFreeContract.intValue());
                        rentDto.setFeeFreeMatch(1);
                        if (feeFreeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>装修免租期不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>装修免租期格式填写错误");
                }

                BigDecimal compositeManageFeeContract = null;
                String compositeManageFeeContractExcel = contractExcelReq.getCompositeManageFeeContract();
                try {
                    if (StringUtils.isEmpty(compositeManageFeeContractExcel)){
                        errorBuffer.append("<br/>综合管理费为必填字段，不可为空");
                    }else {
                        compositeManageFeeContract = new BigDecimal(compositeManageFeeContractExcel);
                        if (compositeManageFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>综合管理费不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>综合管理费格式填写错误");
                }
                rentDto.setCompositeManageFee(compositeManageFeeContract);
                rentDto.setCompositeManageFeeContract(compositeManageFeeContract);
                rentDto.setCompositeManageFeeMatch(1);

                BigDecimal rentBailFeeContract = null;
                String rentBailFeeContractExcel = contractExcelReq.getRentBailFeeContract();
                try {
                    if (StringUtils.isEmpty(rentBailFeeContractExcel)){
                        errorBuffer.append("<br/>租赁保证金为必填字段，不可为空");
                    }else {
                        rentBailFeeContract = new BigDecimal(rentBailFeeContractExcel);
                        if (rentBailFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>租赁保证金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>租赁保证金格式填写错误");
                }
                rentDto.setRentBailFee(rentBailFeeContract);
                rentDto.setRentBailFeeContract(rentBailFeeContract);
                rentDto.setRentBailFeeMatch(1);

                BigDecimal propertyManageBailFeeContract = null;
                String propertyManageBailFeeContractExcel = contractExcelReq.getPropertyManageBailFeeContract();
                try {
                    if (StringUtils.isEmpty(propertyManageBailFeeContractExcel)){
                        errorBuffer.append("<br/>物业管理费保证金为必填字段，不可为空");
                    }else {
                        propertyManageBailFeeContract = new BigDecimal(propertyManageBailFeeContractExcel);
                        if (propertyManageBailFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>物业管理费保证金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>物业管理费保证金格式填写错误");
                }
                rentDto.setPropertyManageBailFee(propertyManageBailFeeContract);
                rentDto.setPropertyManageBailFeeContract(propertyManageBailFeeContract);
                rentDto.setPropertyManageBailFeeMatch(1);

                BigDecimal operateManageBailFeeContract = null;
                String operateManageBailFeeContractExcel = contractExcelReq.getOperateManageBailFeeContract();
                try {
                    if (StringUtils.isEmpty(operateManageBailFeeContractExcel)){
                        errorBuffer.append("<br/>运营管理费保证金为必填字段，不可为空");
                    }else {
                        operateManageBailFeeContract = new BigDecimal(operateManageBailFeeContractExcel);
                        if (operateManageBailFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>运营管理费保证金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>运营管理费保证金格式填写错误");
                }
                rentDto.setOperateManageBailFee(operateManageBailFeeContract);
                rentDto.setOperateManageBailFeeContract(operateManageBailFeeContract);
                rentDto.setOperateManageBailFeeMatch(1);

                BigDecimal commonBailFeeContract = null;
                String commonBailFeeContractExcel = contractExcelReq.getCommonBailFeeContract();
                try {
                    if (StringUtils.isEmpty(commonBailFeeContractExcel)){
                        errorBuffer.append("<br/>公共事业保证金为必填字段，不可为空");
                    }else {
                        commonBailFeeContract = new BigDecimal(commonBailFeeContractExcel);
                        if (commonBailFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>公共事业保证金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>公共事业保证金格式填写错误");
                }
                rentDto.setCommonBailFee(commonBailFeeContract);
                rentDto.setCommonBailFeeContract(commonBailFeeContract);
                rentDto.setCommonBailFeeMatch(1);

                BigDecimal decorationBailFeeContract = null;
                String decorationBailFeeContractExcel = contractExcelReq.getDecorationBailFeeContract();
                try {
                    if (StringUtils.isEmpty(decorationBailFeeContractExcel)){
                        errorBuffer.append("<br/>装修押金为必填字段，不可为空");
                    }else {
                        decorationBailFeeContract = new BigDecimal(decorationBailFeeContractExcel);
                        if (decorationBailFeeContract.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>装修押金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>装修押金格式填写错误");
                }
                rentDto.setDecorationBailFee(decorationBailFeeContract);
                rentDto.setDecorationBailFeeContract(decorationBailFeeContract);
                rentDto.setDecorationBailFeeMatch(1);

                BigDecimal posRentFee = null;
                String posRentFeeExcel = contractExcelReq.getPosRentFee();
                try {
                    if (StringUtils.isEmpty(posRentFeeExcel)){
                        errorBuffer.append("<br/>POS机租金为必填字段，不可为空");
                    }else {
                        posRentFee = new BigDecimal(posRentFeeExcel);
                        if (posRentFee.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>POS机租金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>POS机租金格式填写错误");
                }
                rentDto.setPosRentFee(posRentFee);

                BigDecimal posBailFee = null;
                String posBailFeeExcel = contractExcelReq.getPosBailFee();
                try {
                    if (StringUtils.isEmpty(posBailFeeExcel)){
                        errorBuffer.append("<br/>POS机押金为必填字段，不可为空");
                    }else {
                        posBailFee = new BigDecimal(posBailFeeExcel);
                        if (posBailFee.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>POS机押金不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>POS机押金格式填写错误");
                }
                rentDto.setPosBailFee(posBailFee);

                BigDecimal decorationPropertyFee = null;
                String decorationPropertyFeeExcel = contractExcelReq.getDecorationPropertyFee();
                try {
                    if (StringUtils.isEmpty(decorationPropertyFeeExcel)){
                        errorBuffer.append("<br/>装修期物业管理费为必填字段，不可为空");
                    }else {
                        decorationPropertyFee = new BigDecimal(decorationPropertyFeeExcel);
                        if (decorationPropertyFee.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>装修期物业管理费不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>装修期物业管理费格式填写错误");
                }
                rentDto.setDecorationPropertyFee(decorationPropertyFee);

                BigDecimal decorationOperateFee = null;
                String decorationOperateFeeExcel = contractExcelReq.getDecorationOperateFee();
                try {
                    if (StringUtils.isEmpty(decorationOperateFeeExcel)){
                        errorBuffer.append("<br/>装修期运营管理费为必填字段，不可为空");
                    }else {
                        decorationOperateFee = new BigDecimal(decorationOperateFeeExcel);
                        if (decorationOperateFee.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>装修期运营管理费不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>装修期运营管理费格式填写错误");
                }
                rentDto.setDecorationOperateFee(decorationOperateFee);

                BigDecimal cleanFee = null;
                String cleanFeeExcel = contractExcelReq.getCleanFee();
                try {
                    if (StringUtils.isEmpty(cleanFeeExcel)){
                        errorBuffer.append("<br/>垃圾清运费为必填字段，不可为空");
                    }else {
                        cleanFee = new BigDecimal(cleanFeeExcel);
                        if (cleanFee.compareTo(BigDecimal.ZERO) < 0) {
                            errorBuffer.append("<br/>垃圾清运费不可小于0");
                        }
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>垃圾清运费格式填写错误");
                }
                rentDto.setCleanFee(cleanFee);

                importExcelDto.setContractRentImportDto(rentDto);

                resultList.add(importExcelDto);

                if (errorBuffer.length()>5){
                    errorResp.setErrorInfo(errorBuffer.toString().substring(5, errorBuffer.length()));
                    errorList.add(errorResp);
                }
            }
            // 入库
            if (CollectionUtils.isNotEmpty(resultList) && CollectionUtils.isEmpty(errorList)){
                log.info("resultList:{}", resultList.size());
                LocalDateTime rkTime = LocalDateTime.now();
                int a = 0;
                for (CommerceContractImportExcelDto importExcelDto : resultList) {
                    LocalDateTime rkAddTime = rkTime.plusSeconds(a++);
                    importExcelDto.setCreateTime(rkAddTime);
                    commerceContractProvider.saveImportExcel(importExcelDto);

                    //更新铺位信息
                    CommerceContractImportDto contractDto = importExcelDto.getContractImportDto();
                    CommerceContractInfoImportDto contractInfoDto = importExcelDto.getContractInfoImportDto();
                    log.info("更新铺位信息");
                    RoomsEditDto roomsEditDto = new RoomsEditDto();
                    roomsEditDto.setId(contractDto.getRoomId());
                    roomsEditDto.setBrandId(contractDto.getBrandId());
                    roomsEditDto.setBrandName(contractDto.getBrandName());
                    roomsEditDto.setSupplierId(contractInfoDto.getSupplierId());
                    roomsEditDto.setSupplierName(contractInfoDto.getSupplierName());

                    // 首次初始化且租赁起始时间小于当前时间修改铺位状态为为已签约
                    String contractCode = importExcelDto.getContractCode();
                    if (StringUtils.isEmpty(contractCode)){
                        if (nowLocalDate.isAfter(contractInfoDto.getRentStartDate())){
                            roomsEditDto.setStatus(3);
                            roomsEditDto.setContractCode(contractDto.getContractCode());
                            roomsEditDto.setStoreId(contractDto.getRoomShopId());
                        }
                    }

                    roomsProvider.edit(roomsEditDto);
                }
            }else {
                exportResult.setExportResult(Boolean.FALSE);
                exportResult.setErrorList(errorList);
            }
        }
        return exportResult;
    }

    @Override
    public CommerceContractExportResultResp openDelivery(MultipartFile file) throws IOException{
        CommerceContractExportResultResp exportResult = new CommerceContractExportResultResp();
        exportResult.setExportResult(Boolean.TRUE);
        exportResult.setErrorList(new ArrayList<>());

        OptUser curUser = ApiUtils.getUser(OptUser.class);
        log.info("curUser:{}", JsonUtils.toJson(curUser));
        String tenantName = curUser.getTenantName();

        InputStream inputStream = file.getInputStream();
        //获取正确数据
        ArrayList<CommerceOpenDeliveryExcelReq> importOpenDeliveryList = new ArrayList<>();
        EasyExcel.read(inputStream)
                .head(CommerceOpenDeliveryExcelReq.class)
                .registerReadListener(new CommerceOpenDeliveryListener(
                        importOpenDeliveryList::addAll))
                .sheet()
                .headRowNumber(2)
                .doRead();
        log.info("importOpenDeliveryList size: {}", importOpenDeliveryList.size());

        List<CommerceContractExportErrorResp> errorList = new ArrayList<>();

        List<CommerceEntexitEditDto> entexitEditDtoList = new ArrayList<>();
        List<CommerceContractInfoEditDto> contractInfoEditDtoList = new ArrayList<>();
        List<RoomsEditDto> roomsEditDtoList = new ArrayList<>();
        List<CommercePlanDetailPresentEditDto> planEditDtoList = new ArrayList<>();

        LocalDate nowLocalDate = LocalDate.now();
        if (CollectionUtils.isNotEmpty(importOpenDeliveryList)){
            Map<String, Integer> duplicateMap=new HashMap<>();
            int j = 3;
            for (int i = 0; i < importOpenDeliveryList.size(); i++) {
                log.info("开始处理第 "+ (i+j) + " 行数据");
                CommerceOpenDeliveryExcelReq openDeliveryExcelReq = importOpenDeliveryList.get(i);
                log.info("openDeliveryExcelReq:{}", JsonUtils.toJson(openDeliveryExcelReq));

                CommerceContractExportErrorResp errorResp = new CommerceContractExportErrorResp();
                errorResp.setLineNo(i+j);
                StringBuffer errorBuffer = new StringBuffer();

                String shopNo = openDeliveryExcelReq.getShopNo();
                if (StringUtils.isEmpty(shopNo)){
                    errorBuffer.append("<br/>铺位号为必填字段，不可为空");
                }else {
                    if (duplicateMap.get(shopNo) != null) {
                        errorBuffer.append("<br/>铺位号（铺位信息）与第" + duplicateMap.get(shopNo) + "行铺位号（铺位信息）重复");
                    }
                    duplicateMap.put(shopNo,i+j);
                }

                String managerName = openDeliveryExcelReq.getManagerName();
                if (StringUtils.isEmpty(managerName)){
                    errorBuffer.append("<br/>门店负责人为必填字段，不可为空");
                }

                String managerContactPhone = openDeliveryExcelReq.getManagerContactPhone();
                if (StringUtils.isEmpty(managerContactPhone)){
                    errorBuffer.append("<br/>门店负责人联系电话为必填字段，不可为空");
                }else {
                    if (managerContactPhone.length()!=11){
                        errorBuffer.append("<br/>门店负责人联系电话格式错误，需要为11位数字");
                    }
                }

                LocalDate realityDeliveryDate = null;
                String realityDeliveryDateExcel = openDeliveryExcelReq.getRealityDeliveryDate();
                try {
                    if (StringUtils.isEmpty(realityDeliveryDateExcel)){
                        errorBuffer.append("<br/>实际交付日期为必填字段，不可为空");
                    }else {
                        realityDeliveryDate = LocalDate.parse(realityDeliveryDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>实际交付日期格式填写错误");
                }

                LocalDate realityOpenDate = null;
                String realityOpenDateExcel = openDeliveryExcelReq.getRealityOpenDate();
                try {
                    if (StringUtils.isEmpty(realityOpenDateExcel)){
                        errorBuffer.append("<br/>实际开业日期为必填字段，不可为空");
                    }else {
                        realityOpenDate = LocalDate.parse(realityOpenDateExcel);
                    }
                }catch (Exception e){
                    errorBuffer.append("<br/>实际开业日期格式填写错误");
                }

                if (realityDeliveryDate!=null && realityOpenDate!=null){
                    if (realityDeliveryDate.isAfter(realityOpenDate)){
                        errorBuffer.append("<br/>实际交付日期不可大于实际开业时间");
                    }
                }

                Integer employeeCount = null;
                String employeeCountExcel = openDeliveryExcelReq.getEmployeeCount();
                if (StringUtils.isEmpty(employeeCountExcel)){
                    errorBuffer.append("<br/>门店员工数量为必填字段，不可为空");
                }else {
                    try {
                        employeeCount = Integer.valueOf(employeeCountExcel);
                        if (employeeCount < 0) {
                            errorBuffer.append("<br/>门店员工数量不可小于0");
                        }
                    }catch (Exception e){
                        errorBuffer.append("<br/>门店员工数量格式填写错误");
                    }
                }

                CommerceEntexitEditDto commerceEntexitEditDto = new CommerceEntexitEditDto();

                String hasBusinessLicense = openDeliveryExcelReq.getHasBusinessLicense();
                if (StringUtils.isEmpty(hasBusinessLicense)) {
                    errorBuffer.append("<br/>是否有营业执照为必填字段，不可为空");
                } else {
                    if (!hasBusinessLicense.equals("是") && !hasBusinessLicense.equals("否")) {
                        errorBuffer.append("<br/>是否有营业执照只存在“是、否”");
                    } else {
                        if ("是".equals(hasBusinessLicense)) {
                            commerceEntexitEditDto.setHasBusinessLicense(1);
                        } else {
                            commerceEntexitEditDto.setHasBusinessLicense(0);
                        }
                    }
                }

                String hasFoodPermit = openDeliveryExcelReq.getHasFoodPermit();
                if (StringUtils.isEmpty(hasFoodPermit)) {
                    errorBuffer.append("<br/>是否有食品经营许可证为必填字段，不可为空");
                } else {
                    if (!hasFoodPermit.equals("是") && !hasFoodPermit.equals("否")) {
                        errorBuffer.append("<br/>是否有食品经营许可证只存在“是、否”");
                    } else {
                        if ("是".equals(hasFoodPermit)) {
                            commerceEntexitEditDto.setHasFoodPermit(1);
                        } else {
                            commerceEntexitEditDto.setHasFoodPermit(0);
                        }
                    }
                }

                String hasFireReceipt = openDeliveryExcelReq.getHasFireReceipt();
                if (StringUtils.isEmpty(hasFireReceipt)) {
                    errorBuffer.append("<br/>是否有消防申报回执为必填字段，不可为空");
                } else {
                    if (!hasFireReceipt.equals("是") && !hasFireReceipt.equals("否")) {
                        errorBuffer.append("<br/>是否有消防申报回执只存在“是、否”");
                    } else {
                        if ("是".equals(hasFireReceipt)) {
                            commerceEntexitEditDto.setHasFireReceipt(1);
                        } else {
                            commerceEntexitEditDto.setHasFireReceipt(0);
                        }
                    }
                }

                String hasFireReport = openDeliveryExcelReq.getHasFireReport();
                if (StringUtils.isEmpty(hasFireReport)) {
                    errorBuffer.append("<br/>是否有消防验收意见书为必填字段，不可为空");
                } else {
                    if (!hasFireReport.equals("是") && !hasFireReport.equals("否")) {
                        errorBuffer.append("<br/>是否有消防验收意见书只存在“是、否”");
                    } else {
                        if ("是".equals(hasFireReport)) {
                            commerceEntexitEditDto.setHasFireReport(1);
                        } else {
                            commerceEntexitEditDto.setHasFireReport(0);
                        }
                    }
                }

                String hasStoreClean = openDeliveryExcelReq.getHasStoreClean();
                if (StringUtils.isEmpty(hasStoreClean)) {
                    errorBuffer.append("<br/>店铺卫生是否整洁为必填字段，不可为空");
                } else {
                    if (!hasStoreClean.equals("是") && !hasStoreClean.equals("否")) {
                        errorBuffer.append("<br/>店铺卫生是否整洁只存在“是、否”");
                    } else {
                        if ("是".equals(hasStoreClean)) {
                            commerceEntexitEditDto.setHasStoreClean(1);
                        } else {
                            commerceEntexitEditDto.setHasStoreClean(0);
                        }
                    }
                }

                String hasStoreTidy = openDeliveryExcelReq.getHasStoreTidy();
                if (StringUtils.isEmpty(hasStoreTidy)) {
                    errorBuffer.append("<br/>店铺陈列是否整齐为必填字段，不可为空");
                } else {
                    if (!hasStoreTidy.equals("是") && !hasStoreTidy.equals("否")) {
                        errorBuffer.append("<br/>店铺陈列是否整齐只存在“是、否”");
                    } else {
                        if ("是".equals(hasStoreTidy)) {
                            commerceEntexitEditDto.setHasStoreTidy(1);
                        } else {
                            commerceEntexitEditDto.setHasStoreTidy(0);
                        }
                    }
                }

                String hasStoreCheck = openDeliveryExcelReq.getHasStoreCheck();
                if (StringUtils.isEmpty(hasStoreCheck)) {
                    errorBuffer.append("<br/>是否有商铺装修验收表为必填字段，不可为空");
                } else {
                    if (!hasStoreCheck.equals("是") && !hasStoreCheck.equals("否")) {
                        errorBuffer.append("<br/>是否有商铺装修验收表只存在“是、否”");
                    } else {
                        if ("是".equals(hasStoreCheck)) {
                            commerceEntexitEditDto.setHasStoreCheck(1);
                        } else {
                            commerceEntexitEditDto.setHasStoreCheck(0);
                        }
                    }
                }

                String hasPersonnelArchived = openDeliveryExcelReq.getHasPersonnelArchived();
                if (StringUtils.isEmpty(hasPersonnelArchived)) {
                    errorBuffer.append("<br/>是否有商铺装修验收表为必填字段，不可为空");
                } else {
                    if (!hasPersonnelArchived.equals("是") && !hasPersonnelArchived.equals("否")) {
                        errorBuffer.append("<br/>是否有商铺装修验收表只存在“是、否”");
                    } else {
                        if ("是".equals(hasPersonnelArchived)) {
                            commerceEntexitEditDto.setHasPersonnelArchived(1);
                        } else {
                            commerceEntexitEditDto.setHasPersonnelArchived(0);
                        }
                    }
                }

                RoomsListDto roomsListDto = new RoomsListDto();
                roomsListDto.setTenantName(tenantName);
                roomsListDto.setName(shopNo);
                RoomsGetVo roomsGetVo = roomsProvider.get(roomsListDto);
                if (roomsGetVo==null || StringUtils.isEmpty(roomsGetVo.getContractCode())){
                    errorBuffer.append("<br/>铺位还未到租赁开始时间进场");
                }

                // 更新进撤场数据
                CommerceEntexitListDto entexitListDto = new CommerceEntexitListDto();
                entexitListDto.setTenantName(tenantName);
                entexitListDto.setRoomNo(shopNo);
                entexitListDto.setContractCode(roomsGetVo.getContractCode());
                CommerceEntexitGetVo entexitGetVo = commerceEntexitProvider.getLastEntexitByTime(entexitListDto);
                if (entexitGetVo==null){
                    errorBuffer.append("<br/>铺位还未到租赁开始时间进场");
                }else {
                    commerceEntexitEditDto.setId(entexitGetVo.getId());

                    if (realityDeliveryDate != null) {
                        if (realityDeliveryDate.isBefore(nowLocalDate) || realityDeliveryDate.isEqual(nowLocalDate)){
                            if (!"open".equals(entexitGetVo.getRoomStatus())){
                                 commerceEntexitEditDto.setRoomStatus("no_open");
                            }
                            commerceEntexitEditDto.setRealityDeliveryDate(realityDeliveryDate);
                            commerceEntexitEditDto.setDeliveredUserName(curUser.getRealName());
                            commerceEntexitEditDto.setOperateDeliveryDate(nowLocalDate);
                        }
                    }

                    if (realityOpenDate != null) {
                        if (realityOpenDate.isBefore(nowLocalDate) || realityOpenDate.isEqual(nowLocalDate)){
                            commerceEntexitEditDto.setRoomStatus("open");
                            commerceEntexitEditDto.setRealityOpenDate(realityOpenDate);
                        }
                    }

                    if (entexitGetVo.getOperateStatus()==null){
                        commerceEntexitEditDto.setOperateStatus("normal");
                    }
                    // 赋值开业信息
                    commerceEntexitEditDto.setManagerName(managerName);
                    commerceEntexitEditDto.setManagerContactPhone(managerContactPhone);
                    commerceEntexitEditDto.setEmployeeCount(employeeCount);
                    commerceEntexitEditDto.setDataType(1);
                    entexitEditDtoList.add(commerceEntexitEditDto);
                }

                // 交付处理
                if (entexitGetVo!=null){
                    log.info("合同编码:{}", entexitGetVo.getContractCode());
                    CommerceContractInfoGetVo byContractCode = commerceContractInfoProvider.getByContractCode(entexitGetVo.getContractCode());
                    if (byContractCode==null){
                        errorBuffer.append("<br/>第 "+ (i+j) + " 行找不到对应的合同信息");
                    }else {
                        log.info("更新合同信息");
                        CommerceContractInfoEditDto editDto = new CommerceContractInfoEditDto();
                        editDto.setId(byContractCode.getId());

                        if (realityDeliveryDate != null) {
                            if (realityDeliveryDate.isBefore(nowLocalDate) || realityDeliveryDate.isEqual(nowLocalDate)) {
                                editDto.setActDeliveryDate(realityDeliveryDate);
                            }
                        }

                        if (realityOpenDate != null) {
                            if (realityOpenDate.isBefore(nowLocalDate) || realityOpenDate.isEqual(nowLocalDate)) {
                                editDto.setActOpenDate(realityOpenDate);
                            }
                        }

                        editDto.setUpdateTime(LocalDateTime.now());
                        contractInfoEditDtoList.add(editDto);
                    }
                }

                // 开业处理
                if (realityOpenDate != null) {
                    if (realityOpenDate.isBefore(nowLocalDate) || realityOpenDate.isEqual(nowLocalDate)) {
                        if (entexitGetVo != null) {
                            log.info("确定开业变更门店全周期管理铺位状态");
                            RoomsEditDto roomsEditDto = new RoomsEditDto();
                            roomsEditDto.setId(entexitGetVo.getShopId());
                            roomsEditDto.setStatus(2);
                            if (entexitGetVo.getOperateStatus() == null) {
                                roomsEditDto.setOperateStatus(1);
                            }
                            roomsEditDto.setOpeningDate(realityOpenDate);
                            roomsEditDtoList.add(roomsEditDto);

                            CommercePlanDetailPresentListDto planDetailPresentListDto = new CommercePlanDetailPresentListDto();
                            planDetailPresentListDto.setRoomId(entexitGetVo.getShopId());
                            CommercePlanDetailPresentGetVo commercePlanDetailPresentGetVo = commercePlanDetailPresentProvider.get(planDetailPresentListDto);
                            if (commercePlanDetailPresentGetVo != null) {
                                CommercePlanDetailPresentEditDto planDetailPresentEditDto = new CommercePlanDetailPresentEditDto();
                                planDetailPresentEditDto.setId(commercePlanDetailPresentGetVo.getId());
                                planDetailPresentEditDto.setRoomStatus(2);
                                planEditDtoList.add(planDetailPresentEditDto);
                            }
                        }
                    }
                }

                if (errorBuffer.length()>5){
                    errorResp.setErrorInfo(errorBuffer.toString().substring(5, errorBuffer.length()));
                    errorList.add(errorResp);
                }
            }
            // 修改
            if (CollectionUtils.isEmpty(errorList)){
                if (CollectionUtils.isNotEmpty(entexitEditDtoList)){
                    for (CommerceEntexitEditDto entexitEditDto : entexitEditDtoList) {
                        commerceEntexitProvider.edit(entexitEditDto);
                    }
                }
                if (CollectionUtils.isNotEmpty(contractInfoEditDtoList)){
                    for (CommerceContractInfoEditDto contractInfoEditDto : contractInfoEditDtoList) {
                        commerceContractInfoProvider.edit(contractInfoEditDto);
                    }
                }
                if (CollectionUtils.isNotEmpty(roomsEditDtoList)){
                    for (RoomsEditDto roomsEditDto : roomsEditDtoList) {
                        roomsProvider.edit(roomsEditDto);
                    }
                }
                if (CollectionUtils.isNotEmpty(planEditDtoList)){
                    for (CommercePlanDetailPresentEditDto planEditDto : planEditDtoList) {
                        commercePlanDetailPresentProvider.edit(planEditDto);
                    }
                }
            }else {
                exportResult.setExportResult(Boolean.FALSE);
                exportResult.setErrorList(errorList);
            }
        }
        return exportResult;
    }

    @Override
    public void correct(CommerceContractEditReq editReq) {
        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        // 记录合同修改日志
        CommerceContractLogAddDto logAddDto = new CommerceContractLogAddDto();
        // 获取修改前的数据
        CommerceContractGetReq getReq = new CommerceContractGetReq();
        getReq.setId(editReq.getId());
        CommerceContractGetResp getResp = get(getReq);
        if (getResp!=null){
            logAddDto.setBeforeContractContent(JsonUtils.toJson(getResp));
        }
        // 修正数据
        CommerceContractEditDto dto = BeanUtils.copyProperties(editReq, CommerceContractEditDto.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        CommerceContractRentAddDto rentAddDto = dto.getContractRent();
        if (CollectionUtils.isNotEmpty(editReq.getContractRent().getIncrementalRateList())) {
            rentAddDto.setIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getIncrementalRateList()));
        }
        if (CollectionUtils.isNotEmpty(editReq.getContractRent().getMonthFeePercentList())) {
            rentAddDto.setMonthFeePercentList(JSON.toJSONString(editReq.getContractRent().getMonthFeePercentList()));
        }
        if (ObjectUtils.isNotEmpty(editReq.getContractRent().getOperateIncrementalRateList())) {
            rentAddDto.setOperateIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getOperateIncrementalRateList()));
        }
        if (ObjectUtils.isNotEmpty(editReq.getContractRent().getManageIncrementalRateList())) {
            rentAddDto.setManageIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getManageIncrementalRateList()));
        }
        dto.setContractRent(rentAddDto);
        CommerceContractGetVo contractGetVo = commerceContractProvider.correct(dto);

        // 修改门店品牌和供应商
        RoomsEditDto roomsEditDto = new RoomsEditDto();
        log.info("roomId:{}", contractGetVo.getRoomId());
        roomsEditDto.setId(contractGetVo.getRoomId());
        roomsEditDto.setUpdateTime(LocalDateTime.now());

        LocalDate now = LocalDate.now();
        LocalDate rentStartDate = dto.getContractInfo().getRentStartDate();
        LocalDate rentEndDate = dto.getContractInfo().getRentEndDate();
        log.info("rentStartDate:{}", rentStartDate);
        log.info("rentEndDate:{}", rentEndDate);

        // 合同签约类型
        Integer contractType = editReq.getContractInfo().getContractType();
        log.info("contractType:{}", contractType);

        if (rentStartDate.isBefore(now) || rentStartDate.isEqual(now)) {
            if (rentEndDate.isAfter(now) || rentEndDate.isEqual(now)) {
                log.info("符合条件:{}", dto.getContractInfo().getContractCode());
                roomsEditDto.setBrandId(dto.getBrandId());
                roomsEditDto.setBrandName(dto.getBrandName());
                roomsEditDto.setSupplierId(dto.getContractInfo().getSupplierId());
                roomsEditDto.setSupplierName(dto.getContractInfo().getSupplierName());
                // 续签
                if (contractType != null && contractType == 2) {
                    log.info("续签修正...");
                    roomsEditDto.setContractCode(dto.getContractInfo().getContractCode());
                }
            }
        }

        roomsProvider.edit(roomsEditDto);

        // 修改合同面积
        RoomsExpandListDto roomsExpandListDto = new RoomsExpandListDto();
        roomsExpandListDto.setRoomId(contractGetVo.getRoomId());
        RoomsExpandGetVo roomsExpandGetVo = roomsExpandProvider.get(roomsExpandListDto);
        if (roomsExpandGetVo!=null){
            CommerceContractEditDto contractEditDto = new CommerceContractEditDto();
            contractEditDto.setId(contractGetVo.getId());
            contractEditDto.setActualArea(roomsExpandGetVo.getActualArea());
            contractEditDto.setRentArea(roomsExpandGetVo.getRentArea());
            commerceContractProvider.editArea(contractEditDto);
        }

        logAddDto.setCreateBy(curUser.getUserId());
        logAddDto.setCreateUser(curUser.getUserName());
        logAddDto.setCreateUserName(curUser.getRealName());
        logAddDto.setTenantId(curUser.getTenantId());
        logAddDto.setTenantName(curUser.getTenantName());
        logAddDto.setEntId(curUser.getEntId());
        logAddDto.setOrgFid(curUser.getOrgFid());
        logAddDto.setOrgFname(curUser.getOrgFname());
        logAddDto.setContractCode(contractGetVo.getContractCode());
        // 获取修改后的数据
        CommerceContractGetReq afterGetReq = new CommerceContractGetReq();
        afterGetReq.setId(editReq.getId());
        CommerceContractGetResp afterResp = get(afterGetReq);
        if (afterResp!=null){
            logAddDto.setContractContent(JsonUtils.toJson(afterResp));
        }
        commerceContractLogProvider.add(logAddDto);
    }

    @Override
    public void addition(CommerceContractAdditionReq additionReq) {
        //设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        List<Long> files = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(additionReq.getFiles())){
            List<SysAttachmentListVo> attachmentList = FileUtils.findFile(additionReq.getId(), FileType.CONTRACT_FILE, attachmentProvider);
            if (CollectionUtils.isNotEmpty(attachmentList)){
                for (SysAttachmentListVo attachment : attachmentList) {
                    files.add(attachment.getFileId());
                }
            }
            for (Long file : additionReq.getFiles()) {
                if (!files.contains(file)){
                    files.add(file);
                }
            }
            // 保存文件
            FileUtils.saveFileAndDelete(FileType.CONTRACT_FILE, additionReq.getId(), curUser, files, attachmentProvider);
        }
    }

    /**
     * 递增率处理
     * @param originStr
     * @return
     */
    private String convertRateStr(String originStr) {
        if (originStr.length() <= 2){
            return originStr;
        }
        StringBuffer sf = new StringBuffer();
        sf.append("[");
        String contentStr = originStr.substring(1, originStr.length() - 1);
        String[] splitArray = contentStr.split(",");
        for (int i = 0; i < splitArray.length-1; i++) {
            sf.append("\"");
            sf.append(splitArray[i].trim());
            sf.append("\"");
            sf.append(",");
        }
        sf.append("\"");
        sf.append(splitArray[splitArray.length-1].trim());
        sf.append("\"");
        sf.append("]");
        return sf.toString();
    }

    public static void main(String[] args) {
        String aa = "0--10000--3";
        String[] split = aa.split("-");
        System.out.println(split.length);
        System.out.println("=====");
        for (String s : split) {
            System.out.println(s);
        }
    }


    /**
     * 校验阶梯变化是否含有负数
     * @param originStr
     * @return
     */
    private Boolean checkLadRate(String originStr) {
        Boolean flag = Boolean.FALSE;
        if (originStr.length() <= 2){
            return flag;
        }
        String contentStr = originStr.substring(1, originStr.length() - 1);
        String[] splitArray = contentStr.split(",");
        for (String splitStr : splitArray) {
            String[] splitLen = splitStr.split("-");
            if (splitLen.length>3){
                flag = Boolean.TRUE;
            }
        }
        return flag;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public CommerceContractAddResp add(CommerceContractAddReq addReq) {
        //设置创建人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        CommerceContractAddDto dto = null;
        if (StringUtils.isNotEmpty(addReq.getOrderCode())) {
            //校验工单是否已有合同
            boolean hasContract = commerceContractProvider.checkContract(addReq.getOrderCode());
            if (hasContract) {
                throw new ServiceException("工单存在审批中或已通过的合同，请勿重复保存提交");
            }
            CommerceOrderGetVo orderGetVo = commerceOrderProvider.getByOrderCode(addReq.getOrderCode());
            RoomsGetVo roomsGetVo = roomsProvider.get(orderGetVo.getRoomId());
            if (Objects.isNull(roomsGetVo)) {
                throw new ServiceException("店铺不存在");
            }
            dto = BeanUtils.copyProperties(roomsGetVo, CommerceContractAddDto.class);
            dto.setRoomId(orderGetVo.getRoomId());
            //更新工单执行步骤
            CommerceSubOrderGetVo subOrderGetVo = commerceSubOrderProvider.updateExecuteStep(addReq.getOrderCode(), curUser.getUserName(), OrderExecuteStepEnum.CONTRACT.getCode());
            dto.setSubOrderId(subOrderGetVo.getId());

            CommerceOrderExtendGetVo orderExtendGetVo = commerceOrderExtendProvider.getByOrderCode(addReq.getOrderCode());
            dto.setActualArea(orderExtendGetVo.getActualArea());
            dto.setRentArea(orderExtendGetVo.getRentArea());
            dto.setRoomStatus(roomsGetVo.getStatus());
            dto.setEmptyStartDate(orderExtendGetVo.getEmptyStartDate());
            dto.setLastContractDate(orderExtendGetVo.getLastContractDate());
            dto.setElectricity(orderExtendGetVo.getElectricity());
            dto.setWaterSupply(orderExtendGetVo.getWaterSupply());
            dto.setDrainage(orderExtendGetVo.getDrainage());
            dto.setExhaustFumes(orderExtendGetVo.getExhaustFumes());
        } else if (StringUtils.isNotEmpty(addReq.getContractCode())) {
            CommerceContractListDto listDto = new CommerceContractListDto();
            listDto.setContractCode(addReq.getContractCode());
            CommerceContractGetVo contractGetVo = commerceContractProvider.get(listDto);
            dto = BeanUtils.copyProperties(contractGetVo, CommerceContractAddDto.class);

            RoomsGetVo roomsGetVo = roomsProvider.get(contractGetVo.getRoomId());
            if (Objects.isNull(roomsGetVo)) {
                throw new ServiceException("店铺不存在");
            }
            org.springframework.beans.BeanUtils.copyProperties(roomsGetVo, dto);

            dto.setApplyDate(null);
            dto.setApproveDate(null);
            dto.setArchiveDate(null);

            CommerceContractInfoGetVo contractInfoGetVo = commerceContractInfoProvider.getByContractCode(contractGetVo.getContractCode());
            dto.setLastContractDate(contractInfoGetVo.getRentEndDate());
        }

        org.springframework.beans.BeanUtils.copyProperties(addReq, dto);

        CommerceContractInfoAddDto contractInfoAddDto = BeanUtils.copyProperties(addReq.getContractInfo(), CommerceContractInfoAddDto.class);
        dto.setContractInfo(contractInfoAddDto);
        CommerceContractRentAddDto contractRentAddDto = BeanUtils.copyProperties(addReq.getContractRent(), CommerceContractRentAddDto.class);
        if (CollectionUtils.isNotEmpty(addReq.getContractRent().getIncrementalRateList())) {
            contractRentAddDto.setIncrementalRateList(JSON.toJSONString(addReq.getContractRent().getIncrementalRateList()));
        }
        if (CollectionUtils.isNotEmpty(addReq.getContractRent().getMonthFeePercentList())) {
            contractRentAddDto.setMonthFeePercentList(JSON.toJSONString(addReq.getContractRent().getMonthFeePercentList()));
        }
        if (ObjectUtils.isNotEmpty(addReq.getContractRent().getOperateIncrementalRateList())) {
            contractRentAddDto.setOperateIncrementalRateList(JSON.toJSONString(addReq.getContractRent().getOperateIncrementalRateList()));
        }
        if (ObjectUtils.isNotEmpty(addReq.getContractRent().getManageIncrementalRateList())) {
            contractRentAddDto.setManageIncrementalRateList(JSON.toJSONString(addReq.getContractRent().getManageIncrementalRateList()));
        }
        dto.setContractRent(contractRentAddDto);

        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());

//        dto.setTenantId(curUser.getTenantId());
        dto.setExecuteUserFid(curUser.getOrgFid());
        dto.setExecuteUserFname(curUser.getOrgFname());

        EntProjectGetVo projectGetVo = entProjectProvider.get(dto.getTenantId());
        dto.setProjectCode(projectGetVo.getCode());
        CommerceContractAddVo addVo = commerceContractProvider.add(dto);

        if (Objects.nonNull(dto.getOperate()) && dto.getOperate().equals(2)) {
            //启动审批流程
            ProcessStartDto processStartDto = new ProcessStartDto();
            processStartDto.setTenantId(dto.getTenantId());
            processStartDto.setTenantName(dto.getTenantName());
            ;
            processStartDto.setEntId(curUser.getEntId());
            processStartDto.setOrgFid(curUser.getOrgFid());
            processStartDto.setOrgFname(curUser.getOrgFname());
            ;
            processStartDto.setUserCode(curUser.getUserName());
            processStartDto.setUserId(curUser.getUserId());
            processStartDto.setUserName(curUser.getRealName());
            processStartDto.setUserOrgId(curUser.getUserFid());
            processStartDto.setUserOrgName(curUser.getUserFname());
            processStartDto.setProcDefCode("business_contract");
            processStartDto.setRemark(dto.getName() + " " + dto.getBrandName() );
            // 具体业务表单ID
            processStartDto.setBizId(addVo.getId());
            processStartDto.setApplyTime(LocalDateTime.now());

            Map<String, Object> variables = new HashMap<String, Object>();
            variables.put("contract_code", addVo.getContractCode());
            processStartDto.setVariables(variables);
            ProcessTaskVo vo = processServiceProvider.start(processStartDto);

            //回填流程ID
            commerceContractProvider.updateProcessInstanceId(addVo.getId(), vo.getProcessInstanceId());
        }


        CommerceContractAddResp addResp = BeanUtils.copyProperties(addVo, CommerceContractAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    @GlobalTransactional(rollbackFor = Exception.class)
    public void edit(CommerceContractEditReq editReq) {
        CommerceContractEditDto dto = BeanUtils.copyProperties(editReq, CommerceContractEditDto.class);

        //设置修改人信息
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        CommerceContractRentAddDto rentAddDto = dto.getContractRent();
        if (CollectionUtils.isNotEmpty(editReq.getContractRent().getIncrementalRateList())) {
            rentAddDto.setIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getIncrementalRateList()));
        }
        if (CollectionUtils.isNotEmpty(editReq.getContractRent().getMonthFeePercentList())) {
            rentAddDto.setMonthFeePercentList(JSON.toJSONString(editReq.getContractRent().getMonthFeePercentList()));
        }
        if (ObjectUtils.isNotEmpty(editReq.getContractRent().getOperateIncrementalRateList())) {
            rentAddDto.setOperateIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getOperateIncrementalRateList()));
        }
        if (ObjectUtils.isNotEmpty(editReq.getContractRent().getManageIncrementalRateList())) {
            rentAddDto.setManageIncrementalRateList(JSON.toJSONString(editReq.getContractRent().getManageIncrementalRateList()));
        }
        dto.setContractRent(rentAddDto);
        CommerceContractGetVo contractGetVo = commerceContractProvider.edit(dto);

        if (Objects.nonNull(dto.getOperate()) && dto.getOperate().equals(2)) {
            if (StringUtils.isEmpty(contractGetVo.getProcessInstanceId())) {
                //启动审批流程
                ProcessStartDto processStartDto = new ProcessStartDto();
                processStartDto.setTenantId(curUser.getTenantId());
                processStartDto.setTenantName(curUser.getTenantName());

                processStartDto.setEntId(curUser.getEntId());
                processStartDto.setOrgFid(curUser.getOrgFid());
                processStartDto.setOrgFname(curUser.getOrgFname());

                processStartDto.setUserCode(curUser.getUserName());
                processStartDto.setUserId(curUser.getUserId());
                processStartDto.setUserName(curUser.getRealName());
                processStartDto.setUserOrgId(curUser.getUserFid());
                processStartDto.setUserOrgName(curUser.getUserFname());
                processStartDto.setProcDefCode("business_contract");
                processStartDto.setRemark(contractGetVo.getName() + " " + contractGetVo.getBrandName() );
                // 具体业务表单ID
                processStartDto.setBizId(editReq.getId());
                processStartDto.setApplyTime(LocalDateTime.now());

                Map<String, Object> variables = new HashMap<String, Object>();
                processStartDto.setVariables(variables);
                ProcessTaskVo vo = processServiceProvider.start(processStartDto);

                //回填流程ID
                commerceContractProvider.updateProcessInstanceId(editReq.getId(), vo.getProcessInstanceId());
            } else {
                FlwApplyListDto applyListDto = new FlwApplyListDto();
                applyListDto.setProcInstanceId(contractGetVo.getProcessInstanceId());
                FlwApplyGetVo flwApplyGetVo = flwApplyProvider.get(applyListDto);
                if (Objects.nonNull(flwApplyGetVo) && flwApplyGetVo.getStatus() == ProcessStatusEnum.Reject.getStatus()) {
                    ReSubmitDto reSubmitDto = new ReSubmitDto();
                    reSubmitDto.setProcessInstanceId(contractGetVo.getProcessInstanceId());
                    reSubmitDto.setBizId(contractGetVo.getId());
                    reSubmitDto.setRemark(contractGetVo.getName() + " " + contractGetVo.getBrandName() );
                    reSubmitDto.setApplyTime(LocalDateTime.now());
                    reSubmitDto.setUserId(curUser.getUserId());
                    reSubmitDto.setUserCode(curUser.getUserName());
                    reSubmitDto.setUserName(curUser.getRealName());
                    reSubmitDto.setUserOrgId(curUser.getUserFid());
                    reSubmitDto.setUserOrgName(curUser.getUserFname());
                    processServiceProvider.reSubmit(reSubmitDto);
                } else {
                    //流程自动终止时，重新提交新流程
                    ProcessStartDto processStartDto = new ProcessStartDto();
                    processStartDto.setTenantId(curUser.getTenantId());
                    processStartDto.setTenantName(curUser.getTenantName());

                    processStartDto.setEntId(curUser.getEntId());
                    processStartDto.setOrgFid(curUser.getOrgFid());
                    processStartDto.setOrgFname(curUser.getOrgFname());

                    processStartDto.setUserCode(curUser.getUserName());
                    processStartDto.setUserId(curUser.getUserId());
                    processStartDto.setUserName(curUser.getRealName());
                    processStartDto.setUserOrgId(curUser.getUserFid());
                    processStartDto.setUserOrgName(curUser.getUserFname());
                    processStartDto.setProcDefCode("business_contract");
                    processStartDto.setRemark(contractGetVo.getName() + " " + contractGetVo.getBrandName() );
                    // 具体业务表单ID
                    processStartDto.setBizId(editReq.getId());
                    processStartDto.setApplyTime(LocalDateTime.now());

                    Map<String, Object> variables = new HashMap<String, Object>();
                    processStartDto.setVariables(variables);
                    ProcessTaskVo vo = processServiceProvider.start(processStartDto);

                    //回填流程ID
                    commerceContractProvider.updateProcessInstanceId(editReq.getId(), vo.getProcessInstanceId());
                }
            }
        }
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(CommerceContractDelReq delReq) {
        commerceContractProvider.delete(delReq.getId());
    }

    @Override
    public void archive(CommerceContractArchiveReq archiveReq) {
        CommerceContractArchiveDto dto = BeanUtils.copyProperties(archiveReq, CommerceContractArchiveDto.class);
        //设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());

        //合同归档
        CommerceContractArchiveVo contractGetVo = commerceContractProvider.archive(dto);

        // 保存文件
        FileUtils.saveFileAndDelete(FileType.CONTRACT_FILE, archiveReq.getId(), curUser, archiveReq.getFiles(), attachmentProvider);

        // 更新铺位状态
        Long roomId = contractGetVo.getRoomId();
        log.info("roomId:{}", roomId);
        RoomsGetVo roomsGetVo = roomsProvider.get(roomId);
        if (roomsGetVo!=null && roomsGetVo.getStatus()==1){
            // 生成营运工单
            CommerceContractArchiveVo contractArchiveVo = commerceContractProvider.findSignContract(roomId);
            try {
                if (contractArchiveVo != null) {
                    // 生成进场工单
                    contractArchiveVo = commerceContractProvider.contractRentDateStart(contractArchiveVo);
                    // 更新铺位信息
                    RoomsEditDto roomsEditDto = new RoomsEditDto();
                    roomsEditDto.setId(contractArchiveVo.getRoomId());
                    roomsEditDto.setContractCode(contractArchiveVo.getContractCode());
                    if (Objects.nonNull(contractArchiveVo.getContractInfo())) {
                        CommerceContractInfoGetVo contractInfo = contractArchiveVo.getContractInfo();
                        roomsEditDto.setBrandId(contractInfo.getBrandId());
                        roomsEditDto.setBrandName(contractInfo.getBrandName());
                        roomsEditDto.setSupplierId(contractInfo.getSupplierId());
                        roomsEditDto.setSupplierName(contractInfo.getSupplierName());
                        roomsEditDto.setStoreId(contractArchiveVo.getRoomShopId());
                    }
                    roomsEditDto.setStatus(3);
                    roomsProvider.edit(roomsEditDto);
                }
            } catch (Exception e) {
                log.error("合同归档生成营运工单失败：{}", e.getMessage());
            }
        }

//        //更新铺位信息
//        RoomsEditDto roomsEditDto = new RoomsEditDto();
//        roomsEditDto.setId(contractGetVo.getRoomId());
//
//        if (Objects.nonNull(contractGetVo.getContractInfo().getContractType()) && contractGetVo.getContractInfo().getContractType().equals(1)) {
//            roomsEditDto.setStatus(3);
//        }
//        roomsEditDto.setContractCode(contractGetVo.getContractCode());
//        if(Objects.nonNull(contractGetVo.getContractInfo())) {
//            CommerceContractInfoGetVo contractInfo = contractGetVo.getContractInfo();
//            roomsEditDto.setBrandId(contractInfo.getBrandId());
//            roomsEditDto.setBrandName(contractInfo.getBrandName());
//            roomsEditDto.setSupplierId(contractInfo.getSupplierId());
//            roomsEditDto.setSupplierName(contractInfo.getSupplierName());
//            roomsEditDto.setStoreId(contractGetVo.getRoomShopId());
//        }
//        roomsProvider.edit(roomsEditDto);
//
//        try{
//            if(Objects.nonNull(contractGetVo.getMsgLogAddVo())){
//                MsgLogAddDto msgLogAddDto = BeanUtils.copyProperties(contractGetVo.getMsgLogAddVo(), MsgLogAddDto.class);
//                msgLogAddDto.setTenantId(contractGetVo.getTenantId());
//                msgLogProvider.add(msgLogAddDto);
//            }
//        }catch (Exception e){
//            log.error("发送消息失败：{}",e.getMessage());
//        }
    }

    @Override
    public CommerceContractGetResp getByOrderCode(CommerceContractGetOrderReq getReq) {
        CommerceContractListDto dto = new CommerceContractListDto();
        dto.setOrderCode(getReq.getOrderCode());
        dto.setApproveStatus(ComercePlanApproveStatusEnum.approve.getCode());
        CommerceContractGetVo getVo = commerceContractProvider.get(dto);
        if (Objects.isNull(getVo) || Objects.isNull(getVo.getArchiveDate())) {
            return null;
        }
        CommerceContractInfoGetVo infoGetVo = commerceContractInfoProvider.getByContractCode(getVo.getContractCode());
        CommerceContractRentGetVo rentGetVo = commerceContractRentProvider.getByContractCode(getVo.getContractCode());

        CommerceContractGetResp getResp = BeanUtils.copyProperties(getVo, CommerceContractGetResp.class);
        CommerceContractInfoGetResp infoGetResp = BeanUtils.copyProperties(infoGetVo, CommerceContractInfoGetResp.class);
        CommerceContractRentGetResp rentGetResp = BeanUtils.copyProperties(rentGetVo, CommerceContractRentGetResp.class);

        getResp.setContractInfo(infoGetResp);
        // 获取租赁政策租金信息
        getRentPolicyInfo(getVo, rentGetResp);
        getResp.setContractRent(rentGetResp);

        //合同附件
        try {
            List<SysAttachmentListVo> brandFiles = FileUtils.findFile(getVo.getId(), FileType.CONTRACT_FILE, attachmentProvider);
            List<FileResp> files = BeanUtils.copyProperties(brandFiles, FileResp.class);
            getResp.setFiles(files);
        } catch (Exception e) {
            log.error("文件服务异常：{}", e.getMessage());
        }

        getResp.setOgn(getOgn(getVo.getOrgFname(), getVo.getOrgFid()));
        getResp.setDpt(getDpt(getVo.getOrgFname(), getVo.getOrgFid()));

        return getResp;
    }

    @Override
    public void export(HttpServletResponse response, CommerceContractListReq listReq) {
        listReq.setPageNum(1);
        listReq.setPageSize(-1);
        List<CommerceContractListItem> contractList = page(listReq, true).getItems();
        List<CommerceContractExportResp> exportRespList = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(contractList)){
                for (CommerceContractListItem contractItem : contractList) {
                    CommerceContractGetReq getReq = new CommerceContractGetReq();
                    getReq.setId(contractItem.getId());
                    CommerceContractGetResp getResp = get(getReq);
                    CommerceContractInfoGetResp contractInfo = getResp.getContractInfo();
                    CommerceContractRentGetResp contractRent = getResp.getContractRent();
                    CommerceContractExportResp exportResp = BeanUtils.copyProperties(getResp, CommerceContractExportResp.class);
                    // 铺位状态
                    Integer roomStatus = getResp.getRoomStatus();
                    if (roomStatus!=null){
                        if (roomStatus==1){
                            exportResp.setRoomStatus("空置");
                        }else if (roomStatus==2){
                            exportResp.setRoomStatus("在营");
                        }else if (roomStatus==3){
                            exportResp.setRoomStatus("已签约");
                        }
                    }
                    exportResp.setLessor(contractInfo.getLessor());
                    exportResp.setLessorRepresentative(contractInfo.getLessorRepresentative());
                    exportResp.setLessorAddress(contractInfo.getLessorAddress());
                    exportResp.setBrandName(contractInfo.getBrandName());
                    exportResp.setBrandCommercialTypeName(contractInfo.getBrandCommercialTypeName());
                    exportResp.setBrandCategoryName(contractInfo.getBrandCategoryName());
                    exportResp.setBusinessScope(contractInfo.getBusinessScope());
                    exportResp.setSupplierName(contractInfo.getSupplierName());
                    exportResp.setSupplierCertificateCode(contractInfo.getSupplierCertificateCode());
                    exportResp.setSupplierCertificateAddress(contractInfo.getSupplierCertificateAddress());
                    exportResp.setContactName(contractInfo.getContactName());
                    exportResp.setContactPhon(contractInfo.getContactPhon());
                    exportResp.setSupplierQuality(contractInfo.getSupplierQuality());
                    exportResp.setSupplierSecurity(contractInfo.getSupplierSecurity());
                    exportResp.setSupplierViolate(contractInfo.getSupplierViolate());
                    exportResp.setThirdParty(contractInfo.getThirdParty());
                    // 商铺类型
                    Integer shopType = contractInfo.getShopType();
                    if (shopType!=null){
                        if (shopType==0){
                            exportResp.setShopType("正铺");
                        }else if (shopType==1){
                            exportResp.setShopType("临促");
                        }
                    }
                    // 签约主体
                    Integer signEntity = contractInfo.getSignEntity();
                    if (signEntity!=null){
                        if (signEntity==1){
                            exportResp.setSignEntity("公司");
                        }else if (signEntity==2){
                            exportResp.setSignEntity("个人");
                        }
                    }
                    // 签约类型
                    Integer contractType = contractInfo.getContractType();
                    if (contractType!=null){
                        if (contractType==1){
                            exportResp.setContractType("新签");
                        }else if (contractType==2){
                            exportResp.setContractType("续签");
                        }
                    }
                    // 经营模式
                    Integer businessType = contractInfo.getBusinessType();
                    if (businessType!=null){
                        if (businessType==1){
                            exportResp.setBusinessType("品牌直营");
                        }else if (businessType==2){
                            exportResp.setBusinessType("品牌代理");
                        }else if (businessType==3){
                            exportResp.setBusinessType("品牌加盟");
                        }else if (businessType==4){
                            exportResp.setBusinessType("个体经营");
                        }
                    }
                    // 是否重新装修
                    Integer decoration = contractInfo.getDecoration();
                    if (decoration!=null){
                        if (decoration==0){
                            exportResp.setDecoration("否");
                        }else if (decoration==1){
                            exportResp.setDecoration("是");
                        }
                    }

                    Integer rentType = contractInfo.getRentType();
                    LocalDate rentStartDate = contractInfo.getRentStartDate();
                    LocalDate rentFeeDateEnd = contractInfo.getRentEndDate();
                    exportResp.setContractPeriod(rentStartDate+"至"+rentFeeDateEnd);
                    exportResp.setDeliveryDate(contractInfo.getDeliveryDate());
                    exportResp.setRentFeeDate(contractInfo.getRentFeeDate());
                    exportResp.setOpenDate(contractInfo.getOpenDate());
                    // 租金递增方式
                    Integer rentIncreaseMode = contractInfo.getRentIncreaseMode();
                    Integer operateIncreaseMode = contractInfo.getOperateIncreaseMode();
                    Integer manageIncreaseMode = contractInfo.getManageIncreaseMode();

                    // 扣点方式
                    Integer pointsMode = contractInfo.getPointsMode();
                    BigDecimal incrementalRate = contractRent.getIncrementalRate();
                    List<String> incrementalRateList = contractRent.getIncrementalRateList();
                    List<String> monthFeePercentList = contractRent.getMonthFeePercentList();
                    List<String> operateIncrementalRateList = contractRent.getOperateIncrementalRateList();
                    List<String> manageIncrementalRateList = contractRent.getManageIncrementalRateList();

                    // 年份列表
                    List<String> yearList = CalculateRuleUtils.getYearList(rentStartDate, rentFeeDateEnd);
                    // 租金递增率集合
                    StringBuffer rateList = new StringBuffer();
                    // 运营管理费递增率集合
                    StringBuffer operateRateList = new StringBuffer();
                    // 物业管理费递增率集合
                    StringBuffer manageRateList = new StringBuffer();
                    for (int i = 0; i < yearList.size(); i++) {
                        String year = yearList.get(i);
                        if (i==0){
                            if (rentIncreaseMode!=null){
                                if (rentIncreaseMode==0){
                                    rateList.append("【"+year+","+"0%"+"】"+" ");
                                }else if (rentIncreaseMode==1){
                                    rateList.append("【"+year+","+"0元"+"】"+" ");
                                }
                            }
                            if (operateIncreaseMode!=null){
                                if (operateIncreaseMode==0){
                                    operateRateList.append("【"+year+","+"0%"+"】"+" ");
                                }else if (operateIncreaseMode==1){
                                    operateRateList.append("【"+year+","+"0元"+"】"+" ");
                                }
                            }
                            if (manageIncreaseMode!=null){
                                if (manageIncreaseMode==0){
                                    manageRateList.append("【"+year+","+"0%"+"】"+" ");
                                }else if (manageIncreaseMode==1){
                                    manageRateList.append("【"+year+","+"0元"+"】"+" ");
                                }
                            }
                        }else {
                            if (rentIncreaseMode!=null){
                                String rate = "0";
                                if (CollectionUtils.isNotEmpty(incrementalRateList)){
                                    rate = incrementalRateList.size() >= i ? incrementalRateList.get(i - 1) : incrementalRateList.get(incrementalRateList.size()-1);
                                }
                                if (rentIncreaseMode==0){
                                    rateList.append("【"+year+","+rate+"%"+"】"+" ");
                                }else if (rentIncreaseMode==1){
                                    rateList.append("【"+year+","+rate+"元"+"】"+" ");
                                }
                            }
                            if (operateIncreaseMode!=null){
                                String operateRate = "0";
                                if (CollectionUtils.isNotEmpty(operateIncrementalRateList)){
                                    operateRate = operateIncrementalRateList.size() >= i ? operateIncrementalRateList.get(i - 1) : operateIncrementalRateList.get(operateIncrementalRateList.size()-1);
                                }
                                if (operateIncreaseMode==0){
                                    operateRateList.append("【"+year+","+operateRate+"%"+"】"+" ");
                                }else if (operateIncreaseMode==1){
                                    operateRateList.append("【"+year+","+operateRate+"元"+"】"+" ");
                                }
                            }
                            if (manageIncreaseMode!=null){
                                String manageRate = "0";
                                if (CollectionUtils.isNotEmpty(manageIncrementalRateList)){
                                    manageRate = manageIncrementalRateList.size() >= i ? manageIncrementalRateList.get(i - 1) : manageIncrementalRateList.get(manageIncrementalRateList.size()-1);
                                }
                                if (manageIncreaseMode==0){
                                    manageRateList.append("【"+year+","+manageRate+"%"+"】"+" ");
                                }else if (manageIncreaseMode==1){
                                    manageRateList.append("【"+year+","+manageRate+"元"+"】"+" ");
                                }
                            }
                        }
                    }
                    // 扣点集合
                    StringBuffer pointList = new StringBuffer();
                    // 阶梯变化单独处理
                    if (pointsMode!=null && pointsMode==1){
                        if (CollectionUtils.isNotEmpty(monthFeePercentList)){
                            for (String rate : monthFeePercentList) {
                                String[] ladderSplit = rate.split("-");
                                if (ladderSplit.length>2){
                                    String startValue = ladderSplit[0];
                                    String endValue = ladderSplit[1];
                                    String monthFeePercentValue = ladderSplit[2];
                                    endValue = "0".equals(endValue) ? "无穷大" : endValue;
                                    pointList.append("【"+startValue+"-"+endValue+"，"+monthFeePercentValue+"%"+"】"+" ");
                                }else {
                                    pointList.append("【"+rate+"】"+" ");
                                }
                            }
                        }
                    }else {
                        for (int i = 0; i < yearList.size(); i++) {
                            String year = yearList.get(i);
                            if (pointsMode!=null){
                                String rate = "0";
                                if (CollectionUtils.isNotEmpty(monthFeePercentList)){
                                    rate = monthFeePercentList.size() > i ? monthFeePercentList.get(i) : monthFeePercentList.get(monthFeePercentList.size()-1);
                                }
                                if (pointsMode==0){
                                    pointList.append("【"+year+","+rate+"%"+"】"+" ");
                                }else if (pointsMode==2){
                                    pointList.append("【"+year+","+rate+"%"+"】"+" ");
                                }
                            }
                        }
                    }

                    if (rentIncreaseMode!=null){
                        if (rentIncreaseMode==0){
                            exportResp.setRentIncreaseMode("固定比例");
                            exportResp.setIncrementalRate(incrementalRate);
                            exportResp.setIncrementalRateList(rateList.toString());
                            exportResp.setIncrementalRateListMatch("-");
                        }else if (rentIncreaseMode==1){
                            exportResp.setRentIncreaseMode("固定金额");
                            exportResp.setIncrementalFeePolicy(incrementalRate);
                            exportResp.setIncrementalFee(rateList.toString());
                            exportResp.setIncrementalFeeMatch("-");
                        }
                    }

                    if (operateIncreaseMode!=null){
                        if (operateIncreaseMode==0){
                            exportResp.setOperateIncreaseModeStr("比例递增");
                            exportResp.setOperateIncrementalRatePolicy("-");
                            exportResp.setOperateIncrementalRateList(operateRateList.toString());
                            exportResp.setOperateIncrementalRateListMatch("-");
                        }else if (operateIncreaseMode==1){
                            exportResp.setOperateIncreaseModeStr("金额递增");
                            exportResp.setOperateIncrementalMoneyPolicy("-");
                            exportResp.setOperateIncrementalMoneyList(operateRateList.toString());
                            exportResp.setOperateIncrementalMoneyListMatch("-");
                        }
                    }

                    if (manageIncreaseMode!=null){
                        if (manageIncreaseMode==0){
                            exportResp.setManageIncreaseModeStr("比例递增");
                            exportResp.setManageIncrementalRatePolicy("-");
                            exportResp.setManageIncrementalRateList(manageRateList.toString());
                            exportResp.setManageIncrementalRateListMatch("-");
                        }else if (manageIncreaseMode==1){
                            exportResp.setManageIncreaseModeStr("金额递增");
                            exportResp.setManageIncrementalMoneyPolicy("-");
                            exportResp.setManageIncrementalMoneyList(manageRateList.toString());
                            exportResp.setManageIncrementalMoneyListMatch("-");
                        }
                    }

                    if (pointsMode!=null){
                        if (pointsMode==0){
                            exportResp.setPointsMode("固定比例");
                        }else if (pointsMode==1){
                            exportResp.setPointsMode("阶梯变化");
                        }else if (pointsMode==2){
                            exportResp.setPointsMode("年份变化");
                        }
                    }
                    // 结算日期
                    Integer settlementDate = contractInfo.getSettlementDate();
                    if (settlementDate!=null){
                        if (settlementDate==0){
                            exportResp.setSettlementDate("月末");
                        }else {
                            exportResp.setSettlementDate(settlementDate+"号");
                        }
                    }
                    // 合同租赁信息
                    exportResp.setGuarantSaleContract(contractRent.getGuarantSaleContract());
                    exportResp.setYearLimit(contractRent.getYearLimit());
                    exportResp.setYearLimitContract(contractRent.getYearLimitContract());
                    Integer yearLimitMatch = contractRent.getYearLimitMatch();
                    if (yearLimitMatch!=null){
                        if (yearLimitMatch==0){
                            exportResp.setYearLimitMatch("否");
                        }else{
                            exportResp.setYearLimitMatch("是");
                        }
                    }
                    exportResp.setMonthPrice(contractRent.getMonthPrice());
                    exportResp.setMonthPriceContract(contractRent.getMonthPriceContract());
                    exportResp.setMonthPriceMatch("-");

                    exportResp.setMonthFee(contractRent.getMonthFee());
                    exportResp.setMonthFeeContract(contractRent.getMonthFeeContract());
                    exportResp.setMonthFeeMatch("-");

                    exportResp.setMonthFeePercentListPolicy("-");
                    exportResp.setMonthFeePercentList(pointList.toString());
                    exportResp.setMonthFeePercentListMatch("-");

                    exportResp.setMonthManagePrice(contractRent.getMonthManagePrice());
                    exportResp.setMonthManagePriceContract(contractRent.getMonthManagePriceContract());
                    Integer monthManagePriceMatch = contractRent.getMonthManagePriceMatch();
                    if (monthManagePriceMatch!=null){
                        if (monthManagePriceMatch==0){
                            exportResp.setMonthManagePriceMatch("否");
                        }else{
                            exportResp.setMonthManagePriceMatch("是");
                        }
                    }

                    exportResp.setMonthOperatePrice(contractRent.getMonthOperatePrice());
                    exportResp.setMonthOperatePriceContract(contractRent.getMonthOperatePriceContract());
                    Integer monthOperatePriceMatch = contractRent.getMonthOperatePriceMatch();
                    if (monthOperatePriceMatch!=null){
                        if (monthOperatePriceMatch==0){
                            exportResp.setMonthOperatePriceMatch("否");
                        }else{
                            exportResp.setMonthOperatePriceMatch("是");
                        }
                    }

                    exportResp.setOtherPrice(contractRent.getOtherPrice());
                    exportResp.setOtherPriceContract(contractRent.getOtherPriceContract());
                    Integer otherPriceMatch = contractRent.getOtherPriceMatch();
                    if (otherPriceMatch!=null){
                        if (otherPriceMatch==0){
                            exportResp.setOtherPriceMatch("否");
                        }else{
                            exportResp.setOtherPriceMatch("是");
                        }
                    }

                    exportResp.setMonthManageFee(contractRent.getMonthManageFee());
                    exportResp.setMonthManageFeeContract(contractRent.getMonthManageFeeContract());
                    exportResp.setMonthManageFeeMatch("-");

                    exportResp.setMonthOperateFee(contractRent.getMonthOperateFee());
                    exportResp.setMonthOperateFeeContract(contractRent.getMonthOperateFeeContract());
                    exportResp.setMonthOperateFeeMatch("-");

                    exportResp.setOtherFee("-");
                    exportResp.setOtherFeeContract(contractRent.getOtherFeeContract());
                    exportResp.setOtherFeeMatch("-");

                    exportResp.setFeeFree(contractRent.getFeeFree());
                    exportResp.setFeeFreeContract(contractRent.getFeeFreeContract());
                    Integer feeFreeMatch = contractRent.getFeeFreeMatch();
                    if (feeFreeMatch!=null){
                        if (feeFreeMatch==0){
                            exportResp.setFeeFreeMatch("否");
                        }else{
                            exportResp.setFeeFreeMatch("是");
                        }
                    }

                    exportResp.setCompositeManageFee(contractRent.getCompositeManageFee());
                    exportResp.setCompositeManageFeeContract(contractRent.getCompositeManageFeeContract());
                    Integer compositeManageFeeMatch = contractRent.getCompositeManageFeeMatch();
                    if (compositeManageFeeMatch!=null){
                        if (compositeManageFeeMatch==0){
                            exportResp.setCompositeManageFeeMatch("否");
                        }else{
                            exportResp.setCompositeManageFeeMatch("是");
                        }
                    }

                    exportResp.setRentBailFee(contractRent.getRentBailFee());
                    exportResp.setRentBailFeeContract(contractRent.getRentBailFeeContract());
                    Integer rentBailFeeMatch = contractRent.getRentBailFeeMatch();
                    if (rentBailFeeMatch!=null){
                        if (rentBailFeeMatch==0){
                            exportResp.setRentBailFeeMatch("否");
                        }else{
                            exportResp.setRentBailFeeMatch("是");
                        }
                    }

                    exportResp.setPropertyManageBailFee(contractRent.getPropertyManageBailFee());
                    exportResp.setPropertyManageBailFeeContract(contractRent.getPropertyManageBailFeeContract());
                    Integer propertyManageBailFeeMatch = contractRent.getPropertyManageBailFeeMatch();
                    if (propertyManageBailFeeMatch!=null){
                        if (propertyManageBailFeeMatch==0){
                            exportResp.setPropertyManageBailFeeMatch("否");
                        }else{
                            exportResp.setPropertyManageBailFeeMatch("是");
                        }
                    }

                    exportResp.setOperateManageBailFee(contractRent.getOperateManageBailFee());
                    exportResp.setOperateManageBailFeeContract(contractRent.getOperateManageBailFeeContract());
                    Integer operateManageBailFeeMatch = contractRent.getOperateManageBailFeeMatch();
                    if (operateManageBailFeeMatch!=null){
                        if (operateManageBailFeeMatch==0){
                            exportResp.setOperateManageBailFeeMatch("否");
                        }else{
                            exportResp.setOperateManageBailFeeMatch("是");
                        }
                    }

                    exportResp.setCommonBailFee(contractRent.getCommonBailFee());
                    exportResp.setCommonBailFeeContract(contractRent.getCommonBailFeeContract());
                    Integer commonBailFeeMatch = contractRent.getCommonBailFeeMatch();
                    if (commonBailFeeMatch!=null){
                        if (commonBailFeeMatch==0){
                            exportResp.setCommonBailFeeMatch("否");
                        }else{
                            exportResp.setCommonBailFeeMatch("是");
                        }
                    }

                    exportResp.setDecorationBailFee(contractRent.getDecorationBailFee());
                    exportResp.setDecorationBailFeeContract(contractRent.getDecorationBailFeeContract());
                    Integer decorationBailFeeMatch = contractRent.getDecorationBailFeeMatch();
                    if (decorationBailFeeMatch!=null){
                        if (decorationBailFeeMatch==0){
                            exportResp.setDecorationBailFeeMatch("否");
                        }else{
                            exportResp.setDecorationBailFeeMatch("是");
                        }
                    }

                    exportResp.setPosRentFeePolicy("-");
                    exportResp.setPosRentFee(contractRent.getPosRentFee());
                    exportResp.setPosRentFeeMatch("-");

                    exportResp.setPosBailFeePolicy("-");
                    exportResp.setPosBailFee(contractRent.getPosBailFee());
                    exportResp.setPosBailFeeMatch("-");

                    exportResp.setDecorationPropertyFeePolicy("-");
                    exportResp.setDecorationPropertyFee(contractRent.getDecorationPropertyFee());
                    exportResp.setDecorationPropertyFeeMatch("-");

                    exportResp.setDecorationOperateFeePolicy("-");
                    exportResp.setDecorationOperateFee(contractRent.getDecorationOperateFee());
                    exportResp.setDecorationOperateFeeMatch("-");

                    exportResp.setCleanFeePolicy("-");
                    exportResp.setCleanFee(contractRent.getCleanFee());
                    exportResp.setCleanFeeMatch("-");

                    // 租赁类型
                    if (rentType!=null){
                        if (rentType==1){
                            exportResp.setRentType("纯租");
                            exportResp.setPointsMode(null);
                            exportResp.setSettlementDate(null);
                            exportResp.setGuarantSaleContract(null);
                            exportResp.setMonthFeePercentListPolicy(null);
                            exportResp.setMonthFeePercentList(null);
                            exportResp.setMonthFeePercentListMatch(null);
                        }else if (rentType==2){
                            exportResp.setRentType("纯扣");
                            exportResp.setRentIncreaseMode(null);
                            exportResp.setIncrementalRate(null);
                            exportResp.setIncrementalRateList(null);
                            exportResp.setIncrementalRateListMatch(null);
                            exportResp.setIncrementalFeePolicy(null);
                            exportResp.setIncrementalFee(null);
                            exportResp.setIncrementalFeeMatch(null);
                            exportResp.setGuarantSaleContract(null);
                        }else if (rentType==3){
                            exportResp.setRentType("保底扣（租金）");
                            exportResp.setGuarantSaleContract(null);
                        }else if (rentType==4){
                            exportResp.setRentType("保底扣（租金+管理费）");
                        }
                    }

                    // 审批状态
                    Integer approveStatus = contractItem.getApproveStatus();
                    if (approveStatus!=null){
                        if (approveStatus==2){
                            exportResp.setApproveStatus("审批中");
                        }else if (approveStatus==3){
                            exportResp.setApproveStatus("已通过");
                        }else if (approveStatus==4){
                            exportResp.setApproveStatus("已驳回");
                        }
                    }
                    exportResp.setApproveDate(contractItem.getApproveDate());
                    exportResp.setArchiveDate(contractItem.getArchiveDate());
                    exportRespList.add(exportResp);
                    log.info("合同解析解析正常:{}", getResp.getContractCode());
                }
            }
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String timeStr = DateUtil.date().toString("yyyyMMddHHmmss");
            String fileName = "合同传签台账-"+timeStr;
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");
            EasyExcel.write(response.getOutputStream(), CommerceContractExportResp.class).registerWriteHandler(horizontalCellStyleStrategy).sheet("合同传签台账").doWrite(exportRespList);
        } catch (Exception ex) {
            throw new ServiceException("合同传签台账导出错误：" + ex.getMessage());
        }
    }

    @Override
    public boolean checkDate(CommerceContractCheckReq checkReq) {
        if (checkReq.getRoomId()==null) {
            throw new ServiceException("铺位信息不能为空");
        }
        if(checkReq.getRentStartDate()==null){
            throw new ServiceException("租赁开始日期不能为空");
        }
        //校验是否有撤场流程。 有必须都要通过才开始校验时间。
        CommerceEntexitListDto entexitQuery=new CommerceEntexitListDto();
        entexitQuery.setShopId(checkReq.getRoomId());
        List<CommerceEntexitListVo> list = commerceEntexitProvider.listBase(entexitQuery);
        if(CollectionUtils.isNotEmpty(list)){
            List<CommerceEntexitListVo> entexitList = list.stream().filter(item -> item.getApproveStatus()==null|| item.getApproveStatus()!= 3).collect(Collectors.toList());
            if(CollectionUtils.isNotEmpty(entexitList)){
                throw new ServiceException("该店铺有撤场流程未通过，请先通过撤场流程");
            }
        }else{
            //没有撤场数据认为是新创建的铺位。
            return true;
        }
        CommerceEntexitGetVo lastEntexitByRoomId = commerceEntexitProvider.getLastEntexitByRoomId(checkReq.getRoomId());

        //校验租赁开始日期必须大于撤场日期.
        LocalDate rentStartDate = checkReq.getRentStartDate();
        LocalDate lastExitDate = lastEntexitByRoomId.getExitDate();
        if (!rentStartDate.isAfter(lastExitDate)) {
            throw new ServiceException("租赁开始日期["+rentStartDate+"]必须晚于最近撤场日期["+lastExitDate+"]");
        }
        return true;
    }

    @Override
    public List<CommerceChargeContractListItem> listByReceivable(CommerceChargeContractListReq listReq) {
        OptUser user = ApiUtils.getUser(OptUser.class);
        //正铺数据
        CommerceChargeContractListDto dto=BeanUtils.copyProperties(listReq, CommerceChargeContractListDto.class);
        dto.setTenantId(user.getTenantId());
        List<CommerceChargeContractListVo> commerceChargeContractListVos;
        if(listReq.getType()==0){
            commerceChargeContractListVos = commerceContractProvider.listByReceivable(dto);
        }else{
            commerceChargeContractListVos = advertContractProvider.listByReceivable(dto);
        }
        return BeanUtils.copyProperties(commerceChargeContractListVos, CommerceChargeContractListItem.class);
    }

    @Override
    public CommerceContractSupplierResp listSupplierByBrand(Long brandId) {
        CommerceContractSupplierResp resp=new CommerceContractSupplierResp();
        CommerceChargeContractListDto dto=new CommerceChargeContractListDto();
        dto.setBrandId(brandId);
        List<CommerceChargeContractListVo> commerceChargeContractListVos = commerceContractProvider.listByReceivable(dto);

        // 获取当前日期
        boolean brandStatus=true;
        LocalDate currentDate = LocalDate.now();
        if(CollectionUtils.isNotEmpty(commerceChargeContractListVos)){
            for (CommerceChargeContractListVo item : commerceChargeContractListVos) {
                LocalDate rentStartDate = item.getRentStartDate();
                long daysBetween = ChronoUnit.DAYS.between(currentDate,rentStartDate );
                if (daysBetween > 30) {
                    brandStatus = false;
                }
            }
            resp.setSupplierList(BeanUtils.copyProperties(commerceChargeContractListVos, CommerceContractSupplierItem.class));
            resp.setBrandStatus(brandStatus);
        }else{
            resp.setBrandStatus(true);
            resp.setSupplierList(new ArrayList<>());
        }
        // 设置响应结果
        return resp;
    }

   @Override
    public CommerceContractAiReviewSubmitResp aiReviewSubmit(CommerceContractAiReviewSubmitReq submitReq) {
        
        // 1. 获取AI返回的json
        String difyJsonStr = getDifyJson(submitReq);
        JSONObject aiJson = JSON.parseObject(difyJsonStr);
        // 2. 获取合同详情
        String contractCode = submitReq.getContractCode();
        CommerceContractCodeGetReq getReq = new CommerceContractCodeGetReq();
        getReq.setContractCode(contractCode);
        CommerceContractGetResp contract = getByContractCode(getReq);
        log.info("获取合同详情{}",contract.toString());
       
        Map<String, Integer> CONTRACT_TYPE_REVERSE_MAP = new HashMap<>();
        CONTRACT_TYPE_REVERSE_MAP.put("新签", 1);
        CONTRACT_TYPE_REVERSE_MAP.put("续签", 2);

        // 如果AI返回的json中没有“保证金是否突破”，则默认加上“否”
        if (!aiJson.containsKey("保证金是否突破")) {
            aiJson.put("保证金是否突破", "否");
        }

        // 3. 字段比对
        List<Map<String, Object>> diffList = new ArrayList<>();
        for (Map.Entry<String, String> entry : CommerceContractAiReviewSubmitReq.FIELD_MAP.entrySet()) {
            String cnKey = entry.getKey();
            String javaField = entry.getValue();
            Object aiValue = aiJson.get(cnKey);

            // 获取合同详情字段值（支持主对象、contractInfo、contractRent等多层）
            Object contractValue = getFieldValue(contract, javaField, cnKey);

//            log.debug("字段比对 - 字段名：{}，AI值：{}，合同值：{}", cnKey, aiValue, contractValue);

            if ("保证金是否突破".equals(cnKey)) {
                //系统判断是否突破
                boolean isBreakthrough = checkBailBreakthrough(contract);
                String expectedValue = isBreakthrough ? "是" : "否";
                if (aiValue != null && !String.valueOf(aiValue).equals(expectedValue)) {

                    Map<String, Object> diff = new HashMap<>();
                    diff.put("field", cnKey);
                    diff.put("aiValueStr", javaField);
                    diff.put("aiValue", aiValue.equals(expectedValue) ? "是" : "否");
                    diff.put("contractValue", expectedValue);
                    diffList.add(diff);
                    log.info("保证金是否突破字段比对不一致 - AI值：{}，预期值：{}", aiValue, expectedValue);
                }
            } else if ("签约类型".equals(cnKey)) {
                // AI传的是文字，转成数字再比对
                Integer aiType = aiValue != null ? CONTRACT_TYPE_REVERSE_MAP.get(aiValue.toString()) : null;
                Integer contractType = contractValue != null ? Integer.valueOf(contractValue.toString()) : null;
                if (aiType == null || !aiType.equals(contractType)) {
                    Map<String, Object> diff = new HashMap<>();
                    diff.put("field", cnKey);
                    diff.put("aiValueStr", javaField);
                    diff.put("aiValue", aiValue);
                    diff.put("contractValue", contractValue);
                    diffList.add(diff);
//                    log.info("签约类型字段比对不一致 - AI值：{}，合同值：{}", aiValue, contractValue);
                }
            } else {
                    // 检查是否是数值型字段
                if (isNumericField(javaField)) {
                    // 数值型字段，转换为BigDecimal进行数值比对
                    BigDecimal aiDecimal = convertToBigDecimal(aiValue);
                    BigDecimal contractDecimal = convertToBigDecimal(contractValue);

                    // 使用 compareTo 进行数值比对 (0 表示相等)
                    if (aiDecimal.compareTo(contractDecimal) != 0) {
                        Map<String, Object> diff = new HashMap<>();
                        diff.put("field", cnKey);
                        diff.put("aiValueStr", javaField);
                        diff.put("aiValue", aiValue); // diffList中显示AI的原始值
                        diff.put("contractValue", contractValue); // diffList中显示合同的原始值
                        diffList.add(diff);
                        log.info("数值字段比对不一致 - 字段：{}，AI值：{} ({})，合同值：{} ({})",
                                cnKey, aiValue, aiDecimal, contractValue, contractDecimal);
                    }
                } else {
                    // 非数值型字段，进行字符串比对
                    String aiValueStr = aiValue != null ? String.valueOf(aiValue) : null;
                    String contractValueStr = contractValue != null ? String.valueOf(contractValue) : null;

                    // 只有当两者都有值且不相等，或者一个有值另一个为null时才算差异
                    if (!Objects.equals(aiValueStr, contractValueStr)) {
                        Map<String, Object> diff = new HashMap<>();
                        diff.put("field", cnKey);
                        diff.put("aiValueStr", javaField);
                        diff.put("aiValue", aiValue);
                        diff.put("contractValue", contractValue);
                        diffList.add(diff);
                    }
                }
            }
        }

        // 4. 组装返回
        CommerceContractAiReviewSubmitResp resp = new CommerceContractAiReviewSubmitResp();
        if (diffList.isEmpty()) {
            resp.setStatus(ReviewStatus.PASSED.getCode());
            resp.setMessage(ReviewStatus.PASSED.getName());
        } else {
            resp.setStatus(ReviewStatus.FAILED.getCode());
            resp.setMessage(ReviewStatus.FAILED.getName());
            resp.setDiffList(diffList);
        }

        // 5. 存储userInput
        try {
            CommerceContractUpdateUserInputDto dto = new CommerceContractUpdateUserInputDto();
            dto.setContractCode(submitReq.getContractCode());
            dto.setUserInput(submitReq.getUserInput());
            commerceContractProvider.updateUserInput(dto);
        } catch (Exception e) {
            log.error("存储userInput失败，但不影响主流程: {}", e.getMessage());
            // 不抛出异常，让主流程继续执行
        }

        return resp;
    }

    private boolean isNumericField(String javaField) {
        Set<String> numericFields = new HashSet<>(Arrays.asList(
            // 大概是上面的一些数值类型
             "rentArea", "monthPriceContract", "monthOperatePriceContract", "monthManagePriceContract", "otherPriceContract", "otherFeeContract",
              "feeFreeContract", "compositeManageFeeContract", "rentBailFeeContract", "decorationBailFeeContract",
            "posRentFee", "posBailFee", "decorationPropertyFee", "decorationOperateFee", "cleanFee"
        ));
        return numericFields.contains(javaField);
    }

    private String getDifyJson(CommerceContractAiReviewSubmitReq submitReq) {

        String user_input = submitReq.getUserInput();
        String rule = CommerceContractAiReviewSubmitReq.RULE_JSON;

        JSONObject inputsObj = new JSONObject();
        inputsObj.put("user_input", user_input);
        inputsObj.put("rule", rule);

        JSONObject requestObj = new JSONObject();
        requestObj.put("inputs", inputsObj);
        requestObj.put("user", "abc-123");

        String json = requestObj.toJSONString();

        HttpResponse response = HttpRequest.post(difyUrl)
                .header("Authorization", "Bearer " + apiKey)
                .header("Content-Type", "application/json")
                .body(json)
                .timeout(100000)
                .execute();
        if (response.isOk()) {
            JSONObject respObj = JSON.parseObject(response.body());
            JSONObject dataObj = respObj.getJSONObject("data");
            if (dataObj != null) {
                JSONObject outputsObj = dataObj.getJSONObject("outputs");
                if (outputsObj != null) {
                    String answer = outputsObj.getString("答案");
                    if (answer != null) {
                        // 提取```json ... ```中的内容
                        int start = answer.indexOf("```json");
                        int end = answer.indexOf("```", start + 1);
                        if (start != -1 && end != -1 && end > start) {
                            String jsonStr = answer.substring(start + 7, end).trim();
                            // 格式化json
                            try {
                                JSONObject obj = JSON.parseObject(jsonStr);
                                return obj.toJSONString();
                            } catch (Exception e) {
                                return jsonStr; // 返回原始json字符串
                            }
                        }
                    }
                }
            }
            return "{}";
        } else {
            return "{\"error\":\"请求失败\"}";
        }
    }

    @Override
    public CommerceContractAiReviewCancelResp aiReviewCancel(CommerceContractAiReviewCancelReq cancelReq) {
        String contractCode = cancelReq.getContractCode();
        CommerceContractAiReviewCancelResp resp = new CommerceContractAiReviewCancelResp();
        resp.setContractCode(contractCode);
        resp.setStatus(ReviewStatus.CANCELLED.getCode());
        return resp;
    }

    // 检查保证金是否突破
    private boolean checkBailBreakthrough(CommerceContractGetResp contract) {
        if (contract != null && contract.getContractRent() != null) {
            // 使用getFieldValue方法获取字段值，统一字段获取逻辑
            Object propertyManagePolicy = getFieldValue(contract, "propertyManageBailFee", "物业管理费保证金（元）");
            Object propertyManageCondition = getFieldValue(contract, "propertyManageBailFeeContract", "物业管理费保证金");
            
            Object operateManagePolicy = getFieldValue(contract, "operateManageBailFee", "运营管理费保证金（元）");
            Object operateManageCondition = getFieldValue(contract, "operateManageBailFeeContract", "运营管理费保证金");
            
            Object commonPolicy = getFieldValue(contract, "commonBailFee", "公共事业保证金（元）");
            Object commonCondition = getFieldValue(contract, "commonBailFeeContract", "公共事业保证金");

            // 统一处理空值，用0补足
            int propertyManagePolicyValue = convertToInt(propertyManagePolicy);
            int propertyManageConditionValue = convertToInt(propertyManageCondition);
            int operateManagePolicyValue = convertToInt(operateManagePolicy);
            int operateManageConditionValue = convertToInt(operateManageCondition);
            int commonPolicyValue = convertToInt(commonPolicy);
            int commonConditionValue = convertToInt(commonCondition);

            // 检查三项保证金是否突破
            boolean propertyManageBreakthrough = propertyManagePolicyValue > propertyManageConditionValue;
            boolean operateManageBreakthrough = operateManagePolicyValue > operateManageConditionValue;
            boolean commonBreakthrough = commonPolicyValue > commonConditionValue;

            // 只要有一项突破，就返回true
            return propertyManageBreakthrough || operateManageBreakthrough || commonBreakthrough;
        }
        return false;
    }

    // 辅助方法：多层对象取值
    private Object getFieldValue(CommerceContractGetResp contract, String javaField, String cnKey) {
        if (contract == null) {
            return null;
        }

        try {
            // 先查主对象
            java.lang.reflect.Field field = CommerceContractGetResp.class.getDeclaredField(javaField);
            field.setAccessible(true);
            return field.get(contract);
        } catch (Exception e) {
            // 查contractInfo
            try {
                Object info = contract.getContractInfo();
                if (info != null) {
                    java.lang.reflect.Field field = info.getClass().getDeclaredField(javaField);
                    field.setAccessible(true);
                    return field.get(info);
                }
            } catch (Exception ignore) {}
            
            // 查contractRent
            try {
                Object rent = contract.getContractRent();
                if (rent != null) {
                    java.lang.reflect.Field field = rent.getClass().getDeclaredField(javaField);
                    field.setAccessible(true);
                    return field.get(rent);
                }
            } catch (Exception ignore) {}
        }
        return null;
    }

    // 新增：统一的值转换为BigDecimal方法，处理null为BigDecimal.ZERO
    private BigDecimal convertToBigDecimal(Object value) {
        if (value == null) {
            return BigDecimal.ZERO; // null转为0
        }
        if (value instanceof BigDecimal) {
            return (BigDecimal) value;
        }
        if (value instanceof Number) {
            return new BigDecimal(String.valueOf(value)); // 通过String避免精度问题
        }
        if (value instanceof String) {
            String str = (String) value;
            if (StringUtils.isBlank(str) || "null".equalsIgnoreCase(str)) {
                return BigDecimal.ZERO; // 空字符串或"null"字符串转为0
            }
            try {
                // 尝试转换字符串为BigDecimal
                return new BigDecimal(str);
            } catch (NumberFormatException e) {
                // 转换失败，认为是无效数值，转为0
                log.warn("无法将字符串 '{}' 转换为BigDecimal，默认转为0", str);
                return BigDecimal.ZERO;
            }
        }
        // 其他未知类型也转为0
        log.warn("未知类型 '{}' 无法转换为BigDecimal，默认转为0", value.getClass().getName());
        return BigDecimal.ZERO;
    }


    // 新增：统一的值转换方法
    private int convertToInt(Object value) {
        if (value == null) {
            return 0;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        if (value instanceof String) {
            try {
                return Integer.parseInt((String) value);
            } catch (NumberFormatException e) {
                log.warn("无法将字符串 '{}' 转换为int，默认转为0", value);
                return 0;
            }
        }
        log.warn("未知类型 '{}' 无法转换为int，默认转为0", value.getClass().getName());
        return 0;
    }

}
