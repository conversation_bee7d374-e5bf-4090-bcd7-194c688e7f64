package com.seewin.som.commerce.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson2.JSONObject;
import com.seewin.consumer.data.ApiResponse;
import com.seewin.ems.energy.provider.ElectricityDataProvider;
import com.seewin.ems.energy.req.ElectricityDataByDateDto;
import com.seewin.ems.energy.resp.ElectricityDataWithRoomVo;
import com.seewin.som.commerce.provider.CommerceEntexitProvider;
import com.seewin.som.commerce.provider.CommerceInspectionScopeProvider;
import com.seewin.som.commerce.provider.CommerceStoreManageDetailProvider;
import com.seewin.som.commerce.provider.CommerceStoreManageProvider;
import com.seewin.som.commerce.req.*;
import com.seewin.som.commerce.resp.*;
import com.seewin.som.commerce.vo.req.*;
import com.seewin.som.iot.provider.IotFlowDataProvider;
import com.seewin.som.iot.req.IotFlowDataEnterOutDto;
import com.seewin.som.iot.resp.IotFlowDataEnterOutVo;
import com.seewin.som.message.provider.MsgLogProvider;
import com.seewin.som.message.req.MsgLogAddDto;
import com.seewin.som.report.provider.ReportElectricityDataProvider;
import com.seewin.som.report.req.ReportElectricityDataAddDto;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.req.RoomsListDto;
import com.seewin.som.space.resp.RoomsListVo;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.JsonUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.math.MathContext;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <p>
 * 开闭店管理电表数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Slf4j
@Tag(name = "开闭店管理电表数据表")
@RestController
@RequestMapping("commerceStoreElectricity")
public class CommerceStoreElectricityController {

	@DubboReference(providedBy = "som-space-mgt")
	private RoomsProvider roomProvider;

	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceStoreManageProvider commerceStoreManageProvider;

	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceStoreManageDetailProvider commerceStoreManageDetailProvider;

	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceEntexitProvider commerceEntexitProvider;

	@DubboReference(providedBy = "som-report-mgt")
	private ReportElectricityDataProvider reportElectricityDataDataProvider;

	@DubboReference(providedBy = "ems-energy-mgt")
	private ElectricityDataProvider electricityDataProvider;

	@DubboReference(providedBy = "som-iot-mgt")
	private IotFlowDataProvider flowDataProvider;

	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceInspectionScopeProvider commerceInspectionScopeProvider;

	@DubboReference(providedBy = "som-message-mgt")
	private MsgLogProvider msgLogProvider;

	@Operation(summary = "同步一段范围的电表和客流数据",description = "权限码：opt:commerce:commerceStoreElectricity:scopeData")
	@PostMapping("scopeData")
	public ApiResponse<Boolean> scopeData(@RequestBody @Valid CommerceStoreManageListReq req) {
		log.info("同步一段范围的电表和客流数据:{}", JsonUtils.toJson(req));
		ApiResponse<Boolean> result = new ApiResponse<>();
		result.setData(Boolean.TRUE);

		// 同步全生命周期表到开闭店管理表标识
		Integer syncFlag = 0;
		// 是否指定项目
		Long paramTenantId = null;
		String param = req.getOrgFname();
		if (StringUtils.isNotBlank(param)) {
			String[] split = param.split(",");
			if (split.length > 2) {
				paramTenantId = Long.valueOf(split[2]);
			}
			if (split.length > 3) {
				syncFlag = Integer.valueOf(split[3]);
			}
		}

		// 同步全生命周期店铺到开闭店管理店铺
		if (syncFlag==1){
			syncRoomData(paramTenantId);
			return result;
		}

		// 获取开闭店管理数据
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (paramTenantId!=null){
			storeDto.setTenantId(paramTenantId);
		}
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSize:{}", storeList.size());
		if (CollectionUtil.isNotEmpty(storeList)){
			Set<Long> tenantIdSet = storeList.stream().map(f -> f.getTenantId()).collect(Collectors.toSet());
			for (Long tenantId : tenantIdSet) {
				scopeTenantData(param, tenantId);
			}
		}

		log.info("同步一段范围的电表和客流数据成功...");

		return result;
	}

	public void scopeTenantData(String param, Long tenantId) {
		ApiResponse<Boolean> result = new ApiResponse<>();
		result.setData(Boolean.TRUE);

		Integer syncFlag = 0;
		// 获取拉取时间范围
		LocalDate endDay = LocalDate.now().minusDays(1);
		LocalDate startDay = LocalDate.now().minusDays(7);
		if (StringUtils.isNotBlank(param)) {
			String[] split = param.split(",");
			startDay = LocalDate.parse(split[0]);
			endDay = LocalDate.parse(split[1]);
			if (split.length > 3) {
				syncFlag = Integer.valueOf(split[3]);
			}
		}

		// 获取日期列表
		List<LocalDate> dateList = new ArrayList<>();
		dateList.add(startDay);
		LocalDate tempDate = startDay.plusDays(1);
		while (tempDate.isBefore(endDay) || tempDate.isEqual(endDay)){
			dateList.add(tempDate);
			tempDate = tempDate.plusDays(1);
		}
		log.info("dateList:{}", dateList);

		// 获取所有店铺数据
		RoomsListDto roomDto = new RoomsListDto();
		if (tenantId!=null){
			roomDto.setTenantId(tenantId);
		}
		List<RoomsListVo> roomList = roomProvider.list(roomDto);
		log.info("roomListSize:{}", roomList.size());

		Map<Long, RoomsListVo> roomMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(roomList)) {
			roomMap = roomList.stream().collect(Collectors.toMap(RoomsListVo::getId, Function.identity()));
		}

		// 先同步客流数据
		scopeFlowData(param, tenantId);

		// 只同步客流数据
		if (syncFlag==2){
			return;
		}

		// 再同步电表数据
		// 存储每个店铺上一条记录功率
		Map<Long, BigDecimal> standPowerMap = new HashMap<>();
		Map<Long, BigDecimal> timePowerMap = new HashMap<>();
		Map<Long, BigDecimal> addPowerMap = new HashMap<>();

		// 存储每个店铺的所有标准差、开店时间、闭店时间
		Map<Long, List<BigDecimal>> standardListMap = new HashMap<>();
		Map<Long, List<LocalDateTime>> openTimeListMap = new HashMap<>();
		Map<Long, List<LocalDateTime>> closeTimeListMap = new HashMap<>();

		for (LocalDate localDate : dateList) {
			// 获取电表数据
			ElectricityDataByDateDto getDataDto = new ElectricityDataByDateDto();
			if (tenantId!=null){
				getDataDto.setTenantId(tenantId);
			}
			getDataDto.setStartTime(localDate.atStartOfDay());
			getDataDto.setEndTime(localDate.atTime(23, 59, 59));

			log.info("getDataDto:{}", JsonUtils.toJson(getDataDto));
			List<ElectricityDataWithRoomVo> elecDataList = electricityDataProvider.getElectricityData(getDataDto);
			log.info("elecDataListSize:{}", elecDataList.size());

			if (CollectionUtil.isNotEmpty(elecDataList)){
				// 按门店进行分组
				Map<Long, List<ElectricityDataWithRoomVo>> electricityMap = elecDataList.stream().collect(Collectors.groupingBy(ElectricityDataWithRoomVo::getRoomId));
				for(Map.Entry<Long, List<ElectricityDataWithRoomVo>> entry : electricityMap.entrySet()){
					Long roomId = entry.getKey();
					List<ElectricityDataWithRoomVo> roomDataList = entry.getValue().stream()
							.sorted(Comparator.comparing(ElectricityDataWithRoomVo::getReadTime))
							.collect(Collectors.toList());

					log.info("roomId:{}", roomId);
					// 获取店铺信息
					RoomsListVo roomsVo = roomMap.get(roomId);
					if (roomsVo!=null){
						log.info("roomNo:{}", roomsVo.getName());

						// 获取店铺历史的阀值,开店时间,闭店时间列表
						List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
						List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
						List<BigDecimal> standardValueList = standardListMap.get(roomId);
						if (CollectionUtil.isEmpty(openTimeList)){
							openTimeList = new ArrayList<>();
						}
						if (CollectionUtil.isEmpty(closeTimeList)){
							closeTimeList = new ArrayList<>();
						}
						if (CollectionUtil.isEmpty(standardValueList)){
							standardValueList = new ArrayList<>();
						}

						// 先算出当天的标准差
						List<BigDecimal> powerDiffValueList = new ArrayList<>();
						for (ElectricityDataWithRoomVo dataVo : roomDataList) {
							// 获取店铺上一个功率
							BigDecimal standLastValue = standPowerMap.get(roomId);
							BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
							if (standLastValue==null){
								powerDiffValueList.add(BigDecimal.ZERO);
							}else {
								BigDecimal powerDiffValue = powerValue.subtract(standLastValue);
								if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
									powerDiffValue = BigDecimal.ZERO;
								}
								powerDiffValueList.add(powerDiffValue);
							}
							standPowerMap.put(roomId, powerValue);
						}
						log.info("powerDiffValueList:{}", powerDiffValueList);
						BigDecimal standardValue = getStandardValue(powerDiffValueList);
						log.info("standardValue:{}", standardValue);

						// 存储每一天的标准差
						standardValueList.add(standardValue);
						standardListMap.put(roomId, standardValueList);

						// 利用标准差再算出每天的开店时间和闭店时间是哪条数据
						Integer openTime = null;
						Integer closeTime = null;
						for (int i = 0; i < roomDataList.size(); i++) {
							// 获取店铺上一个功率
							BigDecimal lastTimeValue = timePowerMap.get(roomId);
							// 获取和上一条数据的差值
							BigDecimal powerDiffValue;
							ElectricityDataWithRoomVo dataVo = roomDataList.get(i);
							BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
							if (lastTimeValue==null){
								powerDiffValue = BigDecimal.ZERO;
							}else {
								powerDiffValue = powerValue.subtract(lastTimeValue);
								if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
									powerDiffValue = BigDecimal.ZERO;
								}
							}
							timePowerMap.put(roomId, powerValue);
							// 还没有开店时间
							if (openTime==null){
								if (powerDiffValue.compareTo(standardValue)>0){
									if (i==0){
										openTime = 0;
									}else {
										openTime = i-1;
									}
								}
							}else {
								// 有了开业时间求闭店时间
								if (closeTime==null && powerDiffValue.compareTo(standardValue)<0){
									if (i==0){
										closeTime = 0;
									}else {
										closeTime = i-1;
									}
								}
							}
						}

						log.info("openTime:{}", openTime);
						log.info("closeTime:{}", closeTime);

						// 入库当天的电表数据
						for (int i = 0; i < roomDataList.size(); i++) {
							ReportElectricityDataAddDto electricityAddDto = new ReportElectricityDataAddDto();
							// 获取店铺上一个功率
							BigDecimal lastAddValue = addPowerMap.get(roomId);

							ElectricityDataWithRoomVo dataVo = roomDataList.get(i);

							electricityAddDto.setDeviceId(dataVo.getDeviceSn());
							electricityAddDto.setDeviceName(dataVo.getDeviceName());

							electricityAddDto.setTenantId(roomsVo.getTenantId());
							electricityAddDto.setRoomId(roomId);
							electricityAddDto.setRoomNo(roomsVo.getName());
							electricityAddDto.setStoreId(roomsVo.getStoreId());
							electricityAddDto.setBrandId(roomsVo.getBrandId());
							electricityAddDto.setBrandName(roomsVo.getBrandName());

							electricityAddDto.setCollectTime(dataVo.getReadTime());
							electricityAddDto.setCollectDiffTime(null);

							BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
							electricityAddDto.setPowerValue(powerValue);

							// 获取和上一条记录的差值
							if (lastAddValue==null){
								electricityAddDto.setPowerDiffValue(BigDecimal.ZERO);
							}else {
								BigDecimal powerDiffValue = powerValue.subtract(lastAddValue);
								if (powerDiffValue.compareTo(BigDecimal.ZERO)<0){
									powerDiffValue = BigDecimal.ZERO;
								}
								electricityAddDto.setPowerDiffValue(powerDiffValue);
							}
							addPowerMap.put(roomId, powerValue);

							electricityAddDto.setCreateBy(roomsVo.getCreateBy());
							electricityAddDto.setCreateUser(roomsVo.getCreateUser());
							electricityAddDto.setCreateUserName(roomsVo.getCreateUserName());

							if (closeTime!=null && closeTime == i){
								electricityAddDto.setStoreFlag(1);
								closeTimeList.add(dataVo.getReadTime());
								closeTimeListMap.put(roomId, closeTimeList);
							}

							if (openTime!=null && openTime == i){
								electricityAddDto.setStoreFlag(0);
								openTimeList.add(dataVo.getReadTime());
								openTimeListMap.put(roomId, openTimeList);
							}
							reportElectricityDataDataProvider.add(electricityAddDto);
						}
					}
				}
			}
		}

		// 获取所有的开闭店管理的店铺列表
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (tenantId!=null){
			storeDto.setTenantId(tenantId);
		}
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSize:{}", storeList.size());

		// 给每个店铺赋值 参考值、开店时间、闭店时间、开闭店时长
		if (CollectionUtil.isNotEmpty(storeList)){
			for (CommerceStoreManageListVo storeVo : storeList) {

				CommerceStoreManageEditDto editDto = new CommerceStoreManageEditDto();
				editDto.setId(storeVo.getId());

				Long roomId = storeVo.getRoomId();

				// 求所有标准差的平均值
				List<BigDecimal> standardList = standardListMap.get(roomId);
				BigDecimal referValue = getStandardAvgValue(standardList);
				editDto.setReferValue(referValue);

				// 求开店时间的平均值
				List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
				String referOpenTime = getTimeAvgValue(openTimeList);
				editDto.setReferOpenTime(referOpenTime);

				// 求开店时间的平均值
				List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
				String referCloseTime = getTimeAvgValue(closeTimeList);
				editDto.setReferCloseTime(referCloseTime);

				// 获取开店时长
				BigDecimal timeLength = getTimeLength(referOpenTime, referCloseTime);
				editDto.setReferOpenLength(timeLength);

				// 赋值参考类型为电表
				if (referValue!=null && referValue.compareTo(BigDecimal.ZERO) > 0){
					log.info("standardList:{}", standardList);
					log.info("referValueResult:{}", referValue);
					log.info("openTimeList:{}", openTimeList);
					log.info("closeTimeList:{}", closeTimeList);
					log.info("timeLength:{}", timeLength);
					editDto.setReferType(0);
					commerceStoreManageProvider.edit(editDto);
				}
			}
		}

	}


	@Operation(summary = "每隔一个小时同步电表数据",description = "权限码：opt:commerce:commerceStoreElectricity:syncData")
	@PostMapping("syncData")
	public ApiResponse<Boolean> syncData(@RequestBody @Valid CommerceStoreManageListReq req) {
		log.info("每隔一个小时同步电表数据:{}", JsonUtils.toJson(req));
		ApiResponse<Boolean> result = new ApiResponse<>();
		result.setData(Boolean.TRUE);

		Long tenantId = null;
		LocalDateTime previousHour = LocalDateTime.now().minusHours(1);
		// 前一个小时的开始时间
		LocalDateTime startTime = previousHour.withMinute(0).withSecond(0);
		// 前一个小时的结束时间
		LocalDateTime endTime = previousHour.withMinute(59).withSecond(59);
		String param = req.getOrgFname();
		if (StringUtils.isNotBlank(param)) {
			String[] split = param.split(",");
			startTime = LocalDateTime.parse(split[0], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			endTime = LocalDateTime.parse(split[1], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			if (split.length > 2) {
				tenantId = Long.valueOf(split[2]);
			}
		}
		log.info("startTime:{}", startTime);
		log.info("endTime:{}", endTime);

		if (startTime.getHour()==0){
			// 把项目今日的开店时间和状态赋为空
			log.info("置空今日的开店时间和状态...");
			commerceStoreManageProvider.emptyTimeAndStatus(tenantId);
		}

		// 先同步客流数据
		syncFlowData(req);

		// 日期时间格式
		DateTimeFormatter dateForMatter = DateTimeFormatter.ofPattern("HH:mm");

		// 获取全生命周期店铺数据
		RoomsListDto roomDto = new RoomsListDto();
		if (tenantId!=null){
			roomDto.setTenantId(tenantId);
		}
		List<RoomsListVo> roomList = roomProvider.list(roomDto);
		log.info("roomListSize:{}", roomList.size());

		// 获取开闭店管理数据
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (tenantId!=null){
			storeDto.setTenantId(tenantId);
		}
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSize:{}", storeList.size());

		Map<Long, RoomsListVo> roomMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(roomList)) {
			roomMap = roomList.stream().collect(Collectors.toMap(RoomsListVo::getId, Function.identity()));
		}

		Map<Long, CommerceStoreManageListVo> storeMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(storeList)) {
			storeMap = storeList.stream().collect(Collectors.toMap(CommerceStoreManageListVo::getRoomId, Function.identity()));
		}

		LocalDateTime createTimeStart = startTime.withHour(0).withMinute(0).withSecond(0);
		LocalDateTime createTimeEnd = endTime.withHour(23).withMinute(59).withSecond(59);
		log.info("createTimeStart:{}", createTimeStart);
		log.info("createTimeEnd:{}", createTimeEnd);

		// 获取电表数据
		ElectricityDataByDateDto getDataDto = new ElectricityDataByDateDto();
		if (tenantId!=null){
			getDataDto.setTenantId(tenantId);
		}
		getDataDto.setStartTime(startTime);
		getDataDto.setEndTime(endTime);
		log.info("getDataDto:{}", getDataDto);
		List<ElectricityDataWithRoomVo> elecDataList = electricityDataProvider.getElectricityData(getDataDto);
		log.info("elecDataListSize:{}", elecDataList.size());

		if (CollectionUtil.isNotEmpty(elecDataList)){
			// 按门店进行分组
			Map<Long, List<ElectricityDataWithRoomVo>> electricityMap = elecDataList.stream().collect(Collectors.groupingBy(ElectricityDataWithRoomVo::getRoomId));
			for(Map.Entry<Long, List<ElectricityDataWithRoomVo>> entry : electricityMap.entrySet()){
				Long roomId = entry.getKey();
				List<ElectricityDataWithRoomVo> roomDataList = entry.getValue().stream()
						.sorted(Comparator.comparing(ElectricityDataWithRoomVo::getReadTime))
						.collect(Collectors.toList());

				log.info("roomId:{}", roomId);

				CommerceStoreManageListVo storeVo = storeMap.get(roomId);
				RoomsListVo roomsVo = roomMap.get(roomId);

				// 非电表数据直接下一条
				if (storeVo==null || roomsVo==null || storeVo.getReferType()==null || storeVo.getReferType()!=0){
					continue;
				}

				// 没有门店id或没有参考开店时间直接下一条
				if (StringUtils.isEmpty(roomsVo.getStoreId()) || StringUtils.isEmpty(storeVo.getReferOpenTime())){
					continue;
				}

				// 开闭店管理和明细id
				Long storeManageId = null;
				Long storeManageDetailId = null;

				// 参考值、参考开店时间、异常时间间隔
				BigDecimal referValue = null;
				String referOpenTime = null;
				Integer timePeriod = null;
				// 是否开启消息通知
				Integer messageNotify = null;

				// 门店状态
				String roomStatus = null;

				// 上一条功率值、上一条时间
				BigDecimal dbLastValue = null;
				String dbLastTime = null;

				// 开店时间、闭店时间
				String electricityOpenTime = null;
				String electricityCloseTime = null;

				if (storeVo!=null && roomsVo!=null){
					storeManageId = storeVo.getId();

					referValue = storeVo.getReferValue();
					referOpenTime = storeVo.getReferOpenTime();
					timePeriod = storeVo.getTimePeriod();
					messageNotify = storeVo.getMessageNotify();

					CommerceEntexitListDto dto = new CommerceEntexitListDto();
					dto.setShopId(roomsVo.getId());
					dto.setTenantId(roomsVo.getTenantId());
					dto.setSearchType(4);
					CommerceEntexitGetVo getVo = commerceEntexitProvider.get(dto);
					if (!ObjectUtils.isEmpty(getVo)) {
						roomStatus = getVo.getRoomStatus();
					}

					CommerceStoreManageDetailListDto detailDto = new CommerceStoreManageDetailListDto();
					detailDto.setStoreManageId(storeManageId);
					detailDto.setCreateTimeStart(createTimeStart);
					detailDto.setCreateTimeEnd(createTimeEnd);
					CommerceStoreManageDetailListVo detailListVo = commerceStoreManageDetailProvider.getDetailData(detailDto);
					if (detailListVo==null){
						// 入库明细数据
						CommerceStoreManageDetailAddDto detailAddDto = new CommerceStoreManageDetailAddDto();
						detailAddDto.setStoreManageId(storeManageId);
						detailAddDto.setStoreId(roomsVo.getStoreId());
						detailAddDto.setRoomStatus(roomStatus);
						detailAddDto.setBrandId(roomsVo.getBrandId());
						detailAddDto.setBrandName(roomsVo.getBrandName());
						detailAddDto.setReferOpenTime(storeVo.getReferOpenTime());
						detailAddDto.setReferCloseTime(storeVo.getReferCloseTime());
						detailAddDto.setReferOpenLength(storeVo.getReferOpenLength());
						detailAddDto.setStoreStatus(2);
						LocalDateTime createTimeAddMinutes = createTimeStart.plusMinutes(1);
						detailAddDto.setCreateTime(createTimeAddMinutes);
						CommerceStoreManageDetailAddVo addVo = commerceStoreManageDetailProvider.add(detailAddDto);
						storeManageDetailId = addVo.getId();
						log.info("入库明细数据:{}", storeManageDetailId);
					}else {
						storeManageDetailId = detailListVo.getId();
						electricityOpenTime = storeVo.getElectricityOpenTime();
						electricityCloseTime = detailListVo.getElectricityCloseTime();
						log.info("已有明细数据:{}", storeManageDetailId);
					}

					dbLastValue = storeVo.getLastPowerValue();
					dbLastTime = storeVo.getLastPowerTime();
				}

				for (int i = 0; i < roomDataList.size(); i++) {
					ElectricityDataWithRoomVo dataVo = roomDataList.get(i);
					if (storeVo!=null && roomsVo!=null){

						ReportElectricityDataAddDto electricityAddDto = new ReportElectricityDataAddDto();

						electricityAddDto.setDeviceId(dataVo.getDeviceSn());
						electricityAddDto.setDeviceName(dataVo.getDeviceName());

						electricityAddDto.setTenantId(roomsVo.getTenantId());
						electricityAddDto.setRoomId(roomId);
						electricityAddDto.setRoomNo(roomsVo.getName());
						electricityAddDto.setStoreId(roomsVo.getStoreId());
						electricityAddDto.setBrandId(roomsVo.getBrandId());
						electricityAddDto.setBrandName(roomsVo.getBrandName());

						LocalDateTime readTime = dataVo.getReadTime();
						String readTimeStr = readTime.format(dateForMatter);
						electricityAddDto.setCollectTime(readTime);
						electricityAddDto.setCollectDiffTime(null);

						BigDecimal powerValue = BigDecimal.valueOf(dataVo.getPower());
						electricityAddDto.setPowerValue(powerValue);

						BigDecimal diffValue = BigDecimal.ZERO;
						// 获取和上一条记录的差值
						if (dbLastValue==null){
							electricityAddDto.setPowerDiffValue(diffValue);
						}else {
							diffValue = powerValue.subtract(dbLastValue);
							if (diffValue.compareTo(BigDecimal.ZERO)<0){
								diffValue = BigDecimal.ZERO;
							}
							electricityAddDto.setPowerDiffValue(diffValue);
						}

						electricityAddDto.setCreateBy(roomsVo.getCreateBy());
						electricityAddDto.setCreateUser(roomsVo.getCreateUser());
						electricityAddDto.setCreateUserName(roomsVo.getCreateUserName());

						if (referValue!=null){
							// 还没有开店时间
							if (StringUtils.isEmpty(electricityOpenTime)){
								if (diffValue.compareTo(referValue)>0){
									// 如果开店时间为0点则不取上一条,取当前条
									if (startTime.getHour()==0){
										dbLastTime = readTimeStr;
									}
									// 获取开店是否异常
									Integer storeStatus = checkStoreStatus(referOpenTime, dbLastTime, timePeriod);
									// 开店异常则进行消息通知
									if (storeStatus==1 && messageNotify==1){
										sendMsg(roomsVo);
									}
									// 赋值开店时间
									CommerceStoreManageEditDto storeEditDto = new CommerceStoreManageEditDto();
									storeEditDto.setId(storeManageId);
									storeEditDto.setElectricityOpenTime(dbLastTime);
									storeEditDto.setStoreStatus(storeStatus);
									commerceStoreManageProvider.edit(storeEditDto);

									CommerceStoreManageDetailEditDto openTimeEdit = new CommerceStoreManageDetailEditDto();
									openTimeEdit.setId(storeManageDetailId);
									openTimeEdit.setElectricityOpenTime(dbLastTime);
									openTimeEdit.setStoreStatus(storeStatus);
									commerceStoreManageDetailProvider.edit(openTimeEdit);

									electricityOpenTime = dbLastTime;
								}
							}else {
								// 已有开店时间 赋值闭店时间
								if (electricityCloseTime==null && diffValue.compareTo(referValue)<0){
									// 获取开店时长
									BigDecimal timeLength = getTimeLength(electricityOpenTime, dbLastTime);
									CommerceStoreManageDetailEditDto openTimeEdit = new CommerceStoreManageDetailEditDto();
									openTimeEdit.setId(storeManageDetailId);
									openTimeEdit.setElectricityCloseTime(dbLastTime);
									openTimeEdit.setElectricityOpenLength(timeLength);
									commerceStoreManageDetailProvider.edit(openTimeEdit);

									electricityCloseTime = dbLastTime;
								}
							}
						}

						// 赋值上一个值和上一个时间
						dbLastValue = powerValue;
						dbLastTime = readTimeStr;

						reportElectricityDataDataProvider.add(electricityAddDto);
					}

				}

				// 更新店铺的最后值和最后时间
				CommerceStoreManageEditDto editDto = new CommerceStoreManageEditDto();
				editDto.setId(storeManageId);
				editDto.setLastPowerValue(dbLastValue);
				editDto.setLastPowerTime(dbLastTime);
				commerceStoreManageProvider.edit(editDto);
			}
		}

		return result;
	}

	/**
	 * 发送消息通知
	 * @param roomsVo
	 */
	private void sendMsg(RoomsListVo roomsVo) {
		new Thread(() -> {
			List<CommerceInspectionScopeListVo> inspecList = commerceInspectionScopeProvider.getInspectListByShopNo(roomsVo.getName(), roomsVo.getTenantId());
			if (!CollectionUtils.isEmpty(inspecList)){
				List<Long> inspectUserIdList = inspecList.stream().map(f -> f.getUserId()).collect(Collectors.toList());
				List<String> inspectUserNameList = inspecList.stream().map(f -> f.getRealName()).collect(Collectors.toList());
				MsgLogAddDto msgLogAddDto = new MsgLogAddDto("som");
				msgLogAddDto.setType(0);
				msgLogAddDto.setCode("store-manage");
				msgLogAddDto.setName("开闭店管理");
				// 您接收到一条新的storeId门店的开闭店异常信息
				msgLogAddDto.setContent("您接收到一条新的"+roomsVo.getStoreId()+"门店的开闭店异常信息");
				msgLogAddDto.setUserIds(inspectUserIdList);
				msgLogAddDto.setTargets(inspectUserNameList);
				msgLogAddDto.setSendStatus(1);
				msgLogAddDto.setTenantId(roomsVo.getTenantId());
				msgLogAddDto.setEntId(roomsVo.getEntId());
				// 模版参数
				Map<String, Object> templateParam = new HashMap<>();
				templateParam.put("storeId", roomsVo.getStoreId());
				msgLogAddDto.setTemplateParam(templateParam);
				msgLogAddDto.setUrlLink(JSONObject.toJSONString(templateParam));
				msgLogProvider.add(msgLogAddDto);
			}
		}).start();
	}

	/**
	 * 获取开店时长
	 * @param openTime
	 * @param closeTime
	 * @return
	 */
	private BigDecimal getTimeLength(String openTime, String closeTime) {
		if (StringUtils.isNotEmpty(openTime) && StringUtils.isNotEmpty(closeTime)){
			long openTotalMinutes = parseTimeToMinutes(openTime);
			long closeTotalMinutes = parseTimeToMinutes(closeTime);
			long minuteDifference = closeTotalMinutes - openTotalMinutes;
			if (minuteDifference<0){
				return BigDecimal.ZERO;
			}
			return new BigDecimal(minuteDifference)
					.divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
		}
		return null;
	}

	private long parseTimeToMinutes(String timeStr) {
		String[] parts = timeStr.split(":");
		int hours = Integer.parseInt(parts[0]);
		int minutes = Integer.parseInt(parts[1]);
		return hours * 60L + minutes;
	}

	/**
	 * 获取标准差平均值
	 * @param standardList
	 * @return
	 */
	private BigDecimal getStandardAvgValue(List<BigDecimal> standardList) {
		BigDecimal referValue = BigDecimal.ZERO;
		if (CollectionUtil.isNotEmpty(standardList)){
			BigDecimal sum = BigDecimal.ZERO;
			for (BigDecimal value : standardList) {
				sum = sum.add(value);
			}
			BigDecimal count = new BigDecimal(standardList.size());
			referValue = sum.divide(count, 2, RoundingMode.HALF_UP);
		}
		return referValue;
	}

	/**
	 * 获取时间平均值
	 * @param timeList
	 * @return
	 */
	private String getTimeAvgValue(List<LocalDateTime> timeList) {
		if (CollectionUtil.isNotEmpty(timeList)){
			// 求总分钟
			long totalMinutes = 0;
			for (LocalDateTime dateTime : timeList) {
				int hour = dateTime.getHour();
				int minute = dateTime.getMinute();
				totalMinutes += hour * 60 + minute;
			}
			// 计算平均分钟数
			long averageMinutes = totalMinutes / timeList.size();
			// 转换回小时和分钟
			int hours = (int) (averageMinutes / 60);
			int minutes = (int) (averageMinutes % 60);
			// 格式化为hh:mm
			return String.format("%02d:%02d", hours, minutes);
		}
		return null;
	}

	/**
	 * 获取标准差
	 * @param numbers 数值集合
	 * @return
	 */
	private static BigDecimal getStandardValue(List<BigDecimal> numbers) {
		if (numbers == null || numbers.isEmpty()) {
			return BigDecimal.ZERO;
		}
		int size = numbers.size();
		BigDecimal sum = numbers.stream()
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		BigDecimal mean = sum.divide(new BigDecimal(size), MathContext.DECIMAL128);
		BigDecimal sumOfSquares = numbers.stream()
				.map(num -> num.subtract(mean).pow(2))
				.reduce(BigDecimal.ZERO, BigDecimal::add);
		BigDecimal variance = sumOfSquares.divide(new BigDecimal(size), MathContext.DECIMAL128);
		return variance.sqrt(MathContext.DECIMAL128).setScale(2, RoundingMode.HALF_UP);
	}



	/**
	 * 判断开闭店状态是否异常
	 * @param referOpenTime
	 * @param openTime
	 * @param timePeriod
	 * @return
	 */
	private Integer checkStoreStatus(String referOpenTime, String openTime, Integer timePeriod) {
		Integer storeStatus = 2;
		if (StringUtils.isNotEmpty(referOpenTime) && StringUtils.isNotEmpty(openTime)){
			long referOpenTimeMinute = parseTimeToMinutes(referOpenTime);
			long openTimeMinute = parseTimeToMinutes(openTime);
			long timePeriodMinute = timePeriod.longValue();
			long maxTime = referOpenTimeMinute + timePeriodMinute;
			if (openTimeMinute > maxTime){
				storeStatus = 1;
			}else {
				storeStatus = 0;
			}
		}
		return storeStatus;
	}

	/**
	 * 同步店铺数据
	 */
	private void syncRoomData(Long tenantId) {
		RoomsListDto roomDto = new RoomsListDto();
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (tenantId!=null && tenantId!=0){
			roomDto.setTenantId(tenantId);
			storeDto.setTenantId(tenantId);
		}
		log.info("roomDto:{}", roomDto);
		List<RoomsListVo> roomList = roomProvider.list(roomDto);
		log.info("roomListSize:{}", roomList.size());

		log.info("storeDto:{}", storeDto);
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSize:{}", storeList.size());

		CommerceStoreManageAddDto storeAddDto = new CommerceStoreManageAddDto();
		if (CollectionUtil.isNotEmpty(storeList)){
			List<Long> roomIdList = storeList.stream().map(f -> f.getRoomId()).collect(Collectors.toList());
			for (RoomsListVo roomsVo : roomList) {
				if (!roomIdList.contains(roomsVo.getId())){
					saveStoreManage(roomsVo, storeAddDto);
				}
			}
		}else {
			for (RoomsListVo roomsVo : roomList) {
				saveStoreManage(roomsVo, storeAddDto);
			}
		}
	}

	/**
	 * 保存店铺信息到开闭店管理表
	 * @param roomsVo 店铺信息
	 * @param storeAddDto
	 */
	private void saveStoreManage(RoomsListVo roomsVo, CommerceStoreManageAddDto storeAddDto) {
		storeAddDto.setTenantId(roomsVo.getTenantId());
		storeAddDto.setTenantName(roomsVo.getTenantName());
		storeAddDto.setEntId(roomsVo.getEntId());
		storeAddDto.setOrgFid(roomsVo.getOrgFid());
		storeAddDto.setOrgFname(roomsVo.getOrgFname());
		storeAddDto.setRoomId(roomsVo.getId());
		storeAddDto.setStoreStatus(2);
		storeAddDto.setCreateBy(roomsVo.getCreateBy());
		storeAddDto.setCreateUser(roomsVo.getCreateUser());
		storeAddDto.setCreateUserName(roomsVo.getCreateUserName());
		commerceStoreManageProvider.add(storeAddDto);
	}

	/**
	 * 同步一段范围的客流数据
	 */
	private void scopeFlowData(String param, Long tenantId) {

		Integer syncFlag = 0;
		// 获取时间范围
		LocalDate endDay = LocalDate.now().minusDays(1);
		LocalDate startDay = LocalDate.now().minusDays(7);
		if (StringUtils.isNotBlank(param)) {
			String[] split = param.split(",");
			startDay = LocalDate.parse(split[0]);
			endDay = LocalDate.parse(split[1]);
			if (split.length > 3) {
				syncFlag = Integer.valueOf(split[3]);
			}
		}

		// 获取日期列表
		List<LocalDate> dateList = new ArrayList<>();
		dateList.add(startDay);
		LocalDate tempDate = startDay.plusDays(1);
		while (tempDate.isBefore(endDay) || tempDate.isEqual(endDay)){
			dateList.add(tempDate);
			tempDate = tempDate.plusDays(1);
		}
		log.info("dateList:{}", dateList);

		// 获取所有店铺数据
		RoomsListDto roomDto = new RoomsListDto();
		if (tenantId!=null){
			roomDto.setTenantId(tenantId);
		}
		List<RoomsListVo> roomList = roomProvider.list(roomDto);
		log.info("roomListSize:{}", roomList.size());

		// 店铺数据按tenantId-fcode组成key
		Map<String, RoomsListVo> roomCodeMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(roomList)) {
			roomCodeMap = roomList.stream().collect(Collectors.toMap(
					room -> room.getTenantId() + "-" + room.getFcode(),
					room -> room,
					(existing, replacement) -> existing
			));
		}

		// 存储每个店铺的所有开店时间、闭店时间
		Map<Long, List<LocalDateTime>> openTimeListMap = new HashMap<>();
		Map<Long, List<LocalDateTime>> closeTimeListMap = new HashMap<>();

		for (LocalDate localDate : dateList) {
			// 获取表名
			int monthValue = localDate.getMonthValue();
			String monthTwoDigits = String.format("%02d", monthValue);
			String tableName = "som_iot.som_iot_flow_data" + "_" + monthTwoDigits;
			log.info("tableName:{}", tableName);

			// 进出客流请求参数
			IotFlowDataEnterOutDto enterOutDto = new IotFlowDataEnterOutDto();
			if (tenantId != null) {
				enterOutDto.setTenantId(tenantId);
			}
			enterOutDto.setStartCollectTime(localDate.atStartOfDay());
			enterOutDto.setEndCollectTime(localDate.atTime(23, 59, 59));
			enterOutDto.setTableName(tableName);
			log.info("enterOutDto:{}", enterOutDto);

			// 获取进客流数据
			List<IotFlowDataEnterOutVo> enterVoList = flowDataProvider.getEnterCountFlowData(enterOutDto);
			log.info("enterVoListSize:{}", enterVoList.size());
			if (CollectionUtil.isNotEmpty(enterVoList)) {
				for (IotFlowDataEnterOutVo enterVo : enterVoList) {
					String roomCode = enterVo.getRoomCode();
					LocalDateTime openTime = enterVo.getCollectTime();
					RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
					if (roomsListVo != null) {
						Long roomId = roomsListVo.getId();

						// 获取店铺历史的开店时间列表
						List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
						if (CollectionUtil.isEmpty(openTimeList)) {
							openTimeList = new ArrayList<>();
						}

						openTimeList.add(openTime);
						openTimeListMap.put(roomId, openTimeList);
					}
				}
			}

			// 获取出客流数据
			List<IotFlowDataEnterOutVo> outVoList = flowDataProvider.getOutCountFlowData(enterOutDto);
			log.info("outVoListSize:{}", outVoList.size());
			if (CollectionUtil.isNotEmpty(outVoList)) {
				for (IotFlowDataEnterOutVo outVo : outVoList) {
					String roomCode = outVo.getRoomCode();
					LocalDateTime closeTime = outVo.getCollectTime();
					RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
					if (roomsListVo != null) {
						Long roomId = roomsListVo.getId();

						// 获取店铺历史的闭店时间列表
						List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
						if (CollectionUtil.isEmpty(closeTimeList)) {
							closeTimeList = new ArrayList<>();
						}

						closeTimeList.add(closeTime);
						closeTimeListMap.put(roomId, closeTimeList);
					}
				}
			}
		}

		// 获取所有的开闭店管理的店铺列表
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (tenantId!=null){
			storeDto.setTenantId(tenantId);
		}
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSize:{}", storeList.size());

		// 给每个店铺赋值 参考值、开店时间、闭店时间、开闭店时长
		if (CollectionUtil.isNotEmpty(storeList)){
			for (CommerceStoreManageListVo storeVo : storeList) {

				CommerceStoreManageEditDto editDto = new CommerceStoreManageEditDto();
				editDto.setId(storeVo.getId());

				Long roomId = storeVo.getRoomId();

				// 求开店时间的平均值
				List<LocalDateTime> openTimeList = openTimeListMap.get(roomId);
				String referOpenTime = getTimeAvgValue(openTimeList);
				editDto.setReferFlowOpenTime(referOpenTime);

				// 求开店时间的平均值
				List<LocalDateTime> closeTimeList = closeTimeListMap.get(roomId);
				String referCloseTime = getTimeAvgValue(closeTimeList);
				editDto.setReferFlowCloseTime(referCloseTime);

				// 获取开店时长
				BigDecimal timeLength = getTimeLength(referOpenTime, referCloseTime);
				editDto.setReferFlowOpenLength(timeLength);

				// 赋值参考类型为客流
				if (StringUtils.isNotEmpty(referOpenTime)){
					log.info("openTimeList:{}", openTimeList);
					log.info("closeTimeList:{}", closeTimeList);
					log.info("timeLength:{}", timeLength);
					// 只同步客流数据
					if (syncFlag!=2){
						editDto.setReferType(1);
					}
					commerceStoreManageProvider.edit(editDto);
				}
			}
		}

		log.info("同步客流数据成功...");
	}

	/**
	 * 每隔一个小时同步客流数据
	 * @param req
	 */
	private void syncFlowData(CommerceStoreManageListReq req) {
		log.info("每隔一个小时同步客流数据:{}", JsonUtils.toJson(req));

		Long tenantId = null;
		LocalDateTime previousHour = LocalDateTime.now().minusHours(1);
		// 前一个小时的开始时间
		LocalDateTime startTime = previousHour.withMinute(0).withSecond(0);
		// 前一个小时的结束时间
		LocalDateTime endTime = previousHour.withMinute(59).withSecond(59);
		String param = req.getOrgFname();
		if (StringUtils.isNotBlank(param)) {
			String[] split = param.split(",");
			startTime = LocalDateTime.parse(split[0], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			endTime = LocalDateTime.parse(split[1], DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
			if (split.length > 2) {
				tenantId = Long.valueOf(split[2]);
			}
		}
		log.info("startTime:{}", startTime);
		log.info("endTime:{}", endTime);

		// 日期时间格式
		DateTimeFormatter dateForMatter = DateTimeFormatter.ofPattern("HH:mm");

		// 获取全生命周期店铺数据
		RoomsListDto roomDto = new RoomsListDto();
		if (tenantId!=null){
			roomDto.setTenantId(tenantId);
		}
		List<RoomsListVo> roomList = roomProvider.list(roomDto);
		log.info("roomListSize:{}", roomList.size());

		// 获取开闭店管理数据
		CommerceStoreManageListDto storeDto = new CommerceStoreManageListDto();
		if (tenantId!=null){
			storeDto.setTenantId(tenantId);
		}
		List<CommerceStoreManageListVo> storeList = commerceStoreManageProvider.list(storeDto);
		log.info("storeListSzie:{}", storeList.size());

		Map<String, RoomsListVo> roomCodeMap = new HashMap<>();
		Map<Long, RoomsListVo> roomMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(roomList)) {
			roomMap = roomList.stream().collect(Collectors.toMap(RoomsListVo::getId, Function.identity()));
			roomCodeMap = roomList.stream().collect(Collectors.toMap(
					room -> room.getTenantId() + "-" + room.getFcode(),
					room -> room,
					(existing, replacement) -> existing
			));
		}

		Map<Long, CommerceStoreManageListVo> storeMap = new HashMap<>();
		if (CollectionUtil.isNotEmpty(storeList)) {
			storeMap = storeList.stream().collect(Collectors.toMap(CommerceStoreManageListVo::getRoomId, Function.identity()));
		}

		LocalDateTime createTimeStart = startTime.withHour(0).withMinute(0).withSecond(0);
		LocalDateTime createTimeEnd = endTime.withHour(23).withMinute(59).withSecond(59);
		log.info("createTimeStart:{}", createTimeStart);
		log.info("createTimeEnd:{}", createTimeEnd);

		// 获取客流数据
		// 获取表名
		int monthValue = startTime.getMonthValue();
		String monthTwoDigits = String.format("%02d", monthValue);
		String tableName = "som_iot.som_iot_flow_data" + "_" + monthTwoDigits;
		log.info("tableName:{}", tableName);

		// 进出客流请求参数
		IotFlowDataEnterOutDto enterOutDto = new IotFlowDataEnterOutDto();
		if (tenantId != null) {
			enterOutDto.setTenantId(tenantId);
		}
		enterOutDto.setStartCollectTime(startTime);
		enterOutDto.setEndCollectTime(endTime);
		enterOutDto.setTableName(tableName);
		log.info("enterOutDto:{}", enterOutDto);

		// 获取进客流数据
		List<IotFlowDataEnterOutVo> enterVoList = flowDataProvider.getEnterCountFlowData(enterOutDto);
		log.info("enterVoListSize:{}", enterVoList.size());
		if (CollectionUtil.isNotEmpty(enterVoList)) {
			for (IotFlowDataEnterOutVo enterVo : enterVoList) {
				String roomCode = enterVo.getRoomCode();
				LocalDateTime openTime = enterVo.getCollectTime();
				String openTimeStr = openTime.format(dateForMatter);
				RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
				if (roomsListVo != null) {
					Long roomId = roomsListVo.getId();
					// 赋值开店时间
					log.info("roomId:{}", roomId);

					CommerceStoreManageListVo storeVo = storeMap.get(roomId);
					RoomsListVo roomsVo = roomMap.get(roomId);

					// 非客流数据直接下一条
					if (storeVo==null || roomsVo==null || storeVo.getReferType()==null || storeVo.getReferType()!=1){
						continue;
					}

					// 没有门店id或没有参考开店时间直接下一条
					if (StringUtils.isEmpty(roomsVo.getStoreId()) || StringUtils.isEmpty(storeVo.getReferFlowOpenTime())){
						continue;
					}

					// 当天客流开店时间
					String flowOpenTime = null;

					// 开闭店管理和明细id
					Long storeManageId = storeVo.getId();
					Long storeManageDetailId = null;

					// 参考开店时间、异常时间间隔
					String referOpenTime = storeVo.getReferFlowOpenTime();
					Integer timePeriod = storeVo.getTimePeriod();

					// 是否开启消息通知
					Integer messageNotify = storeVo.getMessageNotify();

					// 门店状态
					String roomStatus = null;
					CommerceEntexitListDto dto = new CommerceEntexitListDto();
					dto.setShopId(roomsVo.getId());
					dto.setTenantId(roomsVo.getTenantId());
					dto.setSearchType(4);
					CommerceEntexitGetVo getVo = commerceEntexitProvider.get(dto);
					if (!ObjectUtils.isEmpty(getVo)) {
						roomStatus = getVo.getRoomStatus();
					}

					CommerceStoreManageDetailListDto detailDto = new CommerceStoreManageDetailListDto();
					detailDto.setStoreManageId(storeManageId);
					detailDto.setCreateTimeStart(createTimeStart);
					detailDto.setCreateTimeEnd(createTimeEnd);
					CommerceStoreManageDetailListVo detailListVo = commerceStoreManageDetailProvider.getDetailData(detailDto);
					if (detailListVo==null){
						// 入库明细数据
						CommerceStoreManageDetailAddDto detailAddDto = new CommerceStoreManageDetailAddDto();
						detailAddDto.setStoreManageId(storeManageId);
						detailAddDto.setStoreId(roomsVo.getStoreId());
						detailAddDto.setRoomStatus(roomStatus);
						detailAddDto.setBrandId(roomsVo.getBrandId());
						detailAddDto.setBrandName(roomsVo.getBrandName());
						detailAddDto.setReferFlowOpenTime(storeVo.getReferFlowOpenTime());
						detailAddDto.setReferFlowCloseTime(storeVo.getReferFlowCloseTime());
						detailAddDto.setReferFlowOpenLength(storeVo.getReferFlowOpenLength());
						detailAddDto.setStoreStatus(2);
						LocalDateTime createTimeAddMinutes = createTimeStart.plusMinutes(1);
						detailAddDto.setCreateTime(createTimeAddMinutes);
						CommerceStoreManageDetailAddVo addVo = commerceStoreManageDetailProvider.add(detailAddDto);
						storeManageDetailId = addVo.getId();
						log.info("入库明细数据:{}", storeManageDetailId);
					}else {
						storeManageDetailId = detailListVo.getId();
						flowOpenTime = storeVo.getFlowOpenTime();
						log.info("已有明细数据:{}", storeManageDetailId);
					}

					// 当天客流开店时间为空才进行赋值操作
					if (StringUtils.isEmpty(flowOpenTime)){
						// 获取开店是否异常
						Integer storeStatus = checkStoreStatus(referOpenTime, openTimeStr, timePeriod);
						// 开店异常则进行消息通知
						if (storeStatus==1 && messageNotify==1){
							sendMsg(roomsVo);
						}
						// 赋值开店时间
						CommerceStoreManageEditDto storeEditDto = new CommerceStoreManageEditDto();
						storeEditDto.setId(storeManageId);
						storeEditDto.setFlowOpenTime(openTimeStr);
						storeEditDto.setStoreStatus(storeStatus);
						commerceStoreManageProvider.edit(storeEditDto);

						CommerceStoreManageDetailEditDto openTimeEdit = new CommerceStoreManageDetailEditDto();
						openTimeEdit.setId(storeManageDetailId);
						openTimeEdit.setFlowOpenTime(openTimeStr);
						openTimeEdit.setStoreStatus(storeStatus);
						commerceStoreManageDetailProvider.edit(openTimeEdit);
					}
				}
			}
		}

		// 获取出客流数据
		List<IotFlowDataEnterOutVo> outVoList = flowDataProvider.getOutCountFlowData(enterOutDto);
		log.info("outVoListSize:{}", outVoList.size());
		if (CollectionUtil.isNotEmpty(outVoList)) {
			for (IotFlowDataEnterOutVo outVo : outVoList) {
				String roomCode = outVo.getRoomCode();
				LocalDateTime closeTime = outVo.getCollectTime();
				String closeTimeStr = closeTime.format(dateForMatter);
				RoomsListVo roomsListVo = roomCodeMap.get(roomCode);
				if (roomsListVo != null) {
					Long roomId = roomsListVo.getId();
					// 赋值闭店时间
					log.info("roomId:{}", roomId);

					CommerceStoreManageListVo storeVo = storeMap.get(roomId);
					RoomsListVo roomsVo = roomMap.get(roomId);

					// 非客流数据直接下一条
					if (storeVo==null || roomsVo==null || storeVo.getReferType()==null || storeVo.getReferType()!=1){
						continue;
					}

					// 没有门店id或没有参考开店时间直接下一条
					if (StringUtils.isEmpty(roomsVo.getStoreId()) || StringUtils.isEmpty(storeVo.getReferFlowOpenTime())){
						continue;
					}

					// 开闭店管理和明细id
					Long storeManageId = storeVo.getId();
					Long storeManageDetailId = null;

					CommerceStoreManageDetailListDto detailDto = new CommerceStoreManageDetailListDto();
					detailDto.setStoreManageId(storeManageId);
					detailDto.setCreateTimeStart(createTimeStart);
					detailDto.setCreateTimeEnd(createTimeEnd);
					CommerceStoreManageDetailListVo detailListVo = commerceStoreManageDetailProvider.getDetailData(detailDto);
					// 为空说明没有开店时间
					if (detailListVo==null || StringUtils.isEmpty(detailListVo.getFlowOpenTime())){
						continue;
					}

					storeManageDetailId = detailListVo.getId();
					// 当天客流开店时间
					String flowOpenTime = detailListVo.getFlowOpenTime();

					// 获取开店时长 闭店每次有都重新赋值计算
					BigDecimal timeLength = getTimeLength(flowOpenTime, closeTimeStr);
					CommerceStoreManageDetailEditDto openTimeEdit = new CommerceStoreManageDetailEditDto();
					openTimeEdit.setId(storeManageDetailId);
					openTimeEdit.setFlowCloseTime(closeTimeStr);
					openTimeEdit.setFlowOpenLength(timeLength);
					commerceStoreManageDetailProvider.edit(openTimeEdit);
				}
			}
		}

		log.info("每隔一小时同步客流数据成功...");
	}

}
