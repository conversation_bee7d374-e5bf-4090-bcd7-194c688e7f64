package com.seewin.som.rent.service.impl;

import cn.hutool.core.util.IdUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson2.JSON;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.provider.CommerceAdvertContractProvider;
import com.seewin.som.commerce.provider.CommerceContractInfoProvider;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.provider.CommerceSupplierProvider;
import com.seewin.som.commerce.req.CommerceAdvertContractListDto;
import com.seewin.som.commerce.req.CommerceContractInfoListDto;
import com.seewin.som.commerce.resp.CommerceAdvertContractGetVo;
import com.seewin.som.commerce.resp.CommerceContractInfoGetVo;
import com.seewin.som.commerce.resp.CommerceEffectiveContractVo;
import com.seewin.som.commerce.resp.CommerceSupplierGetVo;
import com.seewin.som.ent.provider.EntProjectProvider;
import com.seewin.som.ent.resp.EntProjectGetVo;
import com.seewin.som.rent.provider.*;
import com.seewin.som.rent.req.*;
import com.seewin.som.rent.resp.*;
import com.seewin.som.rent.service.RentFeeActualLogService;
import com.seewin.som.rent.service.RentFeeActualService;
import com.seewin.som.rent.util.AnalysisIdCardUtil;
import com.seewin.som.rent.util.BigDecimalUtil;
import com.seewin.som.rent.util.DateUtil;
import com.seewin.som.rent.util.InvoiceUtils;
import com.seewin.som.rent.vo.req.*;
import com.seewin.som.rent.vo.resp.*;
import com.seewin.som.space.provider.RoomsProvider;
import com.seewin.som.space.resp.RoomsGetVo;
import com.seewin.som.storage.provider.SysFileProvider;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.system.service.FileService;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.utils.JsonUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.YearMonth;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * <p>
 * 实收台账 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2024-09-18
 */
@Service
@Slf4j
public class RentFeeActualServiceImpl implements RentFeeActualService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-rent-mgt")
	private RentFeeActualProvider rentFeeActualProvider;

    @DubboReference(providedBy = "som-space-mgt")
    private RoomsProvider roomsProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualDetailProvider rentFeeActualDetailProvider;
    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualLogProvider rentFeeActualLogProvider;
    @DubboReference(providedBy = "som-storage-mgt")
    private SysFileProvider sysFileProvider;

    @Autowired
    private FileService fileService;

    @Autowired
    private RentFeeActualLogService rentFeeActualLogService;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableProvider rentFeeReceivableProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeReceivableDetailProvider rentFeeReceivableDetailProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeActualDetailLogProvider rentFeeActualDetailLogProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentFeeImportProvider rentFeeImportProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentVoucherTenantInfoProvider rentVoucherTenantInfoProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentInvoiceAccountProvider rentInvoiceAccountProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingItemProvider rentChargingItemProvider;

    @DubboReference(providedBy = "som-rent-mgt")
    private RentChargingTenantItemProvider rentChargingTenantItemProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractProvider commerceContractProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceContractInfoProvider commerceContractInfoProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceSupplierProvider commerceSupplierProvider;

    @DubboReference(providedBy = "som-ent-mgt")
    private EntProjectProvider entProjectProvider;

    @DubboReference(providedBy = "som-commerce-mgt")
    private CommerceAdvertContractProvider advertContractProvider;

	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<RentFeeActualListItem> page(RentFeeActualListReq listReq) {
        PageResp<RentFeeActualListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);
		
        RentFeeActualListDto queryDto = BeanUtils.copyProperties(listReq, RentFeeActualListDto.class);
				queryDto.setTenantId(curUser.getTenantId());
        
        PageQuery<RentFeeActualListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<RentFeeActualListVo> pageResult = rentFeeActualProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), RentFeeActualListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public List<RentFeeActualGetResp> get(RentFeeActualGetReq getReq) {

        List<RentFeeActualGetResp> getRespList = new ArrayList<>();
        //"应"收账单明细：
        //先查询"应"收台账数据list 在循环获取明细。
        RentFeeActualListDto actualQuery = BeanUtils.copyProperties(getReq, RentFeeActualListDto.class);
        actualQuery.setFname(null);
        actualQuery.setRoomName(null);
        actualQuery.setRoomName(null);
        actualQuery.setContractCode(getReq.getContractCode());
        List<RentFeeActualListVo> actualList = rentFeeActualProvider.list(actualQuery);
        Map<Integer, RentFeeActualListVo> actualMap = actualList.stream().collect(Collectors.toMap(RentFeeActualListVo::getPeriodMonth, (t) -> t));
        //根据"应"收台账查询应收明细
        String roomName = getReq.getRoomName();
        String fname = getReq.getFname();
        if(getReq.getRoomId()!=null){
            if (StringUtils.isBlank(getReq.getRoomName())) {
                //方法让费用台账也能调用
                RoomsGetVo roomsGetVo = roomsProvider.get(getReq.getRoomId());
                if(roomsGetVo!=null){
                    roomName = roomsGetVo.getName();
                    fname = roomsGetVo.getFname().replace("/" + roomsGetVo.getName(), "");
                }
            }
        }else{
            CommerceAdvertContractListDto advertContractListDto = new CommerceAdvertContractListDto();
            advertContractListDto.setContractCode(getReq.getContractCode());
            CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(advertContractListDto);
            if(commerceAdvertContractGetVo!=null){
                roomName=commerceAdvertContractGetVo.getAdvertName();
            }
        }
        for (int month = 1; month <= 12; month++) {
            RentFeeActualListVo rentFeeActualListVo = actualMap.get(month);
            String period = getReq.getPeriodYear() + "/" + String.format("%02d", month);
            if (rentFeeActualListVo == null) {
                //没有数据的需要补足普通数据
                RentFeeActualGetResp getResp = new RentFeeActualGetResp();
                getResp.setRoomName(roomName);
                getResp.setFname(fname);
                getResp.setPeriod(period);
                getResp.setActualDetailList(new ArrayList<>());
                getRespList.add(getResp);
            } else {
                RentFeeActualGetResp getResp = BeanUtils.copyProperties(rentFeeActualListVo, RentFeeActualGetResp.class);
                getResp.setRoomName(roomName);
                getResp.setFname(fname);
                getResp.setPeriod(period);
                RentFeeActualDetailListDto actualDetailQuery = new RentFeeActualDetailListDto();
                actualDetailQuery.setActualId(rentFeeActualListVo.getId());
                List<RentFeeActualDetailListVo> actualDetailList = rentFeeActualDetailProvider.list(actualDetailQuery);
                List<RentFeeActualDetailListItem> rentFeeActualDetailListItem = BeanUtils.copyProperties(actualDetailList, RentFeeActualDetailListItem.class);
                getResp.setActualDetailList(rentFeeActualDetailListItem);
                getRespList.add(getResp);
            }
        }
        return getRespList;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public RentFeeActualAddResp add(RentFeeActualAddReq addReq) {
        RentFeeActualAddDto dto = BeanUtils.copyProperties(addReq, RentFeeActualAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		dto.setTenantId(curUser.getTenantId());
		
        RentFeeActualAddVo addVo = rentFeeActualProvider.add(dto);

        RentFeeActualAddResp addResp = BeanUtils.copyProperties(addVo, RentFeeActualAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(RentFeeActualEditReq editReq) {
        RentFeeActualEditDto dto = BeanUtils.copyProperties(editReq, RentFeeActualEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        rentFeeActualProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(RentFeeActualDelReq delReq) {
        rentFeeActualProvider.delete(delReq.getId());
    }

    @Override
    public RentFeeActualGetBaseResp getBaseById(RentFeeActualGetBaseReq getReq) {
        Long actualId = getReq.getId();
        RentFeeActualGetBaseResp getResp = new RentFeeActualGetBaseResp();
        RentFeeActualGetVo rentFeeActualGetVo = rentFeeActualProvider.get(actualId);
        if (rentFeeActualGetVo != null) {
            getResp = BeanUtils.copyProperties(rentFeeActualGetVo, RentFeeActualGetBaseResp.class);
            getResp.setPeriod(rentFeeActualGetVo.getPeriodYear() + "/" + String.format("%02d", rentFeeActualGetVo.getPeriodMonth()));
            RentFeeActualDetailListDto detailDto =  new RentFeeActualDetailListDto();
            detailDto.setActualId(actualId);
            //查询实收的明细数据
            List<RentFeeActualDetailListVo> detailList =  rentFeeActualDetailProvider.list(detailDto);
            getResp.setActualDetailList(BeanUtils.copyProperties(detailList, RentFeeActualDetailListItem.class));

            RentFeeActualLogListDto logDto = new RentFeeActualLogListDto();
            logDto.setActualId(actualId);
            //查询实收的录入明细数据
            List<RentFeeActualLogListVo> actualLogList = rentFeeActualLogProvider.list(logDto);
            getResp.setActualLogList(BeanUtils.copyProperties(actualLogList, RentFeeActualLogListItem.class));
        }
        return getResp;
    }

    @Override
    public void download() {
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("实收录入导入模板.xlsx","UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename="+endCodeFileName);

            try (OutputStream outputStream = response.getOutputStream()) {
                EasyExcel.write(outputStream, RentFeeActualExcelTemplateItem.class)
                        .sheet("实收录入导入模板")
                        .doWrite(new ArrayList<>());
            }
        } catch (Exception e) {
            log.error("导出模板异常: {}", e.getMessage(), e);
            throw new ServiceException("导出失败，请稍后再试。", e);
        }
    }

    @Override
    public void imp(RentFeeImportReq req) {
        if (CollectionUtils.isEmpty(req.getFileIds())) {
            throw new ServiceException("附件上传异常,请重新上传");
        }
            for (Long fileId : req.getFileIds()) {
                //上传附件
                SysFileGetVo sysFileGetVo = null;
                try {
                    sysFileGetVo = sysFileProvider.get(fileId);
                    if (sysFileGetVo == null) {
                        throw new ServiceException("附件上传异常,请重新上传");
                    }
                } catch (Exception e) {
                    throw new ServiceException("附件上传异常,请重新上传");
                }
                //解析数据
                log.info("开始解析文件："+sysFileGetVo.getFileName());
                InputStream inputstream = fileService.getObject(sysFileGetVo.getBuckets(), sysFileGetVo.getFilePath());
                importExcel(inputstream,sysFileGetVo,fileId);
            }
    }

    @Override
    public void importSingleExcel(MultipartFile file) throws IOException {
        InputStream inputStream = file.getInputStream();
        SysFileGetVo sysFileGetVo=new SysFileGetVo();
        sysFileGetVo.setFileName(file.getOriginalFilename());
        importExcel(inputStream,sysFileGetVo,-1l);
    }

    @Override
    public void exp(RentFeeReceivableExpReq expReq) {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        List<RentFeeActualDetailExportItem> exportList = new ArrayList<>();
        LocalDate localDate = LocalDate.now();
        int periodYear = expReq.getPeriodYear() == null ? localDate.getYear() : expReq.getPeriodYear();
        int periodMonth = expReq.getPeriodMonth() == null ? localDate.getMonthValue() : expReq.getPeriodMonth();
        String period = periodYear + "/" + String.format("%02d", periodMonth);
        RentFeeActualListDto dto = new RentFeeActualListDto();
        dto.setPeriodYear(periodYear);
        dto.setPeriodMonth(periodMonth);
        dto.setTenantId(optUser.getTenantId());
        List<RentFeeActualExportVo> rentFeeActualExportVos = rentFeeActualProvider.exportActual(dto);
        //根据合同编号查询合同供应商-铺位计租面积
        Set<String> contractCodes = rentFeeActualExportVos.stream().map(RentFeeActualExportVo::getContractCode).collect(Collectors.toSet());
        Map<String, CommerceEffectiveContractVo> effectiveContractMap = new HashMap<>();
        for (String contractCode : contractCodes) {
            CommerceEffectiveContractVo contractVo ;
            if (contractCode.startsWith("D")||contractCode.startsWith("G")) {
                CommerceAdvertContractListDto commerceAdvertContractListDto = new CommerceAdvertContractListDto();
                commerceAdvertContractListDto.setContractCode(contractCode);
                CommerceAdvertContractGetVo commerceAdvertContractGetVo = advertContractProvider.get(commerceAdvertContractListDto);
                contractVo=BeanUtils.copyProperties(commerceAdvertContractGetVo,  CommerceEffectiveContractVo.class);
                contractVo.setRentArea(commerceAdvertContractGetVo.getAdvertArea());
            }else{
                contractVo =commerceContractProvider.getRentFeeCalculateInfo(contractCode);
            }
            effectiveContractMap.put(contractCode,contractVo);
        }
        exportList = BeanUtils.copyProperties(rentFeeActualExportVos, RentFeeActualDetailExportItem.class);
        for (RentFeeActualDetailExportItem item : exportList) {
            item.setPeriod(item.getPeriodYear() + "/" + String.format("%02d", item.getPeriodMonth()));
            item.setRentArea(effectiveContractMap.get(item.getContractCode()).getRentArea());
            item.setSupplierName(effectiveContractMap.get(item.getContractCode()).getSupplierName());
        }
        try {
            HttpServletResponse response = ApiUtils.getResponse();
            response.setContentType("application/octet-stream");
            String endCodeFileName = URLEncoder.encode("实收账单导出.xlsx", "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=" + endCodeFileName);
            response.setContentType("application/octet-stream");
            EasyExcel.write(response.getOutputStream(), RentFeeActualDetailExportItem.class)
                    .sheet("实收账单导出").doWrite(exportList);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("捕获到的导出异常{}", JSON.toJSONString(e.getMessage()));
        }
    }

    @Override
    public List<RentFeeActualInvoiceResp> invoiceList(RentFeeActualInvoiceReq req) {
        List<RentFeeActualInvoiceResp> listResp = new ArrayList<>();
        User curUser = ApiUtils.getUser(User.class);
        Long tenantId = curUser.getTenantId();

        RentFeeActualLogListDto logDto = new RentFeeActualLogListDto();
        logDto.setTenantId(tenantId);
        logDto.setRoomNameList(req.getRoomNameList());
        logDto.setChargingItemType(req.getChargingItemType());
        logDto.setPeriodStartDate(req.getStartYearMonth().atDay(1));
        logDto.setPeriodEndDate(YearMonth.from(req.getEndYearMonth()).atEndOfMonth());
        logDto.setAccountingOrgName(req.getAccountingOrgName());

        // 开票账号
        RentInvoiceAccountGetVo accountVo = null;

        // 查询实收冲抵明细数据
        List<RentFeeActualDetailLogListVo> actualDetailLogList = rentFeeActualLogProvider.getJoinActualListByDate(logDto);
        if (!CollectionUtils.isEmpty(actualDetailLogList)){
            for (RentFeeActualDetailLogListVo detailLogVo : actualDetailLogList) {
                // 获取开票账号
                String accountingOrgName = detailLogVo.getAccountingOrgName();
                if (StringUtils.isNotEmpty(accountingOrgName)){
                    RentInvoiceAccountListDto accountDto = new RentInvoiceAccountListDto();
                    accountDto.setAccountingOrgName(accountingOrgName);
                    accountVo = rentInvoiceAccountProvider.get(accountDto);
                }
                detailLogVo.setChargingItemType(req.getChargingItemType());
                // 如果为本金开票中，查询开票结果
                if (detailLogVo.getInvoiceStatus()!=null && detailLogVo.getInvoiceStatus()==1 && StringUtils.isNotEmpty(detailLogVo.getInvoiceBizId())){
                    String content = InvoiceUtils.getQueryContent(detailLogVo.getInvoiceBizId());
                    if (accountVo!=null){
                        RentInvoiceListResult invoiceResult = InvoiceUtils.query(content, accountVo);
                        log.info("invoiceResult:{}", JsonUtils.toJson(invoiceResult));
                        if (invoiceResult!=null && !CollectionUtils.isEmpty(invoiceResult.getResult())){
                            String code = invoiceResult.getCode();
                            log.info("code:{}", code);
                            RentInvoiceResultDetail result = invoiceResult.getResult().get(0);
                            if ("E0000".equals(code) && result!=null){
                                String status = result.getStatus();
                                log.info("status:{}", status);
                                detailLogVo.setInvoiceStatus(1);
                                detailLogVo.setConfirmStatus(1);
                                detailLogVo.setInvoiceResult("开票中, 请稍后前往开票记录刷新查看");
                                if ("2".equals(status)){
                                    detailLogVo.setInvoicePic(result.getPdfUrl());
                                    LocalDateTime invoiceTime = LocalDateTime.now();
                                    // 修改开票状态
                                    for (String id : detailLogVo.getIds().split(",")) {
                                        RentFeeActualDetailLogEditDto detailLogEditDto = new RentFeeActualDetailLogEditDto();
                                        detailLogEditDto.setId(Long.valueOf(id));
                                        detailLogEditDto.setInvoiceStatus(0);
                                        detailLogEditDto.setConfirmStatus(0);
                                        detailLogEditDto.setInvoiceResult("开票成功");
                                        detailLogEditDto.setInvoicePic(result.getPdfUrl());
                                        detailLogEditDto.setUpdateTime(invoiceTime);
                                        rentFeeActualDetailLogProvider.edit(detailLogEditDto);
                                    }
                                }else if ("22".equals(status) || "24".equals(status)){
                                    detailLogVo.setInvoiceStatus(2);
                                    detailLogVo.setConfirmStatus(0);
                                    detailLogVo.setInvoiceResult(result.getStatusMsg()+result.getFailCause());
                                    // 开票失败,放开确认状态为未确认
                                    for (String id : detailLogVo.getIds().split(",")) {
                                        RentFeeActualDetailLogEditDto detailLogEditDto = new RentFeeActualDetailLogEditDto();
                                        detailLogEditDto.setId(Long.valueOf(id));
                                        detailLogEditDto.setInvoiceStatus(2);
                                        detailLogEditDto.setConfirmStatus(0);
                                        detailLogEditDto.setInvoiceResult(result.getStatusMsg()+result.getFailCause());
                                        rentFeeActualDetailLogProvider.edit(detailLogEditDto);
                                    }
                                }
                            }
                        }
                    }
                }
                // 如果为滞纳金开票中，查询开票结果
                if (detailLogVo.getArrearsInvoiceStatus()!=null && detailLogVo.getArrearsInvoiceStatus()==1 && StringUtils.isNotEmpty(detailLogVo.getArrearsInvoiceBizId())){
                    String content = InvoiceUtils.getQueryContent(detailLogVo.getArrearsInvoiceBizId());
                    if (accountVo!=null){
                        RentInvoiceListResult invoiceResult = InvoiceUtils.query(content, accountVo);
                        log.info("arrearsInvoiceResult:{}", JsonUtils.toJson(invoiceResult));
                        if (invoiceResult!=null && !CollectionUtils.isEmpty(invoiceResult.getResult())){
                            String code = invoiceResult.getCode();
                            log.info("arrearsCode:{}", code);
                            RentInvoiceResultDetail result = invoiceResult.getResult().get(0);
                            if ("E0000".equals(code) && result!=null){
                                String status = result.getStatus();
                                log.info("arrearsStatus:{}", status);

                                detailLogVo.setArrearsInvoiceStatus(1);
                                detailLogVo.setArrearsConfirmStatus(1);
                                detailLogVo.setArrearsInvoiceResult("开票中, 请稍后前往开票记录刷新查看");

                                detailLogVo.setInvoiceStatus(1);
                                detailLogVo.setConfirmStatus(1);
                                detailLogVo.setInvoiceResult("开票中, 请稍后前往开票记录刷新查看");

                                if ("2".equals(status)){
                                    detailLogVo.setArrearsInvoicePic(result.getPdfUrl());
                                    LocalDateTime invoiceArrearsTime = LocalDateTime.now();
                                    // 修改滞纳金开票状态
                                    for (String id : detailLogVo.getIds().split(",")) {
                                        RentFeeActualDetailLogEditDto detailLogEditDto = new RentFeeActualDetailLogEditDto();
                                        detailLogEditDto.setId(Long.valueOf(id));
                                        detailLogEditDto.setArrearsInvoiceStatus(0);
                                        detailLogEditDto.setArrearsConfirmStatus(0);
                                        detailLogEditDto.setArrearsInvoiceResult("开票成功");
                                        detailLogEditDto.setArrearsInvoicePic(result.getPdfUrl());

                                        detailLogEditDto.setInvoiceStatus(0);
                                        detailLogEditDto.setConfirmStatus(0);
                                        detailLogEditDto.setInvoiceResult("开票成功");

                                        detailLogEditDto.setUpdateTime(invoiceArrearsTime);
                                        rentFeeActualDetailLogProvider.edit(detailLogEditDto);
                                    }
                                }else if ("22".equals(status) || "24".equals(status)){
                                    detailLogVo.setArrearsInvoiceStatus(2);
                                    detailLogVo.setArrearsConfirmStatus(0);
                                    detailLogVo.setArrearsInvoiceResult(result.getStatusMsg()+result.getFailCause());

                                    detailLogVo.setInvoiceStatus(2);
                                    detailLogVo.setConfirmStatus(0);
                                    detailLogVo.setInvoiceResult(result.getStatusMsg()+result.getFailCause());

                                    // 开票失败,放开确认状态为未确认
                                    for (String id : detailLogVo.getIds().split(",")) {
                                        RentFeeActualDetailLogEditDto detailLogEditDto = new RentFeeActualDetailLogEditDto();
                                        detailLogEditDto.setId(Long.valueOf(id));
                                        detailLogEditDto.setArrearsInvoiceStatus(2);
                                        detailLogEditDto.setArrearsConfirmStatus(0);
                                        detailLogEditDto.setArrearsInvoiceResult(result.getStatusMsg()+result.getFailCause());

                                        detailLogEditDto.setInvoiceStatus(2);
                                        detailLogEditDto.setConfirmStatus(0);
                                        detailLogEditDto.setInvoiceResult(result.getStatusMsg()+result.getFailCause());

                                        rentFeeActualDetailLogProvider.edit(detailLogEditDto);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            listResp = BeanUtils.copyProperties(actualDetailLogList, RentFeeActualInvoiceResp.class);
            if (!CollectionUtils.isEmpty(listResp)){
                for (RentFeeActualInvoiceResp invoiceResp : listResp) {
                    CommerceContractInfoGetVo contractInfoVo = null;
                    if (invoiceResp.getContractCode().startsWith("D")||invoiceResp.getContractCode().startsWith("G")) {
                        CommerceAdvertContractListDto advertDto = new CommerceAdvertContractListDto();
                        advertDto.setTenantId(tenantId);
                        advertDto.setContractCode(invoiceResp.getContractCode());
                        CommerceAdvertContractGetVo advertGetVo = advertContractProvider.get(advertDto);
                        contractInfoVo = BeanUtils.copyProperties(advertGetVo, CommerceContractInfoGetVo.class);
                    }else {
                        CommerceContractInfoListDto contractInfoDto = new CommerceContractInfoListDto();
                        contractInfoDto.setTenantId(tenantId);
                        contractInfoDto.setContractCode(invoiceResp.getContractCode());
                        contractInfoVo = commerceContractInfoProvider.get(contractInfoDto);
                    }
                    if (contractInfoVo != null && contractInfoVo.getSupplierId() != null) {
                        CommerceSupplierGetVo supplierVo = commerceSupplierProvider.get(contractInfoVo.getSupplierId());
                        if (supplierVo != null) {
                            invoiceResp.setSupplierName(supplierVo.getName());
                            invoiceResp.setTaxPeopleType(supplierVo.getTaxPeopleType());
                            if (StringUtils.isNotEmpty(supplierVo.getEmail())){
                                invoiceResp.setEmail(supplierVo.getEmail());
                            }
                        }
                    }
                }
            }
        }
        return listResp;
    }

    @Override
    public Boolean confirmInvoice(List<RentFeeActualInvoiceReq> reqList) {
        Boolean resultFlag = Boolean.TRUE;
        if (!CollectionUtils.isEmpty(reqList)){
            Integer chargingItemType = reqList.get(0).getChargingItemType();
            String invoiceType = reqList.get(0).getInvoiceType();
            String invoiceLine = reqList.get(0).getInvoiceLine();
            // 获取企业账号
            User curUser = ApiUtils.getUser(User.class);
            Long tenantId = curUser.getTenantId();
            // 按铺位号进行分组
            List<RentFeeActualInvoiceResultReq> resultList = new ArrayList<>();
            Map<String, List<RentFeeActualInvoiceReq>> regionNameMap = reqList.stream().collect(Collectors.groupingBy(RentFeeActualInvoiceReq::getRoomName));
            for(Map.Entry<String, List<RentFeeActualInvoiceReq>> entry : regionNameMap.entrySet()){
                // 按核算组织进行备注分组
                Map<String, String> remarkMap = new HashMap<>();
                List<RentFeeActualInvoiceReq> mapValue = entry.getValue();
                List<Long> idList = new ArrayList<>();
                for (RentFeeActualInvoiceReq rentFeeActualInvoiceReq : mapValue) {
                    for (String id : rentFeeActualInvoiceReq.getIds().split(",")) {
                        idList.add(Long.valueOf(id));
                    }
                    String accountingOrgName = rentFeeActualInvoiceReq.getAccountingOrgName();
                    String reqRemark = rentFeeActualInvoiceReq.getRemark();
                    if (StringUtils.isNotEmpty(accountingOrgName) && StringUtils.isNotEmpty(reqRemark)){
                        if (!remarkMap.containsKey(accountingOrgName)){
                            remarkMap.put(accountingOrgName, reqRemark);
                        }
                    }
                }
                RentFeeActualLogListDto logDto = new RentFeeActualLogListDto();
                logDto.setActualDetailLogIdList(idList);
                List<RentFeeActualDetailLogListVo> actualDetailLogList;
                if (chargingItemType==0){
                    // 非租金
                    actualDetailLogList = rentFeeActualLogProvider.getActualListByDate(logDto);
                }else {
                    // 租金
                    actualDetailLogList = rentFeeActualLogProvider.getActualRentListByDate(logDto);
                }
                List<RentFeeActualInvoiceReq> invoiceReqList = BeanUtils.copyProperties(actualDetailLogList, RentFeeActualInvoiceReq.class);
                // 按核算组织分组
                if (!CollectionUtils.isEmpty(invoiceReqList)){
                    Map<String, List<RentFeeActualInvoiceReq>> accountingOrgNameMap = new LinkedHashMap<>();
                    for (RentFeeActualInvoiceReq invoiceReq : invoiceReqList) {
                        String accountingOrgName = invoiceReq.getAccountingOrgName();
                        String roomName = invoiceReq.getRoomName();
                        String chargingItemName = invoiceReq.getChargingItemName();
                        if (StringUtils.isEmpty(accountingOrgName)){
                            throw new ServiceException("铺号:"+roomName+"对应的收费项目:"+chargingItemName+"找不到对应的核算组织");
                        }
                        RentInvoiceAccountListDto accountDto = new RentInvoiceAccountListDto();
                        accountDto.setAccountingOrgName(accountingOrgName);
                        RentInvoiceAccountGetVo accountVo = rentInvoiceAccountProvider.get(accountDto);
                        if (accountVo == null) {
                            throw new ServiceException("铺号:" + roomName + "对应的收费项目:" + chargingItemName + "对应的核算组织找不到对应的开票账号信息,无法生成发票");
                        }
                        List<RentFeeActualInvoiceReq> invoiceReqsList = new ArrayList<>();
                        if (accountingOrgNameMap.containsKey(accountingOrgName)) {
                            invoiceReqsList = accountingOrgNameMap.get(accountingOrgName);
                        }
                        invoiceReqsList.add(invoiceReq);
                        accountingOrgNameMap.put(accountingOrgName, invoiceReqsList);
                    }
                    for (Map.Entry<String, List<RentFeeActualInvoiceReq>> accountEntry : accountingOrgNameMap.entrySet()) {
                        String accountKey = accountEntry.getKey();
                        RentInvoiceAccountListDto accountDto = new RentInvoiceAccountListDto();
                        accountDto.setAccountingOrgName(accountKey);
                        RentInvoiceAccountGetVo accountVo = rentInvoiceAccountProvider.get(accountDto);

                        accountVo.setRemark(remarkMap.get(accountKey));
                        accountVo.setInvoiceType(invoiceType);
                        accountVo.setInvoiceLine(invoiceLine);
                        accountVo.setChargingItemType(chargingItemType);

                        List<RentFeeActualInvoiceReq> accountInvoiceList = accountEntry.getValue();
                        // 校验是否可以生成发票 获取生成发票信息
                        RentInvoice invoice = setInvoiceInfo(accountInvoiceList, accountVo, 0);
                        RentInvoice arrearsInvoice = setInvoiceInfo(accountInvoiceList, accountVo, 1);
                        RentFeeActualInvoiceResultReq resultReq = new RentFeeActualInvoiceResultReq();
                        resultReq.setInvoiceReqList(accountInvoiceList);
                        resultReq.setInvoice(invoice);
                        resultReq.setArrearsInvoice(arrearsInvoice);
                        resultReq.setAccountVo(accountVo);
                        resultList.add(resultReq);
                    }
                }
            }
            if (!CollectionUtils.isEmpty(resultList)){
                for (RentFeeActualInvoiceResultReq resultReq : resultList) {
                    RentInvoice invoice = resultReq.getInvoice();
                    RentInvoice arrearsInvoice = resultReq.getArrearsInvoice();
                    List<RentFeeActualInvoiceReq> invoiceReqList = resultReq.getInvoiceReqList();
                    RentInvoiceAccountGetVo accountVo = resultReq.getAccountVo();
                    if (invoice!=null){
                        resultFlag = generateInvoice(invoice, accountVo, invoiceReqList, chargingItemType, 0);
                    }
                    if (arrearsInvoice!=null){
                        resultFlag = generateInvoice(arrearsInvoice, accountVo, invoiceReqList, chargingItemType, 1);
                    }
                }
            }
        }
        return resultFlag;
    }

    @Override
    public List<RentFeeActualReceiptZipResp> receiptPrint(RentFeeActualReceiptZipReq zipReq) {
        OptUser curUser = ApiUtils.getUser(OptUser.class);
        List<Long> ids = zipReq.getIds();
        if (CollectionUtils.isEmpty(zipReq.getIds())){
            throw new ServiceException("请选择数据");
        }
        RentFeeActualReceiptZipDto zipDto = BeanUtils.copyProperties(zipReq, RentFeeActualReceiptZipDto.class);
        zipDto.setTenantId(curUser.getTenantId());
        List<RentFeeActualReceiptZipVo> dtoList = new ArrayList<>();
        for (Long id : ids) {
            zipDto.setActualId(id);
            List<RentFeeActualReceiptZipVo> singleList = rentFeeActualDetailLogProvider.getReceiptPrintList(zipDto);
            dtoList.addAll(singleList);
        }

        if (CollectionUtils.isEmpty(dtoList)){
            throw new ServiceException("暂无实收数据需要打印");
        }
        List<RentFeeActualReceiptZipResp> resultList = BeanUtils.copyProperties(dtoList, RentFeeActualReceiptZipResp.class);
        for (RentFeeActualReceiptZipResp resp : resultList) {
            // 获取供应商名称
            RoomsGetVo roomsGetVo = roomsProvider.get(resp.getRoomId());
            if (roomsGetVo!=null){
                resp.setSupplierName(roomsGetVo.getSupplierName());
            }
            resp.setCashier(curUser.getRealName());
            resp.setHandler(curUser.getRealName());

            // 获取序列号和项目名
            resp.setTenantName(curUser.getTenantName());
            resp.setSerialNumber(getSerialNumber());
        }

        return resultList;
    }

    @Override
    public PageResp<RentFeeActualInvoiceResp> invoicePage(RentFeeActualInvoiceReq req) {
        PageResp<RentFeeActualInvoiceResp> pageResp = new PageResp<>();
        User curUser = ApiUtils.getUser(User.class);

        RentFeeActualLogListDto queryDto = new RentFeeActualLogListDto();
        queryDto.setTenantId(curUser.getTenantId());
        queryDto.setRoomNameList(req.getRoomNameList());
        queryDto.setPeriodStartDate(req.getStartYearMonth().atDay(1));
        queryDto.setPeriodEndDate(YearMonth.from(req.getEndYearMonth()).atEndOfMonth());
        queryDto.setInvoiceLine(req.getInvoiceLine());
        queryDto.setInvoiceType(req.getInvoiceType());
        queryDto.setAccountingOrgName(req.getAccountingOrgName());

        PageQuery<RentFeeActualLogListDto> pageQuery = new PageQuery<>(req.getPageNum(), req.getPageSize(), queryDto);
        PageResult<RentFeeActualDetailLogListVo> pageResult = rentFeeActualDetailLogProvider.invoicePage(pageQuery);

        pageResp.setPageNum(req.getPageNum());
        pageResp.setPageSize(req.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), RentFeeActualInvoiceResp.class));

        return pageResp;
    }

    @Override
    public Boolean invoiceRollback(RentFeeActualInvoiceReq req) {
        Boolean resultFlag = Boolean.TRUE;
        // 回滚发票状态
        if (StringUtils.isNotEmpty(req.getInvoiceBizId())){
            rentFeeActualDetailLogProvider.rollBackInvoiceStatusById(req.getInvoiceBizId());
        }else {
            String ids = req.getIds();
            RentFeeActualDetailLogGetVo detailLogGetVo = rentFeeActualDetailLogProvider.get(Long.valueOf(ids));
            if (detailLogGetVo!=null){
                String arrearsInvoiceBizId = detailLogGetVo.getArrearsInvoiceBizId();
                if (StringUtils.isNotEmpty(arrearsInvoiceBizId)){
                    rentFeeActualDetailLogProvider.rollBackArrearsInvoiceStatusById(arrearsInvoiceBizId);
                }
            }
        }
        return resultFlag;
    }

    private Boolean generateInvoice(RentInvoice invoice, RentInvoiceAccountGetVo accountVo, List<RentFeeActualInvoiceReq> invoiceReqList, Integer chargingItemType, Integer amountType) {
        Boolean resultFlag = Boolean.TRUE;
        // 生成发票
        String invoiceContent = JsonUtils.toJson(invoice);
        RentInvoiceResult generateResult = InvoiceUtils.generate(invoiceContent, accountVo);
        log.info("generateResult:{}", JsonUtils.toJson(generateResult));
        String code = generateResult.getCode();
        String describe = generateResult.getDescribe();
        log.info("code:{}", code);
        log.info("describe:{}", describe);
        RentInvoiceResultDetail result = generateResult.getResult();
        for (RentFeeActualInvoiceReq invoiceReq : invoiceReqList) {
            if (chargingItemType ==0){
                // 非租金
                for (String id : invoiceReq.getIds().split(",")) {
                    resultFlag = updateActualDetailLog(Long.valueOf(id), code, result, describe, amountType, accountVo);
                }
            }else {
                // 租金
                for (String id : invoiceReq.getIds().split(",")) {
                    resultFlag = updateActualDetailLog(Long.valueOf(id), code, result, describe, amountType, accountVo);
                }
            }
        }
        return resultFlag;
    }

    /**
     * 修改实收明细冲抵记录表
     */
    private Boolean updateActualDetailLog(Long id, String code, RentInvoiceResultDetail result, String describe, Integer amountType, RentInvoiceAccountGetVo accountVo) {
        Boolean resultFlag = Boolean.TRUE;
        RentFeeActualDetailLogEditDto detailLogEditDto = new RentFeeActualDetailLogEditDto();
        detailLogEditDto.setId(id);
        detailLogEditDto.setInvoiceType(accountVo.getInvoiceType());
        detailLogEditDto.setInvoiceLine(accountVo.getInvoiceLine());
        if ("E0000".equals(code) && result !=null && StringUtils.isNotEmpty(result.getInvoiceSerialNum())){
            log.info("invoiceSerialNum:{}", result.getInvoiceSerialNum());
            if (amountType==0){
                detailLogEditDto.setInvoiceStatus(1);
                detailLogEditDto.setConfirmStatus(1);
                detailLogEditDto.setInvoiceBizId(result.getInvoiceSerialNum());
                detailLogEditDto.setInvoiceResult("开票中, 请稍后刷新查看");
            }else {
                detailLogEditDto.setArrearsInvoiceStatus(1);
                detailLogEditDto.setArrearsConfirmStatus(1);
                detailLogEditDto.setArrearsInvoiceBizId(result.getInvoiceSerialNum());
                detailLogEditDto.setArrearsInvoiceResult("开票中, 请稍后刷新查看");
            }
        }else {
            resultFlag = Boolean.FALSE;
            detailLogEditDto.setInvoiceStatus(2);
            detailLogEditDto.setInvoiceResult("开票失败: "+ describe);
            if (amountType!=0){
                detailLogEditDto.setArrearsInvoiceStatus(2);
                detailLogEditDto.setArrearsInvoiceResult("开票失败: "+ describe);
            }
        }
        rentFeeActualDetailLogProvider.edit(detailLogEditDto);
        return resultFlag;
    }

    private RentInvoice setInvoiceInfo(List<RentFeeActualInvoiceReq> reqList, RentInvoiceAccountGetVo accountVo, Integer amountType) {
        RentInvoice invoice = new RentInvoice();
        RentFeeActualInvoiceReq invoiceReq = reqList.get(0);
        Long tenantId = invoiceReq.getTenantId();
        String tenantName = invoiceReq.getTenantName();
        String contractCode = invoiceReq.getContractCode();
        String roomName = invoiceReq.getRoomName();
        LocalDate periodStart = invoiceReq.getPeriodStart();
        LocalDate periodEnd = invoiceReq.getPeriodEnd();

        String taxNum = accountVo.getTaxNum();
        String departmentId = accountVo.getDepartmentId();

        String invoiceType = accountVo.getInvoiceType();
        String invoiceLine = accountVo.getInvoiceLine();
        Integer chargingItemType = accountVo.getChargingItemType();
        String shopRemark = accountVo.getRemark();

        Long id = IdUtil.getSnowflakeNextId();

        CommerceContractInfoGetVo contractInfoVo = null;
        if (contractCode.startsWith("D")||contractCode.startsWith("G")) {
            CommerceAdvertContractListDto advertDto = new CommerceAdvertContractListDto();
            advertDto.setTenantId(tenantId);
            advertDto.setContractCode(contractCode);
            CommerceAdvertContractGetVo advertGetVo = advertContractProvider.get(advertDto);
            contractInfoVo = BeanUtils.copyProperties(advertGetVo, CommerceContractInfoGetVo.class);
        }else {
            CommerceContractInfoListDto contractInfoDto = new CommerceContractInfoListDto();
            contractInfoDto.setTenantId(tenantId);
            contractInfoDto.setContractCode(contractCode);
            contractInfoVo = commerceContractInfoProvider.get(contractInfoDto);
        }
        if (contractInfoVo==null || contractInfoVo.getSupplierId()==null){
            throw new ServiceException("店铺: "+roomName+" 找不到对应的合同信息,无法生成发票");
        }
        Long supplierId = contractInfoVo.getSupplierId();
        CommerceSupplierGetVo supplierVo = commerceSupplierProvider.get(supplierId);
        if (supplierVo==null || StringUtils.isEmpty(supplierVo.getName())){
            throw new ServiceException("店铺: "+roomName+" 对应的供应商不存在,无法生成发票");
        }
        if ("bs".equals(invoiceLine) && StringUtils.isEmpty(supplierVo.getTaxNo())){
            throw new ServiceException("店铺: "+roomName+" 专票对应的供应商税号不存在,无法生成发票");
        }
        EntProjectGetVo entProjectVo = entProjectProvider.get(tenantId);
        if (entProjectVo==null || StringUtils.isEmpty(entProjectVo.getFaddressdetail())){
            throw new ServiceException("项目对应的地址不存在,无法生成发票");
        }
        String address = entProjectVo.getFaddressdetail();
        log.info("address:{}", address);
        if (!address.contains("市")){
            throw new ServiceException("详细地址必须包含前三级行政地址");
        }
        if (!address.contains("街") && !address.contains("路") && !address.contains("村") && !address.contains("乡") && !address.contains("镇") && !address.contains("道")){
            throw new ServiceException("详细地址必须包含街、路、村、乡、镇、道关键词");
        }
        String[] proCityAreaArr = AnalysisIdCardUtil.spliceDetailedAddress(address);
        String proName = StringUtils.isEmpty(proCityAreaArr[0]) ? "" : proCityAreaArr[0];
        String cityName = StringUtils.isEmpty(proCityAreaArr[1]) ? "" : proCityAreaArr[1];
        String areaName = StringUtils.isEmpty(proCityAreaArr[2]) ? "" : proCityAreaArr[2];
        String detailName = StringUtils.isEmpty(proCityAreaArr[3]) ? "" : proCityAreaArr[3];

        String addressPrefix = proName+cityName+areaName;
        String addressSuffix = detailName;
        String proSuffix = cityName+areaName+detailName;

        List<String> checkNameList = new ArrayList<>();
        StringBuffer strBuffer = new StringBuffer();
        if (chargingItemType==1){
            strBuffer.append(tenantName);
        }else {
            strBuffer.append(proSuffix);
        }
        strBuffer.append(roomName);
        strBuffer.append(supplierVo.getName());
        strBuffer.append(": ");

        RentInvoiceOrder invoiceOrder = new RentInvoiceOrder();
        invoiceOrder.setBuyerName(supplierVo.getName());
        invoiceOrder.setBuyerTaxNum(supplierVo.getTaxNo());
        // invoiceOrder.setBuyerTel(supplierVo.getContactPhon());

        invoiceOrder.setSalerTaxNum(taxNum);

        invoiceOrder.setOrderNo(String.valueOf(id));
        invoiceOrder.setInvoiceDate(cn.hutool.core.date.DateUtil.now());

        invoiceOrder.setDepartmentId(departmentId);

        if (StringUtils.isNotEmpty(supplierVo.getEmail())){
            invoiceOrder.setPushMode("0");
            invoiceOrder.setEmail(supplierVo.getEmail());
        }

        invoiceOrder.setInvoiceType(invoiceType);
        invoiceOrder.setInvoiceLine(invoiceLine);
        invoiceOrder.setSpecificFactor("0");
        // invoiceOrder.setIsIgnoreType("1");
        // 租金模版
        if (chargingItemType==1){
            invoiceOrder.setSpecificFactor("6");
            RentInvoiceRealInfo realInfo = new RentInvoiceRealInfo();
            realInfo.setRealPropertyAddress(addressPrefix);
            realInfo.setDetailAddress(addressSuffix);
            realInfo.setRentStartDate(periodStart.toString());
            realInfo.setRentEndDate(periodEnd.toString());
            realInfo.setCrossCityFlag("0");
            realInfo.setUnit("2");
            invoiceOrder.setRealPropertyRentInfo(realInfo);
        }

        List<RentInvoiceDetail> invoiceDetailList = new ArrayList<>();

        for (RentFeeActualInvoiceReq detailLogVo : reqList) {
            // 校验属期
            if (detailLogVo.getPeriodStart()==null || detailLogVo.getPeriodEnd()==null){
                throw new ServiceException("店铺: "+roomName+" 对应的收费项目: "+detailLogVo.getChargingItemName()+" 属期不存在, 无法生成发票");
            }
            // 获取应收明细
            Long chargingItemId = detailLogVo.getChargingItemId();
            // 获取收费项目税收分类编码
            RentChargingTenantItemListDto tenantItemDto = new RentChargingTenantItemListDto();
            tenantItemDto.setTenantId(tenantId);
            tenantItemDto.setChargingItemId(chargingItemId);
            RentChargingTenantItemGetVo tenantItemVo = rentChargingTenantItemProvider.get(tenantItemDto);
            if (tenantItemVo==null || StringUtils.isEmpty(tenantItemVo.getTaxCategoryCode())){
                throw new ServiceException("店铺: "+roomName+" 对应的收费项目: "+detailLogVo.getChargingItemName()+"税收分类编码不存在,无法生成发票");
            }
            for (int i = 0; i < 2; i++) {
                if (i==0 && amountType==0){
                    // 本金费用
                    if (detailLogVo.getActualAmount()!=null && detailLogVo.getActualAmount().compareTo(BigDecimal.ZERO) > 0){
                        RentInvoiceDetail invoiceDetail = new RentInvoiceDetail();
                        // 实收本金:含税
                        BigDecimal actualAmount = detailLogVo.getActualAmount();
                        // 实收本金:不含税
                        BigDecimal excludeTaxActualAmount = detailLogVo.getExcludeTaxActualAmount();
                        if (excludeTaxActualAmount==null || excludeTaxActualAmount.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目: "+detailLogVo.getChargingItemName()+" 不含税本金小于等于0, 无法生成发票");
                        }
                        // 实收本金: 税率
                        BigDecimal actualAmountTaxRate = detailLogVo.getActualAmountTaxRate();
                        if (actualAmountTaxRate==null || actualAmountTaxRate.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目: "+detailLogVo.getChargingItemName()+" 税率小于等于0, 无法生成发票");
                        }
                        BigDecimal taxRate = actualAmountTaxRate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                        /*BigDecimal actualAmountTax = detailLogVo.getActualAmountTax();
                        if (actualAmountTax==null || actualAmountTax.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目: "+detailLogVo.getChargingItemName()+" 税额小于等于0, 无法生成发票");
                        }*/
                        invoiceDetail.setGoodsName(tenantItemVo.getTaxExtendName());
                        invoiceDetail.setGoodsCode(tenantItemVo.getTaxCategoryCode());
                        invoiceDetail.setWithTaxFlag("0");
                        //invoiceDetail.setTax(actualAmountTax.toString());
                        invoiceDetail.setTaxRate(taxRate.toString());
                        invoiceDetail.setTaxExcludedAmount(excludeTaxActualAmount.toString());
                        invoiceDetail.setTaxIncludedAmount(actualAmount.toString());
                        // 用量
                        invoiceDetail.setNum("1");
                        if (detailLogVo.getTotalCount()!=null && detailLogVo.getTotalCount().compareTo(BigDecimal.ZERO) > 0){
                            invoiceDetail.setNum(detailLogVo.getTotalCount().toString());
                        }
                        if (tenantItemVo.getPreferentialFlag()!=null && tenantItemVo.getPreferentialFlag().equals(1)){
                            invoiceDetail.setFavouredPolicyFlag("01");
                        }
                        // 非租金模版
                        if (chargingItemType==0){
                            invoiceDetail.setSpecType(detailLogVo.getPeriodStart()+"-"+detailLogVo.getPeriodEnd());
                            // invoiceDetail.setUnit("无");
                            // 获取属期结束的月份
                            String remarkMonthItem = detailLogVo.getPeriodEnd().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            if (!checkNameList.contains(remarkMonthItem)){
                                strBuffer.append(remarkMonthItem+"、");
                                checkNameList.add(remarkMonthItem);
                            }
                        }else {
                            // 租金模版
                            String startMonthItem = detailLogVo.getPeriodStart().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            String endMonthItem = detailLogVo.getPeriodEnd().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            strBuffer.append(startMonthItem+"、");
                            if (!startMonthItem.equals(endMonthItem)){
                                strBuffer.append(endMonthItem+"、");
                            }
                        }
                        invoiceDetailList.add(invoiceDetail);
                    }
                }else if (i==1 && amountType==1){
                    // 滞纳金费用
                    if (detailLogVo.getActualArrearsAmount()!=null && detailLogVo.getActualArrearsAmount().compareTo(BigDecimal.ZERO)>0){
                        RentInvoiceDetail invoiceDetail = new RentInvoiceDetail();
                        // 实收滞纳金:含税
                        BigDecimal actualArrearsAmount = detailLogVo.getActualArrearsAmount();
                        // 实收滞纳金:不含税
                        BigDecimal excludeTaxArrearsAmount = detailLogVo.getExcludeTaxArrearsAmount();
                        if (excludeTaxArrearsAmount==null || excludeTaxArrearsAmount.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目滞纳金: "+detailLogVo.getChargingItemName()+" 不含税本金小于等于0, 无法生成发票");
                        }
                        // 实收滞纳金: 税率
                        BigDecimal arrearsAmountTaxRate = detailLogVo.getArrearsAmountTaxRate();
                        if (arrearsAmountTaxRate==null || arrearsAmountTaxRate.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目滞纳金: "+detailLogVo.getChargingItemName()+" 税率小于等于0, 无法生成发票");
                        }
                        BigDecimal taxRate = arrearsAmountTaxRate.divide(new BigDecimal(100), 2, RoundingMode.HALF_UP);
                        // 实收滞纳金: 税额
                        /*BigDecimal arrearsAmountTax = detailLogVo.getArrearsAmountTax();
                        if (arrearsAmountTax==null || arrearsAmountTax.compareTo(BigDecimal.ZERO)<=0){
                            throw new ServiceException("店铺: "+roomName+" 对应的收费项目滞纳金: "+detailLogVo.getChargingItemName()+" 税额小于等于0, 无法生成发票");
                        }*/
                        invoiceDetail.setGoodsName(tenantItemVo.getTaxExtendName()+"滞纳金");
                        invoiceDetail.setGoodsCode(tenantItemVo.getTaxCategoryCode());
                        invoiceDetail.setWithTaxFlag("0");
                        //invoiceDetail.setTax(arrearsAmountTax.toString());
                        invoiceDetail.setTaxRate(taxRate.toString());
                        invoiceDetail.setTaxExcludedAmount(excludeTaxArrearsAmount.toString());
                        invoiceDetail.setTaxIncludedAmount(actualArrearsAmount.toString());
                        // 用量
                        invoiceDetail.setNum("1");
                        if (detailLogVo.getTotalCount()!=null && detailLogVo.getTotalCount().compareTo(BigDecimal.ZERO) > 0){
                            invoiceDetail.setNum(detailLogVo.getTotalCount().toString());
                        }
                        if (tenantItemVo.getPreferentialFlag()!=null && tenantItemVo.getPreferentialFlag().equals(1)){
                            invoiceDetail.setFavouredPolicyFlag("01");
                        }
                        // 非租金模版
                        if (chargingItemType==0){
                            invoiceDetail.setSpecType(detailLogVo.getPeriodStart()+"-"+detailLogVo.getPeriodEnd());
                            // invoiceDetail.setUnit("无");
                            // 获取属期结束的月份
                            String remarkMonthItem = detailLogVo.getPeriodEnd().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            if (!checkNameList.contains(remarkMonthItem)){
                                strBuffer.append(remarkMonthItem+"、");
                                checkNameList.add(remarkMonthItem);
                            }
                        }else {
                            // 租金模版
                            String startMonthItem = detailLogVo.getPeriodStart().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            String endMonthItem = detailLogVo.getPeriodEnd().getMonthValue() + "月" + tenantItemVo.getTaxExtendName();
                            strBuffer.append(startMonthItem+"、");
                            if (!startMonthItem.equals(endMonthItem)){
                                strBuffer.append(endMonthItem+"、");
                            }
                        }
                        invoiceDetailList.add(invoiceDetail);
                    }
                }
            }
        }

        if (CollectionUtils.isEmpty(invoiceDetailList)){
            return null;
        }

        invoiceOrder.setInvoiceDetail(invoiceDetailList);

        String remark = strBuffer.toString();
        remark = remark.substring(0, remark.length() - 1);
        if (StringUtils.isNotEmpty(shopRemark)){
            remark = remark + "，" + shopRemark;
        }
        invoiceOrder.setRemark(remark);
        invoice.setOrder(invoiceOrder);

        log.info("invoice:{}", JsonUtils.toJson(invoice));
        return invoice;
    }

    private  void importExcel(InputStream inputstream,SysFileGetVo sysFileGetVo,Long fileId){
        //导入实收日志数据。直接调用实收录入日志的ADD方法。
        LocalDateTime now= LocalDateTime.now();
        OptUser curUser = null;
        if (fileId == -1) {
            curUser = ApiUtils.getUser(OptUser.class);
            if(curUser==null){
                curUser =new OptUser();
                curUser.setTenantId(1779852125880651777l);
            }
        } else {
            curUser = ApiUtils.getUser(OptUser.class);
        }
        //解析数据
        List<RentFeeActualImportItem> addBatchList = EasyExcel.read(inputstream).head(RentFeeActualImportItem.class).sheet(0).headRowNumber(1).doReadSync();
        log.info("导入记录数：{}", addBatchList.size());
        //导入记录
        RentFeeImportAddDto rentFeeImportAddDto = new RentFeeImportAddDto();
        rentFeeImportAddDto.setTenantId(curUser.getTenantId());
        rentFeeImportAddDto.setFileName(sysFileGetVo.getFileName());
        rentFeeImportAddDto.setFileId(fileId);
        rentFeeImportAddDto.setStatus(1);
        rentFeeImportAddDto.setBizType(2);
        rentFeeImportAddDto.setTotalCount(0);
        rentFeeImportAddDto.setFailCount(0);
        log.info("导入文件名称：{}", sysFileGetVo.getFileName());
        if (CollectionUtils.isEmpty(addBatchList) || addBatchList.size() < 1) {
            //空数据
            rentFeeImportAddDto.setTotalCount(0);
            rentFeeImportAddDto.setFailCount(0);
            rentFeeImportAddDto.setCreateTime(now);
            rentFeeImportProvider.add(rentFeeImportAddDto);
            return;
        }
            List<RentFeeImportDetailAddDto> rentFeeImportDetailAddDtos = new ArrayList<>();
        //过滤后正确的数据。
            List<RentFeeActualImportItem> resultList =  new ArrayList<>();
        for (int i = 0; i < addBatchList.size(); i++) {
                RentFeeActualImportItem item = addBatchList.get(i);
            try {
                if (StringUtils.isEmpty(item.getContractCode())) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "合同编号不能为空",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (StringUtils.isEmpty(item.getAccountingOrgName())) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "核算组织名称不能为空",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (item.getActualAmount() == null) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "录入金额不能为空也不能小于等于0",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //录入金额如果为空不校验，不为空不能小于0
                if(!BigDecimalUtil.isNumeric(item.getActualAmount())){
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "录入金额不能非数字格式",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (BigDecimalUtil.compareTo(new BigDecimal(item.getActualAmount()),BigDecimal.ZERO) <= 0) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "录入金额不能小于等于0",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (item.getPaymentDate()==null) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "缴费日期不能为空",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                if (!DateUtil.isValidDate(item.getPaymentDate())) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "提供的”缴费日期”不符合日期格式!请使用yyyy-MM-dd的格式",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                //根据合同查询最新的实收ID。往最新的实收ID插入数据
                //通过核算组织名称查询ID。
                RentVoucherTenantInfoListDto tenantInfoListDto=new RentVoucherTenantInfoListDto();
                tenantInfoListDto.setAccountingOrgName(item.getAccountingOrgName());
                tenantInfoListDto.setTenantId(curUser.getTenantId());
                RentVoucherTenantInfoGetVo rentVoucherTenantInfoGetVo = rentVoucherTenantInfoProvider.get(tenantInfoListDto);
                RentFeeActualGetVo getVo = rentFeeActualProvider.getOneByContractCode(item.getContractCode(),rentVoucherTenantInfoGetVo.getId());

                //判断导入数据是否在应收中存在
                if (getVo == null) {
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "该合同在实收数据中不存在！",item.getContractCode(), item.getStoreId()));
                    continue;
                }
                // 如果该主体和合同 存在之前的缴费数据。
                RentFeeActualLogListDto dto=new RentFeeActualLogListDto();
                dto.setPaymentDate(DateUtil.parseDateString(item.getPaymentDate()));
                dto.setVoucherTenantInfoId(rentVoucherTenantInfoGetVo.getId());
                dto.setContractCode(item.getContractCode());
                List<RentFeeActualLogListVo> actualLogByContractCode = rentFeeActualLogProvider.getActualLogByContractCode(dto);
                if(actualLogByContractCode.size()>0){
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "仅支持导入已录入实收最新缴费日期之后的实收！",item.getContractCode(), item.getStoreId()));
                    continue;
                }

                item.setLine(i + 1);
                item.setActualId(getVo.getId());
                rentFeeImportAddDto.setTotalCount(rentFeeImportAddDto.getTotalCount()+1);
            } catch (Exception e) {
                log.error("导出模板异常: {}", e.getMessage(), e);
                rentFeeImportAddDto.setFailCount(rentFeeImportAddDto.getFailCount()+1);
                rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "数据异常！",item.getContractCode(), item.getStoreId()));
                continue;
            }
            resultList.add(item);
        }

            //按照日期和合同排序 升序排序
            List<RentFeeActualImportItem> sortList = resultList.stream()
                    .sorted(Comparator.comparing(RentFeeActualImportItem::getContractCode)
                            .thenComparing(item->DateUtil.parseDateString(item.getPaymentDate())))
                    .collect(Collectors.toList());
            for (int i = 0; i < sortList.size(); i++) {

                    RentFeeActualImportItem item = sortList.get(i);
                try {
                    //导入成功记录
                    RentFeeActualLogAddReq addReq = new RentFeeActualLogAddReq();
                    addReq.setActualId(item.getActualId());
                    LocalDate paymentDate = DateUtil.parseDateString(item.getPaymentDate());
                    addReq.setPaymentDate(paymentDate);
                    addReq.setActualAmount(new BigDecimal(item.getActualAmount()));
                    addReq.setDataType(0);
                    addReq.setImpStatus(0);
                    rentFeeActualLogService.add(addReq);
                    log.info("导入成功记录：{}", item.getLine());
                    RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
                    addDto.setLine(item.getLine());
                    addDto.setStatus(1);
                    addDto.setFname(item.getContractCode());
                    addDto.setName(item.getStoreId());
                    rentFeeImportDetailAddDtos.add(addDto);
                } catch (ServiceException e) {
                    log.error("导出模板异常: {}", e.getMessage(), e);
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, e.getMessage(),item.getContractCode(), item.getStoreId()));
                }catch (Exception e) {
                    log.error("导出模板异常: {}", e.getMessage(), e);
                    rentFeeImportDetailAddDtos.add(createImportDetailDto(i, "数据异常！",item.getContractCode(), item.getStoreId()));
                }
            }
            rentFeeReceivableProvider.importList(rentFeeImportAddDto, rentFeeImportDetailAddDtos);
    }
    private RentFeeImportDetailAddDto createImportDetailDto(int i, String msg,String fname,String name) {
        RentFeeImportDetailAddDto addDto = new RentFeeImportDetailAddDto();
        addDto.setLine(i + 1);
        addDto.setStatus(2);
        //合同名称
        addDto.setFname(fname);
        //门店ID
        addDto.setName(name);
        addDto.setFailReason(msg);
        return addDto;
    }

    private String getSerialNumber() {
        LocalDate now = LocalDate.now();
        String yearMonthDay = String.format("%02d%02d%02d",
                now.getYear() % 100,
                now.getMonthValue(),
                now.getDayOfMonth()
        );

        int randomNumber = ThreadLocalRandom.current().nextInt(100000, 1000000);

        return yearMonthDay + randomNumber;
    }

}
