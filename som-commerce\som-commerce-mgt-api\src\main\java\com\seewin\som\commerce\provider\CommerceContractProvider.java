package com.seewin.som.commerce.provider;

import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;
import com.seewin.som.commerce.req.*;
import com.seewin.som.commerce.resp.*;
import com.seewin.util.exception.ServiceException;

import java.time.LocalDate;
import java.util.List;

/**
 * <p>
 * 招商合同表-商铺 API接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-22
 */
public interface CommerceContractProvider {

	/**
     * <p>分页查询<br>
     *
     * @param pageQuery 分页查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    PageResult<CommerceContractListVo> page(PageQuery<CommerceContractListDto> pageQuery) throws ServiceException;

    /**
     * <p>全量查询<br>
     *
     * @param dto 查询条件Dto
     * @return 查询结果
     * @throws ServiceException 服务处理异常
     */
    List<CommerceContractListVo> list(CommerceContractListDto dto) throws ServiceException;

    /**
     * <p>记录数查询<br>
     *
     * @param dto 查询条件Dto
     * @return 记录数
     * @throws ServiceException 服务处理异常
     */
    int count(CommerceContractListDto dto) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param id 主键
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    CommerceContractGetVo get(Long id) throws ServiceException;

    /**
     * <p>详情查询<br>
     *
     * @param dto 查询条件Dto
     * @return 详情VO
     * @throws ServiceException 服务处理异常
     */
    CommerceContractGetVo get(CommerceContractListDto dto) throws ServiceException;


    /**
     * <p>新增<br>
     *
     * @param dto 新增数据Dto
     * @return 响应VO（包含主键）
     * @throws ServiceException 服务处理异常
     */
    CommerceContractAddVo add(CommerceContractAddDto dto) throws ServiceException;

    /**
     * <p>修改<br>
     *
     * @param dto 修改数据Dto
     * @throws ServiceException 服务处理异常
     */
    CommerceContractGetVo edit(CommerceContractEditDto dto) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param id 主键
     * @throws ServiceException 服务处理异常
     */
    void delete(Long id) throws ServiceException;

    /**
     * <p>删除<br>
     *
     * @param dto 删除条件Dto
     * @throws ServiceException 服务处理异常
     */
    void delete(CommerceContractListDto dto) throws ServiceException;

    /**
     * 获取最新合同
     * @param roomId
     * @return
     * @throws ServiceException
     */
    CommerceContractGetVo getLastContract(Long roomId) throws ServiceException;

    CommerceContractArchiveVo archive(CommerceContractArchiveDto dto) throws ServiceException;

    void contractPassWithOrder(Long contractId);

    CommerceContractArchiveVo contractRentDateStart(CommerceContractArchiveVo contractArchiveVo);

    CommerceContractStoreGetVo getStoreVoByContractCode(String contractCode) throws ServiceException;

    void updateApprove(CommerceContractEditDto commerceContractEditDto) throws ServiceException;

    void updateProcessInstanceId(Long id, String processInstanceId) throws ServiceException;

    List<CommerceContractHistoryListVo> historyList(CommerceContractListDto listDto) throws ServiceException;

    boolean checkContract(String orderCode) throws ServiceException;

    CommerceContractGetVo selectByRoomIdAndApproveStatus(Long roomId, Integer approveStatus);

    /**
     * TODO
     * <AUTHOR>
     * @date 2024/5/16 17:49
     * @param billMonth
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractBillMonthVo>
     */
    List<CommerceContractBillMonthVo> selectContractByBillMonth(String billMonth);
    
    List<CommerceContractArchiveVo> findRentStartContract(LocalDate date);

    List<CommerceContractListVo> contractApproveOvertime(LocalDate date) throws ServiceException;

    String getShopId(String contractName, Long roomId, boolean isNewRoomShopId);

    /**
     * 获取每月账单的需要的合同
     * <AUTHOR>
     * @date 2024/6/26 15:16
     * @return java.util.List<com.seewin.som.commerce.resp.CommerceContractMonthFeeVo>
     */
    List<CommerceContractMonthFeeVo> selectContractMonthFee(String type);

    /**
     * 续签-更新原合同计费截止日期
     *
     * @param contractCode
     */
    void updateRenewContract(String contractCode);

    /**
     * 本次合同的前一份合同信息
     * @param id
     * @return
     */
    CommerceContractGetVo getLastOneContract(Long id);

    /**
     * 获取项目最新的合同编码递增后缀数量
     * @return
     */
    Long getContractCodeNum(String tenantName);

    /**
     * 获取项目最新的合同编码
     * @return
     */
    String getContractCode(String projectCode, Long count);

    /**
     * 入库合同传签数据
     * @param excelDto
     */
    void saveImportExcel(CommerceContractImportExcelDto excelDto);

    List<CommerceEffectiveContractVo> selectEffectiveContract(LocalDate currentDate);
    List<CommerceEffectiveContractVo> selectEffectiveContractByContractCode(List<String> contractCodeList);
    CommerceEffectiveContractVo getRentFeeCalculateInfo(String contractCode);

    /**
     * <p>修正<br>
     *
     * @param dto 修正数据Dto
     * @throws ServiceException 服务处理异常
     */
    CommerceContractGetVo correct(CommerceContractEditDto dto) throws ServiceException;

    void editArea(CommerceContractEditDto contractEditDto);

    List<CommerceContractRentFeeExportVo> getContractFeeList(CommerceContractRentFeeExportDto exportDto);

    CommerceContractArchiveVo findSignContract(Long roomId);

    List<CommerceChargeContractListVo> listByReceivable(CommerceChargeContractListDto dto);

    void updateUserInput(CommerceContractUpdateUserInputDto dto);
}
