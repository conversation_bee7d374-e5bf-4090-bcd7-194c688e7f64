package com.seewin.som.commerce.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <p>
 * 零售供应商表
 * </p>
 *
 * <AUTHOR>
 * @since 2024-03-15
 */
@Data
@TableName("som_commerce_supplier")
public class CommerceSupplier implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 租户id
     */
    @TableField("tenant_id")
    private Long tenantId;

    /**
     * 租户名称
     */
    @TableField("tenant_name")
    private String tenantName;

    /**
     * 企业ID
     */
    @TableField("ent_id")
    private Long entId;

    /**
     * 所属组织ID路径
     */
    @TableField("org_fid")
    private String orgFid;

    /**
     * 所属组织名称路径
     */
    @TableField("org_fname")
    private String orgFname;

    /**
     * 供应商名称
     */
    @TableField("name")
    private String name;

    /**
     * 1.身份证、2.营业执照

     */
    @TableField("certificate_type")
    private Integer certificateType;

    /**
     * 零售供应商身份证地址
     */
    @TableField("card_address")
    private String cardAddress;

    /**
     * 零售供应商身份证证件号
     */
    @TableField("card_number")
    private String cardNumber;

    /**
     * 联系人姓名
     */
    @TableField("contact_name")
    private String contactName;

    /**
     * 联系人岗位，数据字典：contact_position
     */
    @TableField("contact_job")
    private String contactJob;

    /**
     * 联系人方式（号码）
     */
    @TableField("contact_phon")
    private String contactPhon;

    /**
     * 营业执照公司名称
     */
    @TableField("company_name")
    private String companyName;

    /**
     * 营业执照统一社会信用代码
     */
    @TableField("social_credit_code")
    private String socialCreditCode;

    /**
     * 营业执照法定代表人
     */
    @TableField("legal_representative")
    private String legalRepresentative;

    /**
     * 在营开店数

     */
    @TableField("store_num")
    private Integer storeNum;

    /**
     * 开店城市，、分隔
     */
    @TableField("citys")
    private String citys;

    /**
     * 邮箱
     */
    @TableField("email")
    private String email;

    /**
     * 税号
     */
    @TableField("tax_no")
    private String taxNo;

    /**
     * 零售供应商违约事件（数据来源于营运管理-日常巡场）
     */
    @TableField("break_num")
    private Integer breakNum;
    /**
     * 零售供应商安全事件（数据来源于营运管理-日常巡场）
     */
    @TableField("safe_num")
    private Integer safeNum;
    /**
     * 零售供应商质量事件（数据来源于营运管理-日常巡场）
     */
    @TableField("quality_num")
    private Integer qualityNum;
    /**
     * 零售供应商安全事件A
     */
    @TableField(value = "retail_supplier_security_A")
    private Integer retailSupplierSecurityA;

    /**
     * 零售供应商安全事件B
     */
    @TableField(value = "retail_supplier_security_B")
    private Integer retailSupplierSecurityB;

    /**
     * 零售供应商安全事件C
     */
    @TableField(value = "retail_supplier_security_C")
    private Integer retailSupplierSecurityC;
    /**
     * 供应商小程序openId
     */
    @TableField("open_id")
    private String openId;
    /**
     * 创建人id
     */
    @TableField("create_by")
    private Long createBy;

    /**
     * 创建人账号/手机号
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建人姓名/昵称
     */
    @TableField("create_user_name")
    private String createUserName;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private LocalDateTime createTime;

    /**
     * 修改人id
     */
    @TableField("update_by")
    private Long updateBy;

    /**
     * 修改人账号/手机号
     */
    @TableField("update_user")
    private String updateUser;

    /**
     * 修改人姓名/昵称
     */
    @TableField("update_user_name")
    private String updateUserName;

    /**
     * 修改时间
     */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /**
     * 是否已删除: 0-否，1-是
     */
    @TableField("del_status")
    @TableLogic
    private Integer delStatus;

    /**
     * 乐观锁
     */
    @TableField("version")
    @Version
    private Integer version;

    /**
     * 审批状态: 2 审批中，3 已通过，4 已驳回
     */
    @TableField("approve_status")
    private Integer approveStatus;
    /**
     * 流程实例ID
     */
    @TableField("process_instance_id")
    private String processInstanceId;
    /**
     * 审批时间
     */
    @TableField("approve_date")
    private LocalDate approveDate;
    /**
     * 用户类型：1-初级用户，2-认证用户，3-历史用户，4-在营用户
     */
    @TableField("user_type")
    private Integer userType;
    /**
     * 邀请人用户ID
     */
    @TableField("inviter_user_id")
    private Long inviterUserId;

    /**
     * 报税人类型 1.一般纳税人 2.小规模纳税人 3.个人
     */
    @TableField("tax_people_type")
    private Integer taxPeopleType;
}
