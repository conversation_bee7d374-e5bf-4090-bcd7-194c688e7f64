package com.seewin.som.commerce.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.style.WriteCellStyle;
import com.alibaba.excel.write.style.HorizontalCellStyleStrategy;
import com.seewin.model.base.OptUser;
import com.seewin.som.commerce.service.CommerceStoreManageDetailService;
import com.seewin.som.commerce.vo.resp.*;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletResponse;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.springframework.stereotype.Service;
import org.apache.dubbo.config.annotation.DubboReference;

import com.seewin.util.bean.BeanUtils;
import com.seewin.consumer.vo.PageResp;
import com.seewin.consumer.data.ApiUtils;
import com.seewin.model.base.User;
import com.seewin.model.query.PageQuery;
import com.seewin.model.query.PageResult;

import com.seewin.som.commerce.provider.CommerceStoreManageDetailProvider;
import com.seewin.som.commerce.req.CommerceStoreManageDetailAddDto;
import com.seewin.som.commerce.req.CommerceStoreManageDetailEditDto;
import com.seewin.som.commerce.req.CommerceStoreManageDetailListDto;
import com.seewin.som.commerce.resp.CommerceStoreManageDetailAddVo;
import com.seewin.som.commerce.resp.CommerceStoreManageDetailGetVo;
import com.seewin.som.commerce.resp.CommerceStoreManageDetailListVo;

import com.seewin.som.commerce.vo.req.CommerceStoreManageDetailListReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageDetailGetReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageDetailAddReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageDetailEditReq;
import com.seewin.som.commerce.vo.req.CommerceStoreManageDetailDelReq;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.net.URLEncoder;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;

/**
 * <p>
 * 开闭店管理明细表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-21
 */
@Service
public class CommerceStoreManageDetailServiceImpl implements CommerceStoreManageDetailService {

	/**
     * providedBy：兼容Mesh服务
     */
	@DubboReference(providedBy = "som-commerce-mgt")
	private CommerceStoreManageDetailProvider commerceStoreManageDetailProvider;
	
	/**
     * <p>分页查询<br>
     *
     * @param listReq 分页查询条件VO
     * @return 查询结果
     */
    @Override
    public PageResp<CommerceStoreManageDetailListItem> page(CommerceStoreManageDetailListReq listReq) {
        PageResp<CommerceStoreManageDetailListItem> pageResp = new PageResp<>();
		User curUser = ApiUtils.getUser(User.class);
		
        CommerceStoreManageDetailListDto queryDto = BeanUtils.copyProperties(listReq, CommerceStoreManageDetailListDto.class);
        
        PageQuery<CommerceStoreManageDetailListDto> pageQuery = new PageQuery<>(listReq.getPageNum(), listReq.getPageSize(), queryDto);
        PageResult<CommerceStoreManageDetailListVo> pageResult = commerceStoreManageDetailProvider.page(pageQuery);

        pageResp.setPageNum(listReq.getPageNum());
        pageResp.setPageSize(listReq.getPageSize());
        pageResp.setPages(pageResult.getPages());
        pageResp.setTotal(pageResult.getTotal());
        pageResp.setItems(BeanUtils.copyProperties(pageResult.getItems(), CommerceStoreManageDetailListItem.class));

        return pageResp;
    }

    /**
     * <p>详情查询<br>
     *
     * @param getReq
     * @return
     */
    @Override
    public CommerceStoreManageDetailGetResp get(CommerceStoreManageDetailGetReq getReq) {
        CommerceStoreManageDetailGetVo getVo = commerceStoreManageDetailProvider.get(getReq.getId());

        CommerceStoreManageDetailGetResp getResp = BeanUtils.copyProperties(getVo, CommerceStoreManageDetailGetResp.class);

        return getResp;
    }

    /**
     * <p>新增<br>
     *
     * @param addReq
     * @return
     */
    @Override
    public CommerceStoreManageDetailAddResp add(CommerceStoreManageDetailAddReq addReq) {
        CommerceStoreManageDetailAddDto dto = BeanUtils.copyProperties(addReq, CommerceStoreManageDetailAddDto.class);

		//设置创建人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setCreateBy(curUser.getUserId());
        dto.setCreateUser(curUser.getUserName());
        dto.setCreateUserName(curUser.getRealName());
       
		
        CommerceStoreManageDetailAddVo addVo = commerceStoreManageDetailProvider.add(dto);

        CommerceStoreManageDetailAddResp addResp = BeanUtils.copyProperties(addVo, CommerceStoreManageDetailAddResp.class);

        return addResp;
    }

    /**
     * <p>修改<br>
     *
     * @param editReq
     */
    @Override
    public void edit(CommerceStoreManageDetailEditReq editReq) {
        CommerceStoreManageDetailEditDto dto = BeanUtils.copyProperties(editReq, CommerceStoreManageDetailEditDto.class);

        //设置修改人信息
        User curUser = ApiUtils.getUser(User.class);
        dto.setUpdateBy(curUser.getUserId());
        dto.setUpdateUser(curUser.getUserName());
        dto.setUpdateUserName(curUser.getRealName());
        commerceStoreManageDetailProvider.edit(dto);
    }

    /**
     * <p>删除<br>
     *
     * @param delReq
     */
    @Override
    public void del(CommerceStoreManageDetailDelReq delReq) {
        commerceStoreManageDetailProvider.delete(delReq.getId());
    }

    @Override
    public void exp(HttpServletResponse response, CommerceStoreManageDetailListReq req) {
        OptUser optUser = ApiUtils.getUser(OptUser.class);
        String tenantName = optUser.getTenantName();

        DateTimeFormatter dataFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

        req.setPageNum(1);
        req.setPageSize(-1);
        List<CommerceStoreManageDetailListItem> storeDetailList = page(req).getItems();
        if (CollectionUtil.isEmpty(storeDetailList)){
            return;
        }
        List<CommerceStoreManageDetailExp> expResp = new ArrayList<>();
        for (CommerceStoreManageDetailListItem item : storeDetailList) {
            CommerceStoreManageDetailExp detailExp = new CommerceStoreManageDetailExp();
            detailExp.setTenantName(tenantName);
            String dataStr = item.getCreateTime().format(dataFormatter);
            detailExp.setDataStr(dataStr);
            detailExp.setStoreId(item.getStoreId());

            String roomStatusStr = "";
            String roomStatus = item.getRoomStatus();
            if (StringUtils.isNotEmpty(roomStatus)){
                if (roomStatus.equals("no_delivered")){
                    roomStatusStr = "未交付";
                }else if (roomStatus.equals("no_open")){
                    roomStatusStr = "未开业";
                }else if (roomStatus.equals("open")){
                    roomStatusStr = "已开业";
                }else if (roomStatus.equals("be_renewed")){
                    roomStatusStr = "待续约";
                }else if (roomStatus.equals("awaited_withdrawal")){
                    roomStatusStr = "待撤场";
                }else if (roomStatus.equals("withdrawal")){
                    roomStatusStr = "已撤场";
                }
            }
            detailExp.setRoomStatus(roomStatusStr);

            detailExp.setReferOpenTime(item.getReferOpenTime());
            detailExp.setReferCloseTime(item.getReferCloseTime());
            BigDecimal referOpenLength = item.getReferOpenLength();
            detailExp.setReferOpenLength(referOpenLength!=null ? referOpenLength.toString() : null);

            detailExp.setReferFlowOpenTime(item.getReferFlowOpenTime());
            detailExp.setReferFlowCloseTime(item.getReferFlowCloseTime());
            BigDecimal referFlowOpenLength = item.getReferFlowOpenLength();
            detailExp.setReferFlowOpenLength(referFlowOpenLength!=null ? referFlowOpenLength.toString() : null);

            detailExp.setElectricityOpenTime(item.getElectricityOpenTime());
            detailExp.setElectricityCloseTime(item.getElectricityCloseTime());
            BigDecimal electricityOpenLength = item.getElectricityOpenLength();
            detailExp.setElectricityOpenLength(electricityOpenLength!=null ? electricityOpenLength.toString() : null);

            detailExp.setFlowOpenTime(item.getFlowOpenTime());
            detailExp.setFlowCloseTime(item.getFlowCloseTime());
            BigDecimal flowOpenLength = item.getFlowOpenLength();
            detailExp.setFlowOpenLength(flowOpenLength!=null ? flowOpenLength.toString() : null);

            String storeStatusStr = "";
            Integer storeStatus = item.getStoreStatus();
            if (storeStatus!=null){
                if (storeStatus==1){
                    storeStatusStr = "异常";
                }else if (storeStatus==0){
                    storeStatusStr = "正常";
                }
            }
            detailExp.setStoreStatus(storeStatusStr);

            expResp.add(detailExp);
        }

        try {
            WriteCellStyle headWriteCellStyle = new WriteCellStyle();
            //内容策略
            WriteCellStyle contentWriteCellStyle = new WriteCellStyle();
            //设置水平居中
            contentWriteCellStyle.setHorizontalAlignment(HorizontalAlignment.CENTER);
            HorizontalCellStyleStrategy horizontalCellStyleStrategy = new HorizontalCellStyleStrategy(headWriteCellStyle, contentWriteCellStyle);
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            String fileName = "开闭店管理明细导出报表";
            response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(fileName, "UTF-8") + ".xlsx");

            ExcelWriter excelWriter = EasyExcel.write(response.getOutputStream())
                    .registerWriteHandler(horizontalCellStyleStrategy)
                    .build();

            WriteSheet sheet1 = EasyExcel.writerSheet(0, "开闭店管理明细")
                    .head(CommerceStoreManageDetailExp.class)
                    .build();
            excelWriter.write(expResp, sheet1);

            excelWriter.finish();

        } catch (Exception ex) {
            throw new ServiceException("开闭店管理明细数据导出：" + ex.getMessage());
        }
    }
}
