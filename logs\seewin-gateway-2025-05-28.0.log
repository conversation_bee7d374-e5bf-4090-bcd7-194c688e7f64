{"appName":"seewin-gateway","time":"2025-05-28 09:18:15","level":"INFO","class":"com.seewin.GateWayApp","method":"logStartupProfileInfo","line":"662","message":"No active profile set, falling back to 1 default profile: \"default\""}
{"appName":"seewin-gateway","time":"2025-05-28 09:18:20","level":"INFO","class":"com.seewin.GateWayApp","method":"logStarted","line":"56","message":"Started GateWayApp in 7.782 seconds (process running for 9.167)"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:33","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:33","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:36","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:36","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:40","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:34:40","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:24","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:24","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:49","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:35:49","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:36:06","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:36:06","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
{"appName":"seewin-gateway","time":"2025-05-28 09:36:38","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"65","message":"queryParams:{}"}
{"appName":"seewin-gateway","time":"2025-05-28 09:36:38","level":"INFO","class":"com.seewin.gateway.factory.AuthFilterGatewayFilterFactory","method":"lambda$apply$0","line":"66","message":"remoteAddress:/[0:0:0:0:0:0:0:1]:58591|Real-IP:0:0:0:0:0:0:0:1"}
