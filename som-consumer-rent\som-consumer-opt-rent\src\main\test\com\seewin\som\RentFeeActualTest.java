package com.seewin.som;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.metadata.fill.FillConfig;
import com.alibaba.fastjson2.JSONObject;
import com.seewin.som.commerce.provider.CommerceContractProvider;
import com.seewin.som.commerce.resp.CommerceEffectiveContractVo;
import com.seewin.som.rent.provider.RentFeeActualLogProvider;
import com.seewin.som.rent.provider.RentFeeReceivableDetailProvider;
import com.seewin.som.rent.provider.RentFeeReceivableProvider;
import com.seewin.som.rent.req.RentFeeActualDetailListDto;
import com.seewin.som.rent.req.RentFeeReceivableDetailListDto;
import com.seewin.som.rent.req.RentFeeReceivableEditDto;
import com.seewin.som.rent.resp.RentFeeReceivableDetailListVo;
import com.seewin.som.rent.resp.RentFeeReceivableExportCheckExcelVo;
import com.seewin.som.rent.resp.RentFeeReceivableExportCheckListVo;
import com.seewin.som.rent.resp.RentFeeReceivableGetVo;
import com.seewin.som.rent.service.RentFeeActualLogService;
import com.seewin.som.rent.service.RentFeeActualService;
import com.seewin.som.rent.util.BigDecimalUtil;
import com.seewin.som.rent.util.POIUtil;
import com.seewin.som.rent.vo.req.RentFeeActualLogGetReq;
import com.seewin.som.rent.vo.req.RentFeeExemptionGetReq;
import com.seewin.som.rent.vo.req.RentFeeReceivableExpZipReq;
import com.seewin.som.storage.resp.SysFileGetVo;
import com.seewin.util.exception.ServiceException;
import jakarta.servlet.http.HttpServletRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

import static org.mockito.Mockito.when;

/**
 * <p>
 * 优惠减免表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-11-25
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class RentFeeActualTest {
    @Mock
    private HttpServletRequest request;
    private static final String authUser = "f5f577ce-44f1-4dd6-9b75-6d05514e18d5";
    @Autowired
    private RentFeeActualLogService rentFeeActualLogService;

    @Autowired
    private RentFeeActualService rentFeeActualService;



    @Test
    public void backtrack(){
        // 创建一个模拟的 ServletRequestAttributes
        ServletRequestAttributes servletRequestAttributes = new ServletRequestAttributes(request);
        RequestContextHolder.setRequestAttributes(servletRequestAttributes);
        // 设置请求头
        when(request.getHeader("Auth-User")).thenReturn(authUser);
        RentFeeActualLogGetReq getReq=new RentFeeActualLogGetReq();
        getReq.setId(1877239240404856833l);
        rentFeeActualLogService.backtrack(getReq);
    }


    @Test
    public void getSerialNumberTest(){
        LocalDate now = LocalDate.now();
        String yearMonthDay = String.format("%02d%02d%02d",
                now.getYear() % 100,
                now.getMonthValue(),
                now.getDayOfMonth()
        );

        int randomNumber = ThreadLocalRandom.current().nextInt(100000, 1000000);

        String SerialNumber = yearMonthDay + randomNumber;
        System.out.println(SerialNumber);
    }
}
